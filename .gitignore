# See http://help.github.com/ignore-files/ for more about ignoring files.

# docker
docker-compose.override.yml

# compiled output
dist
coverage
tmp
/out-tsc
packages/frontend/public/mockServiceWorker.js
.nx

# dependencies
node_modules

# IDEs and editors
.idea/
.history
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.run/

# IDE - VSCode
.vscode/*
!.vscode/tasks.json
!.vscode/extensions.json
.lh

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

# Next.js
.next

# Config File
packages/frontend/.env
packages/api/.env
packages/chrome-extension/.env
packages/websocket/.env
scripts/.env

.terraform
terraform/dev/*.lock.hcl
terraform/dev/*.tfstate
terraform/staging/*.lock.hcl
terraform/staging/*.tfstate
terraform/prod/*.lock.hcl
terraform/prod/*.tfstate

# storybook
storybook-static
build-storybook.log
packages/frontend/.storybook/packages

# Eslint
packages/frontend/.eslintcache
packages/api/.eslintcache
packages/web-extension/.eslintcache
packages/websocket/.eslintcache
packages/**/.eslintcache
libraries/**/.eslintcache
.stylelintcache

/**/vite-env.d.ts

# Phraseapp
.phrase.yml

.spacedrive

# Scripts
scripts/storybook-reporter/*.json
scripts/storybook-reporter/*.csv

# NX
migrations.json
/.nx

.nx/cache
.nx/workspace-data

# Yarn v4
.pnp.*
.yarn/*
scripts/gitlab/.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

vite.config.*.timestamp*

#sonar
.scannerwork
.qodo

# Cursor
.cursor
