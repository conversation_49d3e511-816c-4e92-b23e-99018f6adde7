---
# Settings for generating changelogs using the GitLab API. See
# https://docs.gitlab.com/ee/api/repositories.html#generate-changelog-data for
# more information.
categories:
  feature: Feature
  fix: Fix
  hotfix: Hotfix
  security: Security
  performance: Performance
  chore: Chore
  deprecated: Deprecated
  removed: Removed
  other: Other
template: |
  {% if categories %}
  {% each categories %}
  ### {{ title }} ({% if single_change %}1 change{% else %}{{ count }} changes{% end %})

  {% each entries %}
  - [{{ title }}]({{ commit.reference }})\
  {% if author.contributor %} by {{ author.reference }}{% end %}\
  {% if commit.trailers.MR %}\
   ([merge request]({{ commit.trailers.MR }}))\
  {% else %}\
  {% if merge_request %}\
   ([merge request]({{ merge_request.reference }}))\
  {% end %}\
  {% end %}

  {% end %}

  {% end %}
  {% else %}
  No changes.
  {% end %}
