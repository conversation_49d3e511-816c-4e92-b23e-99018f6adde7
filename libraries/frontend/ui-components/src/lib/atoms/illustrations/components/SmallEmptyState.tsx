export const SmallEmptyState = () => {
  return (
    <svg
      width="96"
      height="64"
      viewBox="0 0 96 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="ds-component-small-empty-state"
    >
      <circle
        cx="48.3333"
        cy="32"
        r="32"
        fill="url(#paint0_linear_8847_28944)"
      />
      <circle cx="48" cy="32" r="21" fill="url(#paint1_linear_8847_28944)" />
      <g opacity="0.8" filter="url(#filter0_d_8847_28944)">
        <g clipPath="url(#clip0_8847_28944)">
          <rect
            x="7.84082"
            y="23.1573"
            width="65"
            height="16"
            rx="2"
            transform="rotate(-7 7.84082 23.1573)"
            fill="white"
          />
          <circle
            cx="17.2524"
            cy="30.0618"
            r="4.5"
            transform="rotate(-7 17.2524 30.0618)"
            fill="#E4EBE8"
          />
          <rect
            x="23.3079"
            y="26.0439"
            width="32"
            height="3"
            rx="1.5"
            transform="rotate(-7 23.3079 26.0439)"
            fill="#E4EBE8"
          />
          <rect
            x="23.978"
            y="31.5029"
            width="46"
            height="1"
            rx="0.5"
            transform="rotate(-7 23.978 31.5029)"
            fill="#E4EBE8"
          />
        </g>
        <rect
          x="7.56222"
          y="22.9397"
          width="65.5"
          height="16.5"
          rx="2.25"
          transform="rotate(-7 7.56222 22.9397)"
          stroke="#D7E0DD"
          strokeWidth="0.5"
        />
      </g>
      <g filter="url(#filter1_d_8847_28944)">
        <rect
          x="23.7012"
          y="31.1119"
          width="64"
          height="16"
          rx="2"
          fill="white"
        />
        <rect
          x="23.4512"
          y="30.8619"
          width="64.5"
          height="16.5"
          rx="2.25"
          stroke="#D7E0DD"
          strokeWidth="0.5"
        />
        <circle cx="32.2012" cy="39.1119" r="4.5" fill="#E4EBE8" />
        <rect
          x="38.7012"
          y="35.8619"
          width="32"
          height="3"
          rx="1.5"
          fill="#E4EBE8"
        />
        <rect
          x="38.7012"
          y="41.3619"
          width="45"
          height="1"
          rx="0.5"
          fill="#E4EBE8"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_8847_28944"
          x="3.56934"
          y="11.9644"
          width="77.0083"
          height="34.3452"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="1" dy="2" />
          <feGaussianBlur stdDeviation="2.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.752 0 0 0 0 0.8 0 0 0 0 0.784 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_8847_28944"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8847_28944"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_8847_28944"
          x="19.2012"
          y="27.6119"
          width="75"
          height="27"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="1" dy="2" />
          <feGaussianBlur stdDeviation="2.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.752 0 0 0 0 0.8 0 0 0 0 0.784 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_8847_28944"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8847_28944"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_8847_28944"
          x1="48.3333"
          y1="0"
          x2="48.3333"
          y2="64"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0" />
          <stop offset="1" stopColor="white" stopOpacity="0.8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_8847_28944"
          x1="48"
          y1="11"
          x2="48"
          y2="53"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F2F5F4" />
          <stop offset="1" stopColor="#E4EBE8" />
        </linearGradient>
        <clipPath id="clip0_8847_28944">
          <rect
            x="7.84082"
            y="23.1573"
            width="65"
            height="16"
            rx="2"
            transform="rotate(-7 7.84082 23.1573)"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
