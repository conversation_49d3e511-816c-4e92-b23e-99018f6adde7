import type { SVGProps } from 'react'

export const DotsGrid2x2 = ({
  width = 16,
  height = 16,
  color,
}: SVGProps<SVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
  >
    <path
      fill={color}
      d="M8.333 15.417a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M8.125 15.417a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-.208-1.042a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
    <path
      fill={color}
      d="M12.5 15.417a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M12.292 15.417a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-.208-1.042a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
    <path
      fill={color}
      d="M8.333 10a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M8.125 10a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-.208-1.042a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
    <path
      fill={color}
      d="M12.5 10a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M12.292 10a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-.208-1.042a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
    <path
      fill={color}
      d="M8.333 4.583a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M8.125 4.583a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0ZM7.917 3.54a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
    <path
      fill={color}
      d="M12.5 4.583a.417.417 0 1 1-.833 0 .417.417 0 0 1 .833 0Z"
    />
    <path
      fill={color}
      fillRule="evenodd"
      d="M12.292 4.583a.208.208 0 1 1-.417 0 .208.208 0 0 1 .417 0Zm-.208-1.042a1.042 1.042 0 1 0 0 2.083 1.042 1.042 0 0 0 0-2.083Z"
      clipRule="evenodd"
    />
  </svg>
)
