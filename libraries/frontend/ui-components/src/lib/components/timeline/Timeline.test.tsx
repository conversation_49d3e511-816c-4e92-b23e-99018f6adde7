import { render, screen } from '@testing-library/react'

import { Timeline } from './Timeline'
import { TimelineElement } from './components/TimelineElement'

describe('Timeline', () => {
  const defaultContent = (
    <Timeline>
      <TimelineElement icon="AdobeIndesign" id="1">
        <div>Content 1</div>
      </TimelineElement>

      <TimelineElement icon="AdobeIndesign" id="2">
        <div>Content 2</div>
      </TimelineElement>
    </Timeline>
  )

  beforeAll(() => {
    // Mock IntersectionObserver
    const mockIntersectionObserver = jest.fn()
    mockIntersectionObserver.mockImplementation(() => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn(),
    }))
    window.IntersectionObserver = mockIntersectionObserver
  })

  it('renders timeline with elements', () => {
    render(defaultContent)

    expect(screen.getByText('Content 1')).toBeInTheDocument()
    expect(screen.getByText('Content 2')).toBeInTheDocument()
  })

  it('renders correct number of timeline elements', () => {
    render(defaultContent)

    const timelineElements = document.querySelectorAll(
      '.vertical-timeline-element'
    )
    expect(timelineElements).toHaveLength(2)
  })

  it('renders icons for each timeline element', () => {
    render(defaultContent)

    const icons = document.querySelectorAll('.vertical-timeline-element-icon')
    expect(icons).toHaveLength(2)
  })

  it('renders with correct CSS class', () => {
    render(<Timeline dataTestId="timeline">{defaultContent}</Timeline>)

    expect(screen.getByTestId('timeline')).toHaveClass('ds-component-timeline')
  })

  it('renders error state correctly', () => {
    render(
      <Timeline>
        <TimelineElement icon="AdobeIndesign" id="1" isError>
          <div>Error Content</div>
        </TimelineElement>
      </Timeline>
    )

    const icon = document.querySelector(
      '.vertical-timeline-element-icon .ds-component-icon'
    )
    expect(icon).toHaveAttribute(
      'style',
      'background-color: rgb(255, 237, 235);'
    )
  })

  it('renders badge when no icon is provided', () => {
    render(
      <Timeline>
        <TimelineElement id="1">
          <div>Badge Content</div>
        </TimelineElement>
      </Timeline>
    )

    const badge = document.querySelector(
      '.vertical-timeline-element-icon .ds-component-badge'
    )
    expect(badge).toBeInTheDocument()
  })
})
