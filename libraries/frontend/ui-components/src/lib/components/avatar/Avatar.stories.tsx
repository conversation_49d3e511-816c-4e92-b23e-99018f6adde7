import type { Meta, StoryObj } from '@storybook/react'

import { Avatar } from './Avatar'

const meta = {
  title: 'Components/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  args: {
    variant: 'circle',
    size: 'ml',
    color: 'brand',
  },
  argTypes: {
    img: {
      description: 'The URL of the image to display as the avatar.',
    },
    fallbackImg: {
      description:
        'The URL of a fallback image to display when the primary image (`img`) fails to load.',
    },
    variant: {
      description: 'The shape of the avatar. Can be "circle" or "square".',
    },
    size: {
      description:
        'The size of the avatar. Available sizes are "s", "m", "ml", "xl", and "xxl".',
    },
    initials: {
      description:
        'Initials or short text to display in the avatar when there is no image.',
    },
    color: {
      description:
        'The background color of the avatar. Can be "brand" or "light".',
    },
  },
} satisfies Meta<typeof Avatar>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    initials: 'AB',
  },
}

export const Sizes: Story = {
  args: {
    initials: 'AB',
  },
  render: args => (
    <div className="flex gap-6 items-end">
      <div className="flex flex-col items-center">
        <Avatar {...args} size="xs" />
        <span className="text-sm">Extra-small</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="s" />
        <span className="text-sm">Small</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="m" />
        <span className="text-sm">Medium</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="ml" />
        <span className="text-sm">Medium large</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="xl" />
        <span className="text-sm">X large</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="xxl" />
        <span className="text-sm">XX large</span>
      </div>
    </div>
  ),
}

export const Initials: Story = {
  args: {
    initials: 'AB',
  },
}

export const Icons: Story = {
  args: {
    icon: 'Check',
    variant: 'square',
  },
  render: args => (
    <div className="flex gap-6 items-end">
      <div className="flex flex-col items-center">
        <Avatar {...args} size="xs" />
        <span className="text-sm">Extra-small</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="s" />
        <span className="text-sm">Small</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="m" />
        <span className="text-sm">Medium</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="ml" />
        <span className="text-sm">Medium large</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="xl" />
        <span className="text-sm">X large</span>
      </div>

      <div className="flex flex-col items-center">
        <Avatar {...args} size="xxl" />
        <span className="text-sm">XX large</span>
      </div>
    </div>
  ),
}

export const Image: Story = {
  args: {
    img: 'https://picsum.photos/id/237/80',
  },
}

export const FallbackImage: Story = {
  args: {
    img: 'https://does.not.exist',
    fallbackImg: 'https://picsum.photos/id/237/80',
  },
}

export const Square: Story = {
  args: {
    variant: 'square',
    img: 'https://picsum.photos/id/237/80',
  },
}

export const Light: Story = {
  args: {
    color: 'light',
    initials: 'AB',
  },
}

export const Brand: Story = {
  args: {
    color: 'brand',
    initials: 'AB',
  },
}
