import { forwardRef } from 'react'

import { useModalContext } from '../providers/ModalContext'
import { IconButton } from '../../iconButton/IconButton'

export const ModalClose = forwardRef(() => {
  const { setOpen, dataTestId } = useModalContext()

  return (
    <div
      className="ds-component-modal-close flex justify-center items-center"
      data-modal-hide="default-modal"
    >
      <IconButton
        icon="Xmark"
        size="large"
        onClick={() => setOpen(false)}
        dataTestId={dataTestId && `${dataTestId}-close`}
      />
    </div>
  )
})
