import type { Meta, StoryObj } from '@storybook/react'

import { LazyImage } from './LazyImage'
import {
  LAZY_IMAGE_DATA_TEST_ID,
  LEAD_CATEGORY_COMPANY_SRC_STORIES,
} from './mocks'

// --------------------------------------------
// Story configuration 🔻
// --------------------------------------------

const meta: Meta = {
  component: LazyImage,
  tags: ['autodocs', 'lazy-image'],
  argTypes: {
    src: {
      name: 'src',
      description: 'The src of the image',
      control: {
        type: 'text',
      },
    },
    alt: {
      name: 'alt',
      description: 'The alt of the image',
      control: {
        type: 'text',
      },
    },
    width: {
      name: 'width',
      description: 'The width in rem',
      control: {
        type: 'number',
      },
    },
    height: {
      name: 'height',
      description: 'The height in rem',
      control: {
        type: 'number',
      },
    },
  },
  render: ({ dataTestId, src, alt, width, height }) => (
    <>
      <div>
        On devtools change network condition to 3G, If you want to test the lazy
      </div>
      <LazyImage
        dataTestId={dataTestId}
        src={src}
        alt={alt}
        height={height}
        width={width}
      />
    </>
  ),
}

export default meta

type Story = StoryObj<typeof LazyImage>

// --------------------------------------------
// Stories 🔻
// --------------------------------------------

const IMG_SIZE = {
  WIDTH: 26.625,
  HEIGHT: 9.563,
}
export const Default: Story = {
  args: {
    dataTestId: LAZY_IMAGE_DATA_TEST_ID,
    src: LEAD_CATEGORY_COMPANY_SRC_STORIES,
    alt: 'lead-category-company-stories',
    width: IMG_SIZE.WIDTH,
    height: IMG_SIZE.HEIGHT,
  },
}

export const SmallSize: Story = {
  args: {
    dataTestId: LAZY_IMAGE_DATA_TEST_ID,
    src: LEAD_CATEGORY_COMPANY_SRC_STORIES,
    alt: 'lead-category-company-stories',
    width: IMG_SIZE.WIDTH / 2,
    height: IMG_SIZE.HEIGHT / 2,
  },
}

export const BigSize: Story = {
  args: {
    dataTestId: LAZY_IMAGE_DATA_TEST_ID,
    src: LEAD_CATEGORY_COMPANY_SRC_STORIES,
    alt: 'lead-category-company-stories',
    width: IMG_SIZE.WIDTH * 2,
    height: IMG_SIZE.HEIGHT * 2,
  },
}
