import type { InputHTMLAttributes, Ref } from 'react'
import { forwardRef } from 'react'

import './radio.scss'

export type RadioProps = {
  id: string
  label: string
} & InputHTMLAttributes<HTMLInputElement>

export const Radio = forwardRef(
  ({ id, label, ...inputProps }: RadioProps, ref: Ref<HTMLInputElement>) => {
    return (
      <div className="ds-component-radio flex items-center">
        <input {...inputProps} type="radio" id={id} ref={ref} />
        <label htmlFor={id} className="ml-2 label-s font-medium">
          {label}
        </label>
      </div>
    )
  }
)
