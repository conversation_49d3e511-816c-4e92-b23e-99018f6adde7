/// <reference types="chrome" />

import type {
  BaseQueryApi,
  <PERSON>tchArg<PERSON>,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/dist/query/react'
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { fetchAuthSession } from 'aws-amplify/auth'

import { AppName, HTTP_STATUS_LOCKED } from '@getheroes/shared'
import { publicRoutes } from '@internals/hooks/useRoute'

import packageJson from '../../../../../../package.json'

const BLOCKED_ROUTE = publicRoutes.blockedPage.path

const baseQuery = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: NonNullable<unknown>
) => {
  const getHeaderValue = (): string => {
    const appName = import.meta.env['VITE_APP_NAME']

    const isChromeExtensionContext = appName === AppName.EXTENSION

    if (isChromeExtensionContext) {
      const manifest = chrome.runtime.getManifest()
      return `${appName}:${manifest.version}`
    }

    return `${appName}:${packageJson.version}`
  }

  const isChromeExtensionContext =
    import.meta.env['VITE_APP_NAME'] === AppName.EXTENSION

  const baseQuery = fetchBaseQuery({
    baseUrl: import.meta.env['VITE_API_URL'],
    prepareHeaders: async headers => {
      const session = await fetchAuthSession()
      const token = session?.tokens?.idToken?.toString()
      if (token) {
        headers.set('Authorization', `Bearer ${token}`)
        headers.set('Content-Type', 'application/json; charset=utf-8')
      }
      headers.set('X-Zeliq-App', getHeaderValue())
      return headers
    },
    timeout: 120000,
  })
  const result = await baseQuery(args, api, extraOptions)

  if (
    result?.error &&
    (result?.error as FetchBaseQueryError).status === HTTP_STATUS_LOCKED
  ) {
    if (isChromeExtensionContext) {
      await chrome.storage.local.set({ isUserBlocked: true })
    } else {
      if (window.location.pathname !== BLOCKED_ROUTE) {
        window.location.href = BLOCKED_ROUTE
      }
    }
  }

  return result
}

export default baseQuery

/**
 * Create a base API to inject endpoints into elsewhere.
 * Components using this API should import from the injected site,
 * in order to get the appropriate types,
 * and to ensure that the file injecting the endpoints is loaded
 */
export const api = createApi({
  /**
   * `reducerPath` is optional and will not be required by most users.
   * This is useful if you have multiple API definitions,
   * e.g. where each has a different domain, with no interaction between endpoints.
   * Otherwise, a single API definition should be used in order to support tag invalidation,
   * among other features
   */
  reducerPath: 'api',
  /**
   * A bare-bones base query would just be `baseQuery: fetchBaseQuery({ baseUrl: '/' })`
   */
  baseQuery,
  /**
   * Tag types must be defined in the original API definition
   * for any tags that would be provided by injected endpoints
   */
  tagTypes: [
    // cockpit feature
    'Tableau',
    // settings feature
    'Aircall',
    'Integration',
    'Hubspot',
    // lead feature
    'Company',
    'Contact',
    'LeadView',
    'Activity',
    'SDRMetrics',
    'Enrichment',
    'SearchFilters',
    'ExternalLeadSearch',
    // task feature
    'Task',
    'Gmail',
    // features
    'Organization',
    'Auth',
    'Template',
    'Note',
    'Record',
    'Personality',
    'Domain',
    'Sequence',
    'Subscription',
    'EnrichmentHub',
    'Search',
    // Shared features
    'AsyncAction',
    'Onboarding',
    'User',
    'FeatureGateway',
    'Lead',
    'Mail',
    'Config',
    'LeadImport',
  ],
  /**
   * This api has endpoints injected in adjacent files,
   */
  endpoints: () => ({}),
})
