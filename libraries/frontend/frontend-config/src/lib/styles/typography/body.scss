@mixin body() {
  font-family: var(--fontFamilyBase);
}

.body-xxs-regular {
  @include body();
  font-weight: var(--fontWeightRegular);
  font-size: var(--fontSize01);
  line-height: var(--lineHeight03);
  text-decoration: none;
}

.body-xxs-medium {
  @include body();
  font-weight: var(--fontWeightMedium);
  font-size: var(--fontSize01);
  line-height: var(--lineHeight03);
  text-decoration: none;
}

.body-xs-regular {
  @include body();
  font-weight: var(--fontWeightRegular);
  font-size: var(--fontSize02);
  line-height: var(--lineHeight04);
  text-decoration: none;
}

.body-xs-medium {
  @include body();
  font-weight: var(--fontWeightMedium);
  font-size: var(--fontSize02);
  line-height: var(--lineHeight04);
  text-decoration: none;
}

.body-s-medium {
  @include body();
  font-weight: var(--fontWeightMedium);
  font-size: var(--fontSize03);
  line-height: var(--lineHeight05);
  text-decoration: none;
}

.body-s-regular {
  @include body();
  font-weight: var(--fontWeightRegular);
  font-size: var(--fontSize03);
  line-height: var(--lineHeight05);
  text-decoration: none;
}

.body-m-regular {
  @include body();
  font-weight: var(--fontWeightRegular);
  font-size: var(--fontSize04);
  line-height: var(--lineHeight06);
  text-decoration: none;
}

.body-m-medium {
  @include body();
  font-weight: var(--fontWeightMedium);
  font-size: var(--fontSize04);
  line-height: var(--lineHeight06);
  text-decoration: none;
}

//TODO: add more body styles
//  .body-m-regular-linethrought
//  .body-m-medium
