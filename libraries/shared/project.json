{"name": "shared", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libraries/shared/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libraries/shared/jest.config.ts"}}, "typecheck": {"executor": "nx:run-commands", "options": {"commands": [{"command": "tsc --noEmit -p libraries/shared/tsconfig.lib.json"}]}}}}