import enUS from 'date-fns/locale/en-US'
import fr from 'date-fns/locale/fr'
import { default as i18n } from 'i18next'
import { isLocaleEnum, LocaleEnum } from '../domain/settings/locale.enum'

export const getI18nLocale = () => {
  const language = getLanguage() as string

  return {
    'fr-FR': fr,
    'en-US': enUS,
  }[language]
}

const determineDefaultLanguage = (): LocaleEnum => {
  if (typeof navigator !== 'undefined') {
    const browserLanguage = navigator?.language || navigator?.languages[0]

    if (browserLanguage && isLocaleEnum(browserLanguage)) {
      return browserLanguage as LocaleEnum
    }
  }

  return LocaleEnum.AMERICAN
}

export const getLanguage = (): LocaleEnum => {
  return (
    i18n.language ||
    (typeof window !== 'undefined' && window.localStorage['i18nextLng']) ||
    determineDefaultLanguage()
  )
}

export const isAmericanLanguage = () => {
  return getLanguage() === LocaleEnum.AMERICAN
}
