import { extractSalesNavigatorIdFromUrl } from './extractSalesNavigatorIdFromUrl';

describe('Sales navigator id extraction tests', () => {
    const scenarios = [
        {
            url: 'https://www.linkedin.com/sales/people/ACoAAAFjtGsBIk1ZejoonqYgQeKGY7zF6abi-j8,name,II3k?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAAAFjtGsBIk1ZejoonqYgQeKGY7zF6abi-j8',
            id: 'ACoAAAFjtGsBIk1ZejoonqYgQeKGY7zF6abi-j8'
        },
        {
            url: 'https://www.linkedin.com/sales/people/ACwAADWZNr0BmolQIYbz8VLkWB-EAojD0TcP0EA,CV9L,NAME_SEARCH/',
            id: 'ACwAADWZNr0BmolQIYbz8VLkWB-EAojD0TcP0EA'
        },
        {
            url: 'https://www.linkedin.com/sales/company/1337',
            id: '1337'
        },
        {
            url: 'https://www.linkedin.com/sales/lead/ACwAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U,NAME_SEARCH,OVX3',
            id: 'ACwAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U',
        },
        {
            url: 'https://www.linkedin.com/sales/profile/*********,n4Q_,NAME_SEARCH',
            id: '*********'
        },
        {
            url: 'https://www.linkedin.com/in/ACwAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U',
            id: 'ACwAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U'
        },
        {
            url: 'https://www.linkedin.com/in/ACoAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U',
            id: 'ACoAACKqIO0BfSA9mU7ea23zRwR5L0LObIgol1U'
        },
    ]
    test.each(scenarios)('Should extract $id from sales navigator people url $url', ({ url, id }) => {
        const actual = extractSalesNavigatorIdFromUrl(url);
        expect(actual).toBe(id);
    })

    it('Should return null if no pattern is matched', () => {
        const actual = extractSalesNavigatorIdFromUrl('https://www.linkedin.com/in/john-doe-123456/');
        expect(actual).toBeNull();
    })
});