import type { Company } from '../lead/company'

type MatchingSource = 'HUBSPOT' | 'ALL_LEADS'
type MatchingResult = 'MATCH' | 'NO_MATCH' | 'EMPTY'

export type CompanyMatchingInputDto = {
  matchingId: string // UUID --> mandatory to reconcile the search result
  domain?: string
  linkedinUrl?: string
  name?: string
}[]

export type CompanyWithMatchingSourceAndScoring = {
  company: Company
  matchingSource: MatchingSource
  scoring: {
    score: string
    criterias: [{ name: keyof CompanyMatchingInputDto; result: MatchingResult }]
  }
}

export type MatchingCompanies = {
  source: CompanyMatchingInputDto
  matches: CompanyWithMatchingSourceAndScoring[]
}

export type MatchingCompaniesResponseType = MatchingCompanies[]

export type MatchingCompaniesPayloadType = {
  organizationId: string
  source: CompanyMatchingInputDto
}

export type TransformedMatchingCompaniesResponseType = {
  source: CompanyMatchingInputDto
  matches: Record<string, CompanyWithMatchingSourceAndScoring>
}[]
