import { useToggleSidePanel } from '@root/hooks/useToggleSidePanel';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { RegexClass } from '@getheroes/shared';
import { Button, IconButton, Link, Typography } from '@getheroes/ui';
import { ZeliqLogo } from '@internals/assets/svg/logos/Zeliq/ZeliqLogo';
import { AuthEventEnum, useAuthTracking } from '@internals/features/auth/hooks/useAuthTracking';
import { SignInErrorCard } from '@internals/features/auth/pages/SignInPage/components/SignInErrorCard';
import type { SignInError } from '@internals/features/auth/types/authTypes';
import { publicRoutes } from '@internals/hooks/useRoute';

import { SignInForm } from './components/SignInForm';
import { SsoButtons } from './components/SsoButtons';

export const SignInPage = () => {
  const { t } = useTranslation('auth');
  const { trackAuth } = useAuthTracking();
  const { toggleSidePanel } = useToggleSidePanel();
  const [emailInputValue, setEmailInputValue] = useState<string>('');
  const [error, setError] = useState<SignInError | null>(null);

  const isHubSpot = RegexClass.HUBPOST_APP_URL.test(window.location.href);
  const isHubSpotBaseUrl = RegexClass.HUBPOST_BASE_APP_URL.test(window.location.href);

  return (
    <div className="flex flex-col h-full px-3">
      {/* HEADER */}
      <div className="flex flex-row items-center justify-end w-ful">
        <IconButton icon="Xmark" onClick={() => toggleSidePanel(false)} />
      </div>
      <div className="flex flex-col items-center justify-between h-full gap-8 mb-8 pt-8">
        <div className="flex justify-center w-full">
          <ZeliqLogo width={129} height={48} />
        </div>
        {isHubSpot && !isHubSpotBaseUrl ? (
          <div className="flex flex-col items-center gap-6 w-full">
            <Typography>{t('To login, please go to app.hubspot.com')}</Typography>
            <Button
              variant={'primary'}
              onClick={() => {
                window.open('https://app.hubspot.com', '_self');
              }}>
              {t('Open app.hubspot.com')}
            </Button>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-6 w-full">
            <div className="flex flex-col items-left gap-3 w-full">
              <Typography variant="heading" size="m" align="left">
                {t('Welcome back')}
              </Typography>
              <Typography variant="label" size="s" align="left" color="base-subtle">
                {t('ZELIQ’s Chrome extension let’s you import contacts from LinkedIn to ZELIQ')}
              </Typography>
            </div>

            <SsoButtons />
            <Typography variant="body" size="m" align="center">
              {t('Or')}
            </Typography>
            <SignInForm setError={setError} setEmailInputValue={setEmailInputValue} />
            <SignInErrorCard
              error={error}
              onClickSignUp={() => {
                window.open(`${import.meta.env.VITE_APP_URL}${publicRoutes.signup.path}`, '_blank');
              }}
            />
          </div>
        )}

        {/* FOOTER */}
        <div className="flex flex-col items-center justify-center gap-3">
          <div className="flex flex-row items-center justify-center gap-2">
            <Typography variant="label" size="s" align="center">
              {t("Don't have an account?")}
            </Typography>
            <Link
              color="decorative-brand"
              size="small"
              isExternal
              to={`${import.meta.env.VITE_APP_URL}${publicRoutes.signup.path}`}
              onClick={() => {
                trackAuth(AuthEventEnum.SIGNIN_CLICK_LOGIN_SIGNUP_HERE);
              }}>
              {t('Sign up on Zeliq')}
            </Link>
          </div>
          <Link
            color="decorative-brand"
            size="small"
            isExternal
            to={`${import.meta.env.VITE_APP_URL}${publicRoutes.forgotPassword.path}/${emailInputValue}`}
            onClick={() => {
              trackAuth(AuthEventEnum.LOGIN_CLICK_FORGOTPASSWORD);
            }}>
            {t('Forgot password?')}
          </Link>
        </div>
      </div>
    </div>
  );
};
