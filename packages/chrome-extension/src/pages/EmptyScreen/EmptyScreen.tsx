import { SidePanelLayout } from '@root/components/SidePanelLayout';
import { useTranslation } from 'react-i18next';

import { RegexClass } from '@getheroes/shared';
import { Button } from '@getheroes/ui';
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper';
import { privateRoutes } from '@internals/hooks/useRoute';

import { HubSpotEmptyScreenImage } from './components/HubSpotEmptyScreenImage';
import { HubSpotEmptyScreenMessage } from './components/HubSpotEmptyScreenMessage';
import { LinkedInEmptyScreenImage } from './components/LinkedInEmptyScreenImage';
import { LinkedInEmptyScreenMessage } from './components/LinkedInEmptyScreenMessage';

export const EmptyScreen = () => {
  const { t } = useTranslation('common');
  const isHubSpot = RegexClass.HUBPOST_APP_URL.test(window.location.href);
  return (
    <SidePanelLayout>
      <div className={'flex flex-col gap-2 h-full overflow-hidden'}>
        <Paper hasBorder>{isHubSpot ? <HubSpotEmptyScreenMessage /> : <LinkedInEmptyScreenMessage />}</Paper>
        <div className={'h-full overflow-hidden'}>
          <Paper hasBorder isHeightFull>
            <div className={'flex flex-col items-center justify-center h-full'}>
              {isHubSpot ? <HubSpotEmptyScreenImage /> : <LinkedInEmptyScreenImage />}
            </div>
          </Paper>
        </div>
        <Button
          variant={'primary'}
          iconRight={'ArrowUpRight'}
          dataTestId={'go-to-app-button'}
          onClick={() => {
            const url = `${import.meta.env.VITE_APP_URL}${privateRoutes.landingPage.path}`;
            window.open(url, '_blank');
          }}>
          {t('Go to Zeliq app')}
        </Button>
      </div>
    </SidePanelLayout>
  );
};
