import { Trans, useTranslation } from 'react-i18next';

import { Typography } from '@getheroes/ui';

export const HubSpotEmptyScreenMessage = () => {
  const { t } = useTranslation();

  return (
    <div className={'flex flex-col gap-2'}>
      <Typography variant={'heading'} size={'s'}>
        <Trans
          i18nKey="Try navigate to a <1>profile</1> or <1>company page</1>"
          components={{ 1: <Typography variant={'heading'} size={'s'} color={'decorative-brand'} /> }}
          t={t}
        />
      </Typography>
    </div>
  );
};
