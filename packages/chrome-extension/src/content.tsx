// Entry point for the content script.
// This script is injected into the page by the extension on load.
// It's the first entrypoint for logic that interacts with the page
import { App } from '@root/app/App';
import { initI18n } from '@root/config/i18n';
import { upsertCurrentUrlLocation } from '@utils/chrome';
import { injectStyles, setContainerSingleton } from '@utils/dom';

import { injectShadowDom } from './injector';

import resetLinkedin from './styles/reset-linkedin.css?inline';
import { watchSPANavigation } from './utils/mutation-observer';

const injectZeliqApp = () => {
  // Instantiate current url location
  upsertCurrentUrlLocation();

  // Instantiate and keep only one container for all apps
  const containerSelector = 'zeliq-app';
  setContainerSingleton(containerSelector);

  // Inject Overlay and Sidepanel on load
  injectShadowDom({
    selector: `#${containerSelector}`,
    Component: <App />,
    rootId: 'zeliq-root',
  });
  injectStyles(resetLinkedin, 'zeliq-reset-l-styles');
};

// Applications entrypoint
injectZeliqApp();
watchSPANavigation();
initI18n();
