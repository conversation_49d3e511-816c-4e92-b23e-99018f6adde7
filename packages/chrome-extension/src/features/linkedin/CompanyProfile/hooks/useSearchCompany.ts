import type { ParsedCompany } from '@root/types/parsed-company';
import { generateMatchingId } from '@utils/api.ts';
import { useCallback } from 'react';

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice';
import type { Organization } from '@getheroes/shared';
import { useLazySearchMatchingCompaniesQuery } from '@internals/features/lead/api/companyApi';
import { useTypedSelector } from '@internals/store/store';

export const useSearchCompany = () => {
  const { id: organizationId } = useTypedSelector(selectCurrentUserOrganization) as Organization;

  const [lazyMatchCompanies] = useLazySearchMatchingCompaniesQuery();

  return useCallback(
    async (companyFromDOM: ParsedCompany) => {
      try {
        const matchingId = generateMatchingId(companyFromDOM?.linkedinUrl ?? companyFromDOM.name);

        // 1. Search company matches in Zeliq DB
        const matchingResult = await lazyMatchCompanies({
          organizationId: organizationId ?? '',
          source: [
            {
              matchingId,
              linkedinUrl: companyFromDOM?.linkedinUrl,
              name: companyFromDOM?.name,
            },
          ],
        }).unwrap();

        if (matchingResult && matchingResult.length > 0) {
          const match = Object.values(matchingResult[0].matches);
          const mostScoredCompany =
            match.length > 1 ? match.toSorted((a, b) => +b.scoring.score - +a.scoring.score)[0] : match[0];
          return mostScoredCompany.company;
        }

        // 2. If no company is found, return the company from DOM
        return companyFromDOM;
      } catch (error) {
        console.error('Error searching company from API:', error);
      }
    },
    [organizationId, lazyMatchCompanies],
  );
};
