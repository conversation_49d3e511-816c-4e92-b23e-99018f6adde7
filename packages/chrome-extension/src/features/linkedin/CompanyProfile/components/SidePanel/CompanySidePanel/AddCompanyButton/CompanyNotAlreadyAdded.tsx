import { useAddCompany } from '@root/features/linkedin/CompanyProfile/hooks/useAddCompany';
import { setCurrentCompany, setCompanyLoading } from '@root/store/slices/companyProfileSlice';
import { useAppDispatch } from '@root/store/store';
import type { ParsedCompany } from '@root/types/parsed-company';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Typography } from '@getheroes/ui';
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper';
import { PaperRadiusSize } from '@internals/components/common/dataDisplay/Paper/Paper-export';

export interface CompanyNotAlreadyAddedProps {
  parsedCompany: ParsedCompany;
}

export const CompanyNotAlreadyAdded = ({ parsedCompany }: CompanyNotAlreadyAddedProps) => {
  const { t } = useTranslation('lead');
  const dispatch = useAppDispatch();
  const { addCompany, isLoading } = useAddCompany();

  const onAddCompany = useCallback(async () => {
    const company = await addCompany(parsedCompany);
    if (company) {
      dispatch(setCurrentCompany(company));
    }
  }, [addCompany, parsedCompany, dispatch]);

  return (
    <>
      <Paper hasBorder radiusSize={PaperRadiusSize.XLARGE}>
        <div className={'flex flex-col gap-2'}>
          <Typography variant={'heading'} size={'xs'}>
            {t('Quick Actions')}
          </Typography>
          <div>
            <Button
              dataTestId={'company-side-panel-add-company-btn'}
              onClick={onAddCompany}
              variant={'primary'}
              size={'small'}
              iconLeft={'Building'}
              loading={isLoading}>
              {t('Add company')}
            </Button>
          </div>
        </div>
      </Paper>
      <Paper hasBorder radiusSize={PaperRadiusSize.XLARGE}>
        <div className={'flex flex-col gap-2'}>
          <Typography variant={'heading'} size={'xs'}>
            {t('Leads already in Zeliq')}
          </Typography>
          <Typography>{t('No leads added')}</Typography>
        </div>
      </Paper>
    </>
  );
};
