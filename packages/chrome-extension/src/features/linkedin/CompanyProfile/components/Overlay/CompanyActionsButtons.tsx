import { CompanyAssignationInformation } from '@root/features/linkedin/CompanyProfile/components/Overlay/CompanyAssignationInformation';
import { useAddCompany } from '@root/features/linkedin/CompanyProfile/hooks/useAddCompany';
import { useCompanyAssignationInformation } from '@root/features/linkedin/ContactProfile/hooks/useCompanyAssignationInformation';
import { useToggleSidePanel } from '@root/hooks/useToggleSidePanel';
import { injectShadowDom } from '@root/injector';
import {
  selectCurrentCompany,
  selectIsCompanyLoading,
  setCurrentCompany,
} from '@root/store/slices/companyProfileSlice';
import type { ParsedCompany } from '@root/types/parsed-company';
import { getMostRecentSelector } from '@utils/selectors';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice';
import type { SelectorsType } from '@getheroes/shared';
import { But<PERSON>, Spinner } from '@getheroes/ui';
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper';
import { useAssignCompanies } from '@internals/features/lead/hooks/assignation/useAssignLeads';
import { useCurrentUser } from '@internals/hooks/useCurrentUser';
import { useTypedSelector } from '@internals/store/store';

export const CompanyActionsButtons = () => {
  const { t } = useTranslation('lead');
  const dispatch = useDispatch();
  const company = useTypedSelector(selectCurrentCompany);
  const { currentUser, isCurrentUserAdminOrManager } = useCurrentUser();
  const { isOpenSidePanel, toggleSidePanel } = useToggleSidePanel();
  const organization = useTypedSelector(selectCurrentUserOrganization);
  const isCompanyLoading = useTypedSelector(selectIsCompanyLoading);

  const { isCompanyAlreadyAdded, isCompanyAssignedToCurrentUser } = useCompanyAssignationInformation(
    currentUser,
    company,
  );

  const { addCompany, isLoading: isLoadingAddCompany } = useAddCompany();

  const [assignCompanies, { isLoading: isLoadingAssignCompany }] = useAssignCompanies({
    organizationId: organization?.id || '',
  });

  const handleClickImport = useCallback(async () => {
    if (isCompanyAlreadyAdded) {
      return;
    }
    if (!isOpenSidePanel) {
      toggleSidePanel(true);
    }

    const companyResponse = await addCompany(company as ParsedCompany);
    if (!companyResponse) {
      return;
    }

    dispatch(setCurrentCompany(companyResponse));
  }, [addCompany, company, dispatch, isCompanyAlreadyAdded, isOpenSidePanel, toggleSidePanel]);

  const handleClickAssignToMe = useCallback(async () => {
    if (!currentUser?.id) {
      return;
    }
    let companyId = company.id;
    let companyToAdd = company;
    if (!isOpenSidePanel) {
      toggleSidePanel(true);
    }
    if (!isCompanyAlreadyAdded) {
      const createdCompany = await addCompany(company as ParsedCompany);
      if (!createdCompany) {
        return;
      }
      companyId = createdCompany.id;
      companyToAdd = createdCompany;
    }
    // Optimistic update if the API call is successful
    try {
      const assignResult = await assignCompanies({
        companiesIds: [companyId],
        userIds: [currentUser?.id.toString()],
      });
      if ('data' in assignResult && typeof assignResult.data === 'object') {
        const { assignedUsersIds } = assignResult.data;
        const assignedUser = organization?.members?.find((org: any) => org.member.id === assignedUsersIds[0]);
        const newAssignedUsers = [
          {
            user: {
              id: assignedUser?.member?.id,
              firstName: assignedUser?.member?.firstName,
              lastName: assignedUser?.member?.lastName,
            },
          },
        ];
        dispatch(
          setCurrentCompany({
            ...companyToAdd,
            assignUsers: assignedUser ? newAssignedUsers : null,
          }),
        );
      }
    } catch (error) {
      console.error('Error optimisticaly updating contact assignee', error);
    }
  }, [
    addCompany,
    assignCompanies,
    company,
    currentUser?.id,
    dispatch,
    isCompanyAlreadyAdded,
    isOpenSidePanel,
    organization?.members,
    toggleSidePanel,
  ]);

  const isLoading = !company || isCompanyLoading || isLoadingAddCompany || isLoadingAssignCompany;

  return (
    <div className="px-6 pb-6 drop-shadow-md">
      <Paper>
        {isLoading && (
          <div className="flex h-10 w-full items-center justify-center">
            <Spinner size="small" />
          </div>
        )}
        {!isLoading && (
          <div className="flex flex-row gap-2 items-center justify-between">
            <CompanyAssignationInformation currentUser={currentUser} company={company} translation={t} />
            {!isCompanyAlreadyAdded && (
              <div className={'flex gap-2 items-center'}>
                {isCurrentUserAdminOrManager && (
                  <Button onClick={handleClickImport} loading={isLoadingAddCompany}>
                    {t('Import in all leads')}
                  </Button>
                )}
                <Button
                  variant={'primary'}
                  iconLeft={'Building'}
                  onClick={handleClickAssignToMe}
                  loading={isLoadingAddCompany || isLoadingAssignCompany}>
                  {t('Import & assign to myself')}
                </Button>
              </div>
            )}
            {isCompanyAlreadyAdded && !isCompanyAssignedToCurrentUser && (
              <Button
                variant={'primary'}
                iconLeft={'Building'}
                onClick={handleClickAssignToMe}
                loading={isLoadingAddCompany || isLoadingAssignCompany}>
                {t('Assign to myself')}
              </Button>
            )}
          </div>
        )}
      </Paper>
    </div>
  );
};

export const inject = async ({ selectors }: { selectors: SelectorsType }) => {
  if (!selectors) {
    return;
  }

  injectShadowDom({
    Component: <CompanyActionsButtons />,
    rootId: 'lead-actions-buttons',
    selector: getMostRecentSelector(selectors?.linkedin?.companyProfilePage?.overlay),
    insert: 'insertBefore',
  });
};
