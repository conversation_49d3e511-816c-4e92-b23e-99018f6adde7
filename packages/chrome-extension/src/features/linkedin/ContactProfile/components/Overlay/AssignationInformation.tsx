import { useAssignationInformation } from '@root/features/linkedin/ContactProfile/hooks/useAssignationInformation';
import { selectCurrentContact } from '@root/store/slices/contactProfileSlice';
import { useTypedSelector } from '@root/store/store';
import { clsx } from 'clsx';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice';
import type { User } from '@getheroes/shared';
import { Icon, Typography } from '@getheroes/ui';

export const AssignationInformation = () => {
  const { t } = useTranslation('lead');
  const contact = useSelector(selectCurrentContact);
  const currentUser = useTypedSelector(selectCurrentUser) as User;

  const { iconName, assignationBackgroundColor, assignationIconColor, getAssignationWording } =
    useAssignationInformation(currentUser, contact);

  const { key, options = {} } = getAssignationWording();

  return (
    <div className={clsx('flex flex-row gap-1 p-2 items-center rounded-m', assignationBackgroundColor)}>
      <Icon name={iconName} color={assignationIconColor} />
      <Typography variant="label" size="s">
        {t(key, options)}
      </Typography>
    </div>
  );
};
