import { api } from '@getheroes/frontend/config/api';
import type { SelectorsType } from '@getheroes/shared';

export const selectorsApi = api.injectEndpoints({
  endpoints: builder => ({
    getSelectors: builder.query<SelectorsType, null | void>({
      query: () => '/extension/selectors',
    }),
  }),
});

export const { useGetSelectorsQuery } = selectorsApi;

export const { getSelectors } = selectorsApi.endpoints;
