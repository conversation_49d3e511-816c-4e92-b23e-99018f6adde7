import { useCallback, useEffect } from 'react';

export const useLazyNavigationObserver = () => {
  const observeNavigation = useCallback((onNavigation: () => void) => {
    let lastUrl = location.href;
    const observer = new MutationObserver(() => {
      const currentUrl = location.href;
      if (currentUrl !== lastUrl) {
        lastUrl = currentUrl;
        onNavigation();
      }
    });
    observer.observe(document, { subtree: true, childList: true });

    return {
      disconnect: () => observer.disconnect(),
    };
  }, []);

  return { observeNavigation };
};

/**
 * Use the navigation observer to call a function when the URL changes
 * @param onNavigation - The function to call when the URL changes
 */
export const useOnNavigation = (onNavigation: () => void) => {
  const { observeNavigation } = useLazyNavigationObserver();
  useEffect(() => {
    const { disconnect } = observeNavigation(onNavigation);
    return () => {
      disconnect();
    };
  }, [observeNavigation, onNavigation]);
};
