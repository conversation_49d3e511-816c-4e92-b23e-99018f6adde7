const { join, resolve } = require('path')

const { createGlobPatternsForDependencies } = require('@nx/react/tailwind')

/** @type {import('tailwindcss').Config} */
module.exports = {
  presets: [
    require('../../libraries/frontend/frontend-config/src/lib/tailwind.config'),
  ],
  content: [
    join(
      __dirname,
      'src/*-sidebar/{features,components,styles}/**/*!(*.stories|*.spec).{ts,tsx,html,scss}'
    ),
    join(
      __dirname,
      'src/overlay/{features,components,styles}/**/*!(*.stories|*.spec).{ts,tsx,html,scss}'
    ),
    join(
      __dirname,
      'src/banner/{features,components,styles}/**/*!(*.stories|*.spec).{ts,tsx,html,scss}'
    ),
    join(
      resolve(__dirname, '../frontend/'),
      'src/{features,components,styles}/**/*!(*.stories|*.spec).{ts,tsx,html,scss}'
    ),
    ...createGlobPatternsForDependencies(__dirname),
  ],
}
