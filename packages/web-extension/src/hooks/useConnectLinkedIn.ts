import isEmpty from 'lodash/isEmpty'
import { useEffect } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useTypedSelector } from '@internals/store/store'

import { useMineConnectedAccountQuery } from '../services/integrationApi'
import { usePatchLinkedInCookiesMutation } from '../services/linkedInApi'
import { updateCookies } from '../utils/cookies'

/**
 * Custom hook to connect to LinkedIn
 * @returns {Object} clearAllAlarms, getAllAlarms
 */
export const useConnectLinkedIn = () => {
  const { id: organizationId } =
    useTypedSelector(selectCurrentUserOrganization) || {}
  const [sendLinkedInCookies] = usePatchLinkedInCookiesMutation()
  const {
    data: accountList = [],
    isLoading,
    isFetching,
  } = useMineConnectedAccountQuery()

  /**
   * Clear all alarms
   */
  const clearAllAlarms = async () => {
    await chrome.alarms.clearAll()
  }

  /**
   * Get all alarms
   */
  const getAllAlarms = async () => {
    return chrome.alarms.getAll()
  }

  const alarmLinkedInListener = async () => {
    return chrome.alarms.onAlarm.addListener(async alarm => {
      if (alarm.name === 'updateCookies') {
        if (!isEmpty(accountList)) {
          const isConnectToLinkedIn = accountList.some(
            account => account.type === 'linkedin' && account.isConnected
          )
          if (isConnectToLinkedIn) {
            const response = await updateCookies()
            if (organizationId && response.cookies) {
              sendLinkedInCookies({
                organizationId,
                cookies: response.cookies,
              })
            }
          }
        }
      }
    })
  }

  /**
   * Create an alarm to update cookies every 2 hours
   */
  useEffect(() => {
    if (!organizationId || isLoading || isFetching) {
      return
    }

    const createAlarmCookies = async () => {
      await chrome.alarms.create('updateCookies', {
        periodInMinutes: 120,
      })
      await alarmLinkedInListener()
    }

    createAlarmCookies()
  }, [organizationId, sendLinkedInCookies, isLoading, isFetching])

  return {
    clearAllAlarms,
    getAllAlarms,
  }
}
