import { createRoot } from 'react-dom/client'
import { sendMessage } from 'webext-bridge/content-script'
import browser from 'webextension-polyfill'

import {
  OverlayOperation,
  TargetFrame,
} from '../../../types/declaration/cross-frame'
import { renderOverlayApp } from '../../apps/linkedin/OverlayApp'
import { qs } from '../../utils/querySelector'
import { randomizeId } from '../../utils/randomizeId'

export const initializeSalesNavigatorSearchOverlay = async () => {
  const zeliqSectionId = randomizeId('zeliq-sales-navigator-search-overlay')
  const searchResultsSelector = 'main [data-x--search-results-container]'

  const addZeliqSection = async () => {
    if (qs(`#${zeliqSectionId}`)) {
      return
    }

    const elSearchResults = qs(searchResultsSelector)
    const elListWrapper = qs('ol.artdeco-list', elSearchResults)

    if (!elSearchResults || !elListWrapper) {
      return
    }

    elSearchResults.style.height = 'calc(100vh - 300px)'
    elSearchResults.insertAdjacentHTML(
      'beforebegin',
      `<div
        id="${zeliqSectionId}"
        class="${elListWrapper.className} m4 mr6 p2 pb0"
      />`
    )

    renderOverlayApp({
      root: createRoot(qs(`#${zeliqSectionId}`) as HTMLElement),
      frameUrl: browser.runtime.getURL('dist/overlay/index.html'),
      height: '42px',
    })

    // Send toggle message to the overlay when a checkbox is clicked
    qs('main [data-x--search-results-container] ol')?.addEventListener(
      'click',
      async e => {
        const elCheckboxLabel = (e.target as HTMLElement).closest(
          'label[for^="multi-selector-checkbox"]'
        )
        const elLink = (e.target as HTMLElement)
          .closest('[data-x-deferred-did-intersect]')
          ?.querySelector(
            'a[data-anonymize="headshot-photo"]'
          ) as HTMLAnchorElement
        const linkedinUrl = elLink?.href?.split('?')[0]
        const data = linkedinUrl
          ? decodeURIComponent(linkedinUrl)
          : ''

        if (elCheckboxLabel && elLink) {
          sendMessage(
            'cross-frame',
            {
              targetFrame: TargetFrame.OVERLAY,
              operation: OverlayOperation.TOGGLE_CONTACT,
              data,
            },
            'background'
          )
          chrome.runtime.sendMessage(
            {
              targetFrame: TargetFrame.SIDEBAR,
              operation: OverlayOperation.TOGGLE_CONTACT,
              data,
            },
            response => {
              if (chrome.runtime.lastError) {
                // eslint-disable-next-line no-console
                console.error(
                  'Error sending message:',
                  chrome.runtime.lastError
                )
              }
            }
          )
        }
      },
      true
    )
  }

  setInterval(() => {
    if (location.pathname.startsWith('/sales/search/people')) {
      addZeliqSection()
    }
  }, 200)
}
