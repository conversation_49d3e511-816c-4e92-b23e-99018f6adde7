import { useCallback } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useLazyGetCompanyQuery } from '@internals/features/lead/api/companyApi'
import { useLazyGetContactQuery } from '@internals/features/lead/api/contactApi'
import { useTypedSelector } from '@internals/store/store'

import { useOnCrossFrameMessages } from '../../hooks/useOnCrossFrameMessage'
import { useExtension } from '../../providers/extension/extension'
import type { OverlayCallback } from '../../types/declaration/cross-frame'
import {
  OverlayOperation,
  TargetFrame,
} from '../../types/declaration/cross-frame'

export const useUpdateOnMessageForProfileOverlay = ({
  setAreActionsDisabled,
}: {
  setAreActionsDisabled: (val: boolean) => void
}) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { setFullyMatchedContacts, setFullyMatchedCompanies } = useExtension()
  const [getContact] = useLazyGetContactQuery()
  const [getCompany] = useLazyGetCompanyQuery()

  useOnCrossFrameMessages({
    target: TargetFrame.OVERLAY,
    onMessage: useCallback<OverlayCallback>(
      async ({ operation, data }) => {
        if (
          operation === OverlayOperation.CONTACT_ASSIGNED ||
          operation === OverlayOperation.CONTACT_CREATED
        ) {
          const contactId =
            operation === OverlayOperation.CONTACT_ASSIGNED
              ? data
              : operation === OverlayOperation.CONTACT_CREATED
                ? data.createdId
                : ''
          const getContactResult = await getContact({
            contactId,
            organizationId,
          })

          if ('data' in getContactResult && getContactResult.data) {
            setFullyMatchedContacts([getContactResult.data])
          }
          setAreActionsDisabled(false)
        }

        if (
          operation === OverlayOperation.COMPANY_ASSIGNED ||
          operation === OverlayOperation.COMPANY_CREATED
        ) {
          const companyId =
            operation === OverlayOperation.COMPANY_ASSIGNED
              ? data
              : operation === OverlayOperation.COMPANY_CREATED
                ? data.createdId
                : ''
          const getCompanyResult = await getCompany({
            companyId,
            organizationId,
          })

          if ('data' in getCompanyResult && getCompanyResult.data) {
            setFullyMatchedCompanies([getCompanyResult.data])
          }
          setAreActionsDisabled(false)
        }
      },
      [
        getCompany,
        getContact,
        organizationId,
        setAreActionsDisabled,
        setFullyMatchedCompanies,
        setFullyMatchedContacts,
      ]
    ),
  })
}
