import type browser from 'webextension-polyfill'

import type { RawParsedContact } from '../../../types/declaration/parsed-contact'
import { supportedContexts } from '../hooks/useCompareAndParse'
import { getCachedParseResult } from '../utils/getCachedParseResult'
import { setCachedParseResult } from '../utils/setCachedParseResult'

function parsePeopleSearchCS(): RawParsedContact[] {
  // helpers, can't be shared between content-scripts
  const qs = <T extends HTMLElement = HTMLElement>(
    selector: string,
    context?: HTMLElement | null
  ): T | null => {
    return (context || document.body).querySelector(selector)
  }
  const qsa = <T extends HTMLElement = HTMLElement>(
    selector: string,
    context?: HTMLElement | null
  ): T[] => {
    return Array.from((context || document.body).querySelectorAll(selector))
  }

  return qsa(
    'main [data-x--search-results-container] [data-x-deferred-did-intersect]'
  ).map(elItem => {
    const fullName =
      qs('[data-anonymize="person-name"]', elItem)?.innerText?.trim() || ''
    const elJobTitle = qs('[data-anonymize="title"]', elItem)
    const jobTitle = elJobTitle?.innerText?.trim() || ''
    const elCompanyName = qs('[data-anonymize="company-name"]', elItem)
    const companyName = elCompanyName
      ? elCompanyName.innerText?.trim() || ''
      : elJobTitle?.parentElement?.innerHTML
          ?.replace(/<(\w+)\W.*?<\/\1>/gms, '')
          ?.trim() || ''
    const address =
      qs('[data-anonymize="location"]', elItem)?.innerText?.trim() || ''
    const profilePic = qs<HTMLImageElement>(
      'img[data-anonymize="headshot-photo"]',
      elItem
    )?.src
    const salesNavigatorUrlEncoded =
      qs<HTMLAnchorElement>(
        'a[data-anonymize="headshot-photo"]',
        elItem
      )?.href?.split('?')[0] || ''
    const salesNavigatorUrl = salesNavigatorUrlEncoded
      ? decodeURIComponent(salesNavigatorUrlEncoded)
      : ''

    return {
      id: salesNavigatorUrl,
      fullName,
      jobTitle,
      emails: [],
      phones: [],
      addresses: [address],
      urls: salesNavigatorUrl ? [salesNavigatorUrl] : [],
      // filter out default profile pic which starts with data:image
      picture: profilePic?.startsWith('http') ? profilePic : '',
      ...(companyName ? { company: { name: companyName } } : {}),
    }
  })
}

export async function parseSalesNavigatorSearch(
  tabId: number,
  { tabs, scripting: { executeScript } }: typeof browser
): Promise<RawParsedContact[]> {
  const tabUrl = new URL((await tabs.get(tabId)).url || '')
  const pageSlug =
    supportedContexts['sales-navigator-search'].buildCacheKey(tabUrl)
  const target = { tabId }

  if (!pageSlug) {
    throw new Error('InvalidPageSlug')
  }

  // 0. Check whether the page has already been parsed
  const [{ result: cachedResult }] = await getCachedParseResult({
    executeScript,
    tabId,
    pageSlug,
  })

  if (cachedResult) {
    return cachedResult
  }

  // 1. Make sure no .dummy-text placeholders are left in the document
  await executeScript({
    target,
    func: async () => {
      return await new Promise((resolve, reject) => {
        let timeout: ReturnType<typeof setTimeout>
        let elSearchResults = document.querySelector(
          'main [data-x--search-results-container]'
        )
        elSearchResults?.scrollTo(0, 0)

        const interval = setInterval(async () => {
          if (!elSearchResults) {
            // If the search results container is not found, try to find it again, before launching the scroll
            elSearchResults = document.querySelector(
              'main [data-x--search-results-container]'
            )
            if (!elSearchResults) {
              return
            }
          }
          if (
            elSearchResults &&
            !elSearchResults.querySelector('li .dummy-text')
          ) {
            clearInterval(interval)
            clearTimeout(timeout)
            return resolve({ success: true })
          }
          // Scroll until experiences are parsable
          elSearchResults?.scrollBy(0, 500)
        }, 250)

        // We don't want this check to run indefinitely if something goes wrong
        setTimeout(() => {
          clearInterval(interval)
          reject(new Error('ProfileCheckTimedOut'))
        }, 6000)
      })
    },
    // ignore timeout errors
    // eslint-disable-next-line no-console
  }).catch(console.error)

  // 2. Parse the page
  const [{ result: profiles }] = await executeScript({
    target,
    func: parsePeopleSearchCS,
  })
  // 3. scroll back to top
  await executeScript({
    target,
    func: () => {
      // scroll back to top
      setTimeout(
        () =>
          document
            .querySelector('main [data-x--search-results-container]')
            ?.scrollTo(0, 0),
        100
      )
    },
  })

  // 4. Cache parse result
  profiles &&
    (await setCachedParseResult({
      executeScript,
      tabId,
      pageSlug,
      profiles,
    }))

  return profiles
}
