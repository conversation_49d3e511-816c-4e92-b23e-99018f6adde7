import { resolve } from 'node:path'

import { bgCyan, black } from 'kolorist'
import { normalizePath } from 'vite'

export const port = parseInt(process.env.PORT || '') || 3303
export const r = (...args: string[]) =>
  // TODO: normalizePath is probably useless here
  normalizePath(resolve(__dirname, '..', ...args))
export const isDev = process.env.NODE_ENV !== 'production'
export const isFirefox = process.env.EXTENSION === 'firefox'

export function log(name: string, message: string) {
  console.log(black(bgCyan(` ${name} `)), message)
}
