import { registerAs } from '@nestjs/config'
import {
  IsBoolean,
  IsIn,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { validateConfigFromClass } from './config.validation'
import dotenv from 'dotenv'
import { Type } from 'class-transformer'

dotenv.config()

class BullBoardConfig {
  @IsBoolean()
  isEnabled: boolean

  @IsString()
  @IsOptional()
  login: string

  @IsString()
  @IsOptional()
  password: string
}

export class BullConfig {
  @IsBoolean()
  removeOnComplete: boolean

  @IsNumber()
  // in seconds
  removeOnFailAge: number

  @IsNumber()
  attempts: number

  @IsIn(['exponential', 'fixed'])
  backoffType: 'exponential' | 'fixed'

  @IsNumber()
  // in milliseconds
  backoffDelay: number

  @ValidateNested()
  @Type(() => BullBoardConfig)
  bullBoardConfig: BullBoardConfig
}

export default registerAs('bull', () => {
  const config: BullConfig = {
    removeOnComplete: true,
    removeOnFailAge: 60 * 60 * 24 * 7, // 7 days
    attempts: 8,
    backoffType: 'exponential',
    backoffDelay: 30000, // 30s
    bullBoardConfig: {
      isEnabled: process.env.BULL_BOARD_ENABLED === 'true',
      login: process.env.BULL_BOARD_LOGIN,
      password: process.env.BULL_BOARD_PASSWORD,
    },
    // Explanation of the backoff algorithm (2 ^ (attempts - 1) * delay)
    // attempt 1: 2 ^ (1 - 1) * 30000 = 30s
    // attempt 2: 2 ^ (2 - 1) * 30000 = 1m
    // attempt 3: 2 ^ (3 - 1) * 30000 = 2m
    // attempt 4: 2 ^ (4 - 1) * 30000 = 4m
    // attempt 5: 2 ^ (5 - 1) * 30000 = 8m
    // attempt 6: 2 ^ (6 - 1) * 30000 = 16m
    // attempt 7: 2 ^ (7 - 1) * 30000 = 32m
    // attempt 8: 2 ^ (8 - 1) * 30000 = 1h 4m (max)
  }

  return validateConfigFromClass(BullConfig, config)
})
