import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddUserHubspotutkColumn1724833941574
  implements MigrationInterface
{
  name = 'AddUserHubspotutkColumn1724833941574'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "hubspotutk" character varying`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "hubspotutk"`)
  }
}
