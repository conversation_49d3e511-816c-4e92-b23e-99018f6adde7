import { MigrationInterface, QueryRunner } from 'typeorm'

export class LeadsCountersOptimizations1747906854478
  implements MigrationInterface
{
  name = 'LeadsCountersOptimizations1747906854478'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "idx_companies_orgid_availws_archat_id" ON "companies" ("organization_id", "available_in_workspace", "archived_at", "id") `
    )
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "idx_companies_users_user_id_company_id_id" ON "companies_users" ("user_id", "company_id", "id") `
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."idx_companies_orgid_availws_archat_id"`
    )
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."idx_companies_users_user_id_company_id_id"`
    )
  }
}
