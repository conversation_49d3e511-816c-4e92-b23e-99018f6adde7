import { MigrationInterface, QueryRunner } from 'typeorm'

export class SequencesKpi1695834704252 implements MigrationInterface {
  name = 'SequencesKpi1695834704252'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sequences" ADD "mails_total" integer`)
    await queryRunner.query(
      `ALTER TABLE "sequences" ADD "mails_opened" integer`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" ADD "links_clicked" integer`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" ADD "mails_replied" integer`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" ADD "mails_bounced" integer`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" ADD "mails_unsubscribed" integer`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "sequences" DROP COLUMN "mails_unsubscribed"`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" DROP COLUMN "mails_bounced"`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" DROP COLUMN "mails_replied"`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" DROP COLUMN "links_clicked"`
    )
    await queryRunner.query(
      `ALTER TABLE "sequences" DROP COLUMN "mails_opened"`
    )
    await queryRunner.query(`ALTER TABLE "sequences" DROP COLUMN "mails_total"`)
  }
}
