import { MigrationInterface, QueryRunner } from "typeorm";

export class CustomEmail1699873439777 implements MigrationInterface {
    name = 'CustomEmail1699873439777'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sequences_contact" ADD "custom_email" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sequences_contact" DROP COLUMN "custom_email"`);
    }
}
