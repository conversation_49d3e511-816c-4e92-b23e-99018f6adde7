import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm'

export class AddRiskAndVerificationFieldsToUser1724941037764
  implements MigrationInterface
{
  name = 'AddRiskAndVerificationFieldsToUser1724941037764'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'domain_verification_date',
        type: 'timestamp',
        isNullable: true,
      })
    )

    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'is_considered_risky',
        type: 'boolean',
        default: false,
      })
    )

    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'phone_verification_sent_at',
        type: 'timestamp',
        isNullable: true,
      })
    )

    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'phone_verification_attempts',
        type: 'int',
        default: 0,
      })
    )

    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'phone_verification_date',
        type: 'timestamp',
        isNullable: true,
      })
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('users', 'phone_verification_date')
    await queryRunner.dropColumn('users', 'phone_verification_attempts')
    await queryRunner.dropColumn('users', 'phone_verification_sent_at')
    await queryRunner.dropColumn('users', 'is_considered_risky')
    await queryRunner.dropColumn('users', 'domain_verification_date')
  }
}
