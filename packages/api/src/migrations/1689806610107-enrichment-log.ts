import { MigrationInterface, QueryRunner } from 'typeorm'

export class EnrichmentLog1689806610107 implements MigrationInterface {
  name = 'EnrichmentLog1689806610107'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "enrichments_queries" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "organization_id" uuid NOT NULL, "enrichment_id" uuid NOT NULL, "enrichment_type" character varying NOT NULL DEFAULT 'GENERAL', "provider" character varying, "contact_id" character varying, "company_id" character varying, "query" jsonb, "query_result" jsonb, "error" character varying, "credit_used" numeric NOT NULL DEFAULT '0', "duration" numeric NOT NULL DEFAULT '0', "created_by_id" uuid, "updated_by_id" uuid, CONSTRAINT "PK_9bf140f2b8cf65b1b45ae25c9c1" PRIMARY KEY ("id"))`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" ADD CONSTRAINT "FK_e586fb7efa5969d6f2264e95c2c" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" ADD CONSTRAINT "FK_4f2a59d0d79cac6de2f23c4e4d4" FOREIGN KEY ("enrichment_id") REFERENCES "enrichments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" ADD CONSTRAINT "FK_a3e372cdf05d3c15f3661638e8e" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" ADD CONSTRAINT "FK_c3e50acbed41600963a6b6f4ffb" FOREIGN KEY ("updated_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" DROP CONSTRAINT "FK_c3e50acbed41600963a6b6f4ffb"`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" DROP CONSTRAINT "FK_a3e372cdf05d3c15f3661638e8e"`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" DROP CONSTRAINT "FK_4f2a59d0d79cac6de2f23c4e4d4"`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments_queries" DROP CONSTRAINT "FK_e586fb7efa5969d6f2264e95c2c"`
    )
    await queryRunner.query(`DROP TABLE "enrichments_queries"`)
  }
}
