import { MigrationInterface, QueryRunner } from 'typeorm'

export class UnsubscribedEmailSequence1693318037402
  implements MigrationInterface
{
  name = 'UnsubscribedEmailSequence1693318037402'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unsubscribed_emails" ADD "sequence_id" uuid NOT NULL`
    )
    await queryRunner.query(
      `ALTER TABLE "unsubscribed_emails" ADD CONSTRAINT "FK_8ebbbf954c0b195fbcb9a460d4c" FOREIGN KEY ("sequence_id") REFERENCES "sequences"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unsubscribed_emails" DROP CONSTRAINT "FK_8ebbbf954c0b195fbcb9a460d4c"`
    )
    await queryRunner.query(
      `ALTER TABLE "unsubscribed_emails" DROP COLUMN "sequence_id"`
    )
  }
}
