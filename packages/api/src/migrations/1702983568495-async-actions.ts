import { MigrationInterface, QueryRunner } from 'typeorm'

export class AsyncActions1702983568495 implements MigrationInterface {
  name = 'AsyncActions1702983568495'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "async_actions" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "label" character varying NOT NULL, "external_id" character varying DEFAULT NULL, "organization_id" uuid NOT NULL, "created_by_id" uuid NOT NULL, "internal_relation_id" uuid, "status" character varying NOT NULL, "category" character varying NOT NULL, "last_status_update" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "estimated_time_of_completion" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_e558ff5acbb6e5f0f4c732511e9" PRIMARY KEY ("id"))`
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_async_actions_created_by_id" ON "async_actions" ("created_by_id")`
    )
    await queryRunner.query(
      `ALTER TABLE "async_actions" ADD CONSTRAINT "FK_57393bf7440156d453119803994" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "async_actions" ADD CONSTRAINT "FK_938feae64cf5849f754ba2f21e6" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_async_actions_internal_relation_id" ON "async_actions" ("internal_relation_id") `
    )
    await queryRunner.query(
      `ALTER TABLE "async_actions" ADD CONSTRAINT "UQ_7e274689ec5f7c502f866eb9f1d" UNIQUE ("internal_relation_id")`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "async_actions" DROP CONSTRAINT "FK_938feae64cf5849f754ba2f21e6"`
    )
    await queryRunner.query(
      `ALTER TABLE "async_actions" DROP CONSTRAINT "FK_57393bf7440156d453119803994"`
    )
    await queryRunner.query(
      `ALTER TABLE "async_actions" DROP CONSTRAINT "UQ_7e274689ec5f7c502f866eb9f1d"`
    )
    await queryRunner.query(`DROP INDEX "IDX_async_actions_created_by_id"`)
    await queryRunner.query(`DROP TABLE "async_actions"`)
  }
}
