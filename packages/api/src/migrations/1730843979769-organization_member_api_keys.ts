import { MigrationInterface, QueryRunner } from 'typeorm'

export class OrganizationMemberApiKeys1730843979769
  implements MigrationInterface
{
  name = 'OrganizationMemberApiKeys1730843979769'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "organization_member_api_keys" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "key" character varying NOT NULL, "rate_limit_per_second" integer, "rate_limit_per_minute" integer, "rate_limit_per_hour" integer, "rate_limit_per_day" integer, "ignore_rate_limit" boolean NOT NULL DEFAULT false, "organization_member_id" uuid NOT NULL, "created_by_id" uuid, CONSTRAINT "UQ_770feec08d7b8068016779b389e" UNIQUE ("key"), CONSTRAINT "PK_0b2161b4363dc6e914716cff2e6" PRIMARY KEY ("id"))`
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_organization_member_api_keys_key" ON "organization_member_api_keys" ("key") `
    )
    await queryRunner.query(
      `ALTER TABLE "organization_member_api_keys" ADD CONSTRAINT "FK_9252d16058e80c37a2e90ddb7b8" FOREIGN KEY ("organization_member_id") REFERENCES "organizations_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "organization_member_api_keys" ADD CONSTRAINT "FK_501ecbed7deb45f1a08a0093a5a" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "enrichments" ADD COLUMN "api_input" jsonb`
    )

    await queryRunner.query(`
      ALTER TABLE "contacts" 
      ALTER COLUMN "first_name" DROP NOT NULL,
      ALTER COLUMN "last_name" DROP NOT NULL
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "enrichments" DROP COLUMN "api_input"`)
    await queryRunner.query(
      `ALTER TABLE "organization_member_api_keys" DROP CONSTRAINT "FK_501ecbed7deb45f1a08a0093a5a"`
    )
    await queryRunner.query(
      `ALTER TABLE "organization_member_api_keys" DROP CONSTRAINT "FK_9252d16058e80c37a2e90ddb7b8"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_organization_member_api_keys_key"`
    )
    await queryRunner.query(`DROP TABLE "organization_member_api_keys"`)

    await queryRunner.query(`
      ALTER TABLE "contacts" 
      ALTER COLUMN "first_name" SET NOT NULL,
      ALTER COLUMN "last_name" SET NOT NULL
    `)
  }
}
