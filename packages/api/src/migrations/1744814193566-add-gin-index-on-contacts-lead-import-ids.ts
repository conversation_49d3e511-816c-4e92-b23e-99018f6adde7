import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddGinIndexOnContactsLeadImportIds1744814193566
  implements MigrationInterface
{
  name = 'AddGinIndexOnContactsLeadImportIds1744814193566'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE INDEX "idx_contact_lead_import_ids_gin" ON "contacts"
        USING GIN (lead_import_ids);
    `)
    await queryRunner.query(`
      CREATE INDEX "idx_company_lead_import_ids_gin" ON "companies"
        USING GIN (lead_import_ids);
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_contact_lead_import_ids_gin";`
    )
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_company_lead_import_ids_gin";`
    )
  }
}
