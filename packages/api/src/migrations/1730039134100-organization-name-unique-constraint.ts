import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddUniqueConstraintToOrganizationsNameCreatedBy1730039134100
  implements MigrationInterface
{
  name = 'AddUniqueConstraintToOrganizationsNameCreatedBy1730039134100'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE organizations
        ADD CONSTRAINT "UQ_organization_name_createdBy" UNIQUE ("name", "created_by_id");
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE organizations
        DROP CONSTRAINT "UQ_organization_name_createdBy";
    `)
  }
}
