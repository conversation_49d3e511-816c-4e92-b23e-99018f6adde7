import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  Param,
  Post,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { PermissionSpecification } from '../../../../../app/shared/domain/specification/model-permission.specitification'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { SequencePermission } from '../../../../../app/sequence/domain/sequence-permission.enum'
import { SequenceStepModel } from '../../../../../app/sequence/domain/model/sequence-step.model'
import { CreateSequenceStepDto } from '../../dto/create-sequence-step.dto'
import { CreateSequenceStepCommand } from '../../../../../app/sequence/application/command/create-sequence-step/create-sequence-step.command'
import { SequencePipe } from '../../pipe/sequence.pipe'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import { FeatureService } from '../../../../../app/feature/application/service/feature.service'
import { FeatureGateway, SequenceStepType } from '@getheroes/shared'

@Controller('/:organizationId')
@ApiTags('Sequence')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class CreateSequenceStepController {
  constructor(
    private readonly commandBus: CommandBus,
    @InjectMapper() private readonly mapper: Mapper,
    private readonly featureService: FeatureService
  ) {}

  @Post('/sequences/:sequenceId/step')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to create sequence step',
  })
  @ApiResponse({
    type: SequenceStepModel,
  })
  @ApiBadRequestResponse({
    schema: {
      type: 'object',
      oneOf: [
        {
          example: {
            error: 'VALIDATION_FAILED',
            message: 'name must be a string',
          },
        },
      ],
    },
  })
  async create(
    @Param('organizationId') organizationId: string,
    @Param(SequencePipe) sequenceModel,
    @Body() createSequenceStepDto: CreateSequenceStepDto,
    @User() user: UserModel
  ): Promise<SequenceStepModel> {
    const sequenceStepModel = this.mapper.map(
      createSequenceStepDto,
      CreateSequenceStepDto,
      SequenceStepModel
    )

    if (
      new PermissionSpecification(sequenceModel).can(
        SequencePermission.CREATE,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    const linkedinStepTypes = [
      SequenceStepType.LINKEDIN_SEND_MESSAGE,
      SequenceStepType.LINKEDIN_SEND_INVITATION,
      SequenceStepType.LINKEDIN_VISIT_PROFILE,
    ]
    const includesLinkedinStep = linkedinStepTypes.includes(
      createSequenceStepDto.type
    )
    if (includesLinkedinStep) {
      const feature = FeatureGateway.CAN_USE_LINKEDIN_INTEGRATION
      const canUseLinkedinIntegration = await this.featureService.canUseFeature(
        organizationId,
        feature
      )

      if (!canUseLinkedinIntegration) {
        throw new ForbiddenException(`Access to feature ${feature} is denied`)
      }
    }

    const isContentValid = createSequenceStepDto.content?.validateContent(
      createSequenceStepDto.type
    )
    if (isContentValid === false) {
      throw new BadRequestException(
        'Step content is inconsistent according to the type'
      )
    }

    sequenceStepModel.sequence = sequenceModel

    return this.commandBus.execute(
      new CreateSequenceStepCommand(sequenceStepModel)
    )
  }
}
