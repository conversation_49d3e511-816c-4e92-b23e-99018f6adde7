import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  HttpStatus,
  Param,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { User } from '../../shared/infrastructure/auth/decorator/user.decorator'
import { UserModel } from '../../../../app/shared/domain/model/user.model'
import { EXPOSE_GROUP } from '../../../../app/shared/globals/expose-group.enum'
import { GetUserQuery } from '../../../../app/user/application/query/get-user/get-user.query'
import { UpdateEndOfFreeTrialSettingsCommand } from '../../../../app/user/application/command/update-end-of-free-trial-settings/update-end-of-free-trial-settings.command'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import { FindOrganizationMembersByUserQuery } from '../../../../app/organization/application/query/find-organization-members-by-user/find-organization-members-by-user.query'
import { LoggerService } from '../../../../app/shared/logger.service'

@Controller()
@ApiTags('User')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({ groups: [EXPOSE_GROUP.PUBLIC, EXPOSE_GROUP.ME] })
export class GetUserController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus
  ) {}

  private readonly logger = new LoggerService({
    context: GetUserController.name,
  })

  @Get(['/:organizationId/users', '/users']) // TODO: revert this commit after August 19 2023 - when extension had time to update
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(UserModel),
    },
  })
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  async invoke(
    @User() userModel: UserModel,
    @Param('organizationId') organizationIdOrUndefined?: string
  ): Promise<UserModel> {
    let organizationId = organizationIdOrUndefined
    if (!organizationId) {
      const organizationMemberModel = await this.queryBus.execute(
        new FindOrganizationMembersByUserQuery(userModel.id)
      )
      organizationId = organizationMemberModel.organization.id

      this.logger.warn({
        message:
          'organizationId is undefined, using organizationId from organizationMemberModel',
        data: { organizationId },
      })
    }
    await this.commandBus.execute(
      new UpdateEndOfFreeTrialSettingsCommand(userModel.id, organizationId)
    )

    return await this.queryBus.execute(new GetUserQuery(userModel.id))
  }
}
