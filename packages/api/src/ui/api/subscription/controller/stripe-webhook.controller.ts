import {
  <PERSON>,
  <PERSON>,
  Head<PERSON>,
  Req,
  BadRequestException,
  HttpStatus,
} from '@nestjs/common'

import RequestWithRawBody from '../../../../app/subscription/middleware/request-with-raw-body.interface'

import {
  HandleStripeEventCommand,
  HandleStripeEventResult,
} from '../../../../app/subscription/application/command/handle-stripe-event/handle-stripe-event.command'
import { CommandBus } from '@nestjs/cqrs'
import {
  LogFeature,
  LoggerService,
} from '../../../../app/shared/logger.service'
import {
  ValidateStripeWebhookEventCommand,
  ValidateStripeWebhookEventResult,
} from '../../../../app/subscription/application/command/validate-stripe-webhook-event/validate-stripe-webhook-event.command'

@Controller()
export default class StripeWebhookController {
  constructor(private commandBus: CommandBus) {}

  private logger = new LoggerService({
    context: StripeWebhookController.name,
    feature: LogFeature.SUBSCRIPTION,
  })

  @Post('/subscription/webhook/stripe')
  async handleIncomingEvents(
    @Headers('stripe-signature') signature: string,
    @Req() request: RequestWithRawBody
  ) {
    if (!signature) {
      this.logger.error({
        message: 'Missing stripe-signature header',
        data: { request },
      })
      throw new BadRequestException('Missing stripe-signature header')
    }

    // Get the valid stripe event to process or return early
    const { stripeEvent, returnSuccess }: ValidateStripeWebhookEventResult =
      await this.commandBus.execute(
        new ValidateStripeWebhookEventCommand(signature, request)
      )
    if (returnSuccess) return true
    if (!stripeEvent) return

    // Process the Stripe event and save it in DB
    const isSuccessfullyHandled: HandleStripeEventResult =
      await this.commandBus.execute(new HandleStripeEventCommand(stripeEvent))
    if (!isSuccessfullyHandled) return

    // Indicate to Stripe that the webhook event has been successfully received & processed
    // Else, Stripe will apply the retry policy for failed webhook events
    return { status: HttpStatus.OK }
  }
}
