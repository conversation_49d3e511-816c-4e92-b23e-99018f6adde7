import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { PermissionSpecification } from '../../../../../app/shared/domain/specification/model-permission.specitification'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import { ViewModel } from '../../../../../app/lead/domain/model/view.model'
import { UpdateViewDto } from '../../dto/view/update-view.dto'
import { ViewPermission } from '../../../../../app/lead/domain/view-permission.enum'
import { ViewPipe } from '../../pipe/view/view.pipe'
import { UpdateViewCommand } from '../../../../../app/lead/application/command/view/update-view/update-view.command'
import { instanceToPlain } from 'class-transformer'

@Controller()
@ApiTags('Leads')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC, EXPOSE_GROUP.LIST],
})
export class UpdateViewController {
  constructor(
    private commandBus: CommandBus,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  @Patch('/:organizationId/leads/views/:viewId')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiConflictResponse({
    schema: {
      type: 'object',
      example: {
        statusCode: HttpStatus.CONFLICT,
        message: 'View name is not available',
        error: 'Conflict',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @ApiBadRequestResponse({
    schema: {
      type: 'object',
      example: {
        error: 'VALIDATION_FAILED',
        message: 'a message to describe error',
      },
    },
  })
  @ApiExtraModels(ViewModel)
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(ViewModel),
    },
  })
  @ApiOperation({
    summary: 'Endpoint use to update a view',
  })
  @HttpCode(HttpStatus.OK)
  invoke(
    @Param('organizationId') organizationId: string,
    @Param(ViewPipe) viewModel,
    @Body() updateViewDto: UpdateViewDto,
    @User() user: UserModel
  ) {
    if (
      new PermissionSpecification(viewModel).can(
        ViewPermission.UPDATE,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    const viewModelNewValues: Partial<ViewModel> =
      instanceToPlain(updateViewDto)

    return this.commandBus.execute(
      new UpdateViewCommand(viewModel, viewModelNewValues)
    )
  }
}
