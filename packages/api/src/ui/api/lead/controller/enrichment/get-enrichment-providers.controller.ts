import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import {
  <PERSON>piB<PERSON>erAuth,
  ApiForbiddenResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { AuthGuard } from '@nestjs/passport'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { CommandBus } from '@nestjs/cqrs'
import { GetEnrichmentProvidersCommand } from '../../../../../app/lead/application/command/enrichment/get-enrichment-providers/get-enrichment-providers.command'

@Controller()
@ApiTags('Enrichment')
@UseInterceptors(ClassSerializerInterceptor)
export class GetEnrichmentProvidersController {
  constructor(private commandBus: CommandBus) {}

  @Get('/:organizationId/leads/waterfall/providers')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  async getEnrichmentProviders(): Promise<object> {
    return this.commandBus.execute(new GetEnrichmentProvidersCommand())
  }
}
