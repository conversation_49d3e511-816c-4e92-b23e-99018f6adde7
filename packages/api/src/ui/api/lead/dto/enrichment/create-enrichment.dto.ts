import { AutoMap } from '@automapper/classes'
import { ApiPropertyOptional } from '@nestjs/swagger'
import {
  ArrayMaxSize,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsUUID,
} from 'class-validator'
import { EnrichmentType } from '@getheroes/shared'

export class CreateEnrichmentDto {
  @ApiPropertyOptional()
  @IsOptional()
  @AutoMap(() => String)
  @IsUUID('all', { each: true })
  @ArrayMaxSize(100)
  contactsIds: string[] = []

  @ApiPropertyOptional()
  @IsOptional()
  @AutoMap(() => String)
  @IsUUID('all', { each: true })
  @ArrayMaxSize(100)
  companiesIds: string[] = []

  @ApiPropertyOptional({ enum: EnrichmentType })
  @IsOptional()
  @AutoMap(() => String)
  @IsEnum(EnrichmentType)
  @IsNotEmpty()
  enrichmentType?: EnrichmentType = EnrichmentType.ADVANCED
}
