import { AutoMap } from '@automapper/classes'
import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator'
import { FieldFamily } from '../../../../../app/lead/domain/field-family.enum'
import { FieldType } from '../../../../../app/shared/domain/field-type.enum'

export class CustomFieldDto {
  @AutoMap()
  @ApiProperty({ required: true })
  @MaxLength(50)
  @IsNotEmpty()
  @IsString()
  @Matches(/^([a-zA-Z0-9-_]+)$/)
  fieldId: string

  @AutoMap()
  @ApiProperty({ required: true })
  @MaxLength(50)
  @IsNotEmpty()
  @IsString()
  fieldLabel: string

  @AutoMap(() => String)
  @ApiProperty({ required: true, enum: FieldFamily })
  @IsEnum(FieldFamily)
  fieldFamily: FieldFamily

  @AutoMap(() => String)
  @ApiProperty({ required: true, enum: FieldType })
  @IsEnum(FieldType)
  fieldType: FieldType

  @AutoMap()
  @ApiProperty()
  fieldFormat: string

  @AutoMap()
  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => !!value)
  isSortable: boolean

  @AutoMap()
  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => !!value)
  isEditable: boolean

  @AutoMap()
  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => !!value)
  isFilterable: boolean
}
