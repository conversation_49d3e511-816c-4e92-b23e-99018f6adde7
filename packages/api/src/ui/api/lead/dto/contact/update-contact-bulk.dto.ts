import { AutoMap } from '@automapper/classes'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import {
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { LeadStatus } from '@getheroes/shared'
import { Type } from 'class-transformer'
import { UpdateLabelsDto } from '../labels/update-labels.dto'

export class UpdateContactBulkDto {
  @AutoMap(() => [String])
  @ApiProperty({ required: true })
  @IsArray()
  @ArrayNotEmpty()
  contactIds: string[]

  @IsString()
  @AutoMap(() => String)
  @IsEnum(LeadStatus)
  @ApiPropertyOptional({ enum: LeadStatus })
  @IsOptional()
  status: LeadStatus

  @AutoMap()
  @ApiPropertyOptional()
  @ValidateNested()
  @IsOptional()
  @Type(() => UpdateLabelsDto)
  labels: UpdateLabelsDto
}
