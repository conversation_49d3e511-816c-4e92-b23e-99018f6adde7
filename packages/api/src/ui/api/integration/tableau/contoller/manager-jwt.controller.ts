import { Controller, Get, HttpStatus, Param, UseGuards } from '@nestjs/common'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { AuthGuard } from '@nestjs/passport'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { QueryBus } from '@nestjs/cqrs'
import { GetTableauTokenQuery } from '../../../../../app/integration/tableau/application/query/get-tableau-token/get-tableau-token.query'
import { FeatureGuard } from '../../../shared/infrastructure/guard/feature.guard'
import { RequiresFeature } from '../../../shared/infrastructure/decorator/requires-feature.decorator'
import { FeatureGateway } from '@getheroes/shared'

@Controller()
export class GenerateManagerJwtController {
  constructor(private readonly queryBus: QueryBus) {}

  @ApiTags('Integration/Tableau')
  @Get('/:organizationId/integrations/tableau/manager/generate-jwt')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard, FeatureGuard)
  @RequiresFeature(FeatureGateway.CAN_USE_MANAGER_COCKPIT)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to fetch a jwt to access to Tableau integration',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    schema: {
      type: 'object',
      properties: {
        token: { type: 'string' },
      },
    },
  })
  async invoke(
    @Param('organizationId') organizationId: string,
    @User() user: UserModel
  ) {
    const token = await this.queryBus.execute(
      new GetTableauTokenQuery(organizationId, user)
    )
    return { token }
  }
}
