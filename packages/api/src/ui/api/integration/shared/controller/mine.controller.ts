import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  Api<PERSON><PERSON>erA<PERSON>,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import {
  IntegrationItemResponse,
  IntegrationName,
} from '../response/integration-item.response'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { FindGrantedGmailAuthorizationQuery } from '../../../../../app/integration/gmail/application/query/find-gmail-authorization/find-granted-gmail-authorization.query'
import { RefreshGmailAliasCommand } from '../../../../../app/integration/gmail/application/command/refresh-gmail-alias/refresh-gmail-alias.command'
import { IntegrationType } from '@getheroes/shared'
import { FindGrantedAuthorizationQuery as FindGrantedOutlookAuthorizationQuery } from '../../../../../app/integration/outlook/application/query/find-outlook-authorization/find-granted-authorization.query'
import { FindGrantedAuthorizationQuery } from '../../../../../app/integration/captain-data/application/query/find-authorization/find-granted-authorization.query'
import { Integration } from '../../../../../app/integration/captain-data/domain/enum/integration.enum'
import {
  LogFeature,
  LoggerService,
} from '../../../../../app/shared/logger.service'

@Controller()
@ApiTags('Integration')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class IntegrationMineController {
  constructor(
    private queryBus: QueryBus,
    private readonly commandBus: CommandBus
  ) {}

  private readonly logger = new LoggerService({
    context: IntegrationMineController.name,
    feature: LogFeature.INTEGRATIONS,
  })

  @Get('/integrations/mine')
  @UseGuards(AuthGuard('jwt'))
  @ApiResponse({
    status: 200,
    isArray: true,
    type: IntegrationItemResponse,
  })
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to fetch all integration status of a user',
  })
  @SerializeOptions({
    groups: ['public'],
  })
  async invoke(@User() user): Promise<IntegrationItemResponse[]> {
    // TODO : All refacto

    // GMAIL
    // refresh alias to make sure we will have the new ones
    try {
      await this.commandBus.execute(new RefreshGmailAliasCommand(user.id))
    } catch (error) {
      this.logger.error({
        error,
        data: { userId: user.id },
        message: 'Error RefreshGmailAliasCommand',
      })
    }

    const gmailAuthorizationModel = await this.queryBus.execute(
      new FindGrantedGmailAuthorizationQuery(user.id)
    )

    const gmailIntegration = new IntegrationItemResponse(
      IntegrationName.GMAIL,
      gmailAuthorizationModel,
      IntegrationType.EMAIL
    )

    // OUTLOOK
    const outlookAuthorizationModel = await this.queryBus.execute(
      new FindGrantedOutlookAuthorizationQuery(user.id)
    )

    const outlookIntegration = new IntegrationItemResponse(
      IntegrationName.OUTLOOK,
      outlookAuthorizationModel,
      IntegrationType.EMAIL
    )

    // LINKEDIN
    const linkedinAuthorizationModel = await this.queryBus.execute(
      new FindGrantedAuthorizationQuery(Integration.LINKEDIN, user.id)
    )

    const linkedinIntegration = new IntegrationItemResponse(
      IntegrationName.LINKEDIN,
      linkedinAuthorizationModel,
      IntegrationType.LINKEDIN
    )

    return [gmailIntegration, outlookIntegration, linkedinIntegration]
  }
}
