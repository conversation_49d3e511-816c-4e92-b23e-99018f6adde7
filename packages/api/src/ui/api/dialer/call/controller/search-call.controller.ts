import {
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { CallModel } from '../../../../../app/dialer/domain/model/call.model'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { PaginationResultMetaObject } from '../../../../../app/shared/domain/object/pagination-result-meta.object'
import { QueryFilterCallDto } from '../dto/query-filter-call.dto'
import { FindCallByReferenceIdQuery } from '../../../../../app/dialer/application/query/find-call-reference/find-call-reference.query'
import { QueryBus } from '@nestjs/cqrs'
import { PermissionSpecification } from '../../../../../app/shared/domain/specification/model-permission.specitification'
import { CallPermission } from '../../../../../app/dialer/domain/model/call-permission.enum'

@Controller()
@ApiTags('Dialer')
export class SearchCallController {
  constructor(private queryBus: QueryBus) {}

  @Get('/:organizationId/dialer/calls/search')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiBearerAuth('access-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Endpoint to search one call',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(CallModel),
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: {
            $ref: getSchemaPath(CallModel),
          },
        },
        meta: {
          type: 'object',
          $ref: getSchemaPath(PaginationResultMetaObject),
        },
      },
    },
  })
  async invoke(
    @Param('organizationId') organizationId: string,
    @User() user: UserModel,
    @Query() queryFilterCallDto: QueryFilterCallDto
  ): Promise<CallModel | any> {
    if (!queryFilterCallDto.reference) {
      return {}
    }

    const callModel = await this.queryBus.execute(
      new FindCallByReferenceIdQuery(
        organizationId,
        queryFilterCallDto.reference
      )
    )

    if (!callModel) {
      return {}
    }

    if (
      new PermissionSpecification(callModel).can(CallPermission.VIEW, user) ===
      false
    ) {
      throw new ForbiddenException()
    }

    return callModel
  }
}
