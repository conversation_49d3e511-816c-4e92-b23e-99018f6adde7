import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { AutoMap } from '@automapper/classes'
import { PaginationQueryDto } from '../../../shared/infrastructure/dto/pagination-query.dto'

export class QueryFilterCallDto extends PaginationQueryDto {
  @AutoMap(() => String)
  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  public reference?: string
}
