import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger'
import { SendMessageDto } from './send-message.dto'
import { AutoMap } from '@automapper/classes'
import { IsBoolean, IsOptional } from 'class-validator'

export class ReplyMessageDto extends PartialType(
  OmitType(SendMessageDto, ['taskId', 'subject'] as const)
) {
  @ApiProperty({ required: false })
  @AutoMap()
  @IsBoolean()
  @IsOptional()
  addSignature: boolean
}
