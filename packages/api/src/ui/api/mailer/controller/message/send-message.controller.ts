import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  HttpStatus,
  Param,
  Post,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiPayloadTooLargeResponse,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { InjectMapper } from '@automapper/nestjs'
import { Mapper } from '@automapper/core'
import { FindTaskAssignQuery } from '../../../../../app/journey/application/query/task/find-task/find-task-assign.query'
import { FindTaskQuery } from '../../../../../app/journey/application/query/task/find-task/find-task.query'
import { MessageModel } from '../../../../../app/mailer/domain/model/message.model'
import { SendMessageDto } from '../../dto/send-message.dto'
import { SendMessageCommand } from '../../../../../app/mailer/application/command/send-message/send-message.command'
import { FindMessageByTaskQuery } from '../../../../../app/mailer/application/query/find-message-by-task/find-message-by-task.query'
import { TaskModel } from '../../../../../app/journey/domain/task/model/task.model'

@Controller()
@ApiTags('Mailer')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class SendMessageController {
  constructor(
    @InjectMapper()
    private readonly mapper: Mapper,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  @Post('/:organizationId/mailer/message')
  @UseGuards(AuthGuard('jwt'))
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiPayloadTooLargeResponse({ description: 'Attachments too large' })
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to send an email (max: 10MB of attachments / request)',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    schema: {
      $ref: getSchemaPath(MessageModel),
    },
  })
  async invoke(
    @Body() sendMessageDto: SendMessageDto,
    @Param('organizationId') organizationId: string,
    @User() user: UserModel
  ): Promise<MessageModel> {
    const messageModel = this.mapper.map(
      sendMessageDto,
      SendMessageDto,
      MessageModel
    )

    messageModel.organizationId = organizationId
    messageModel.createdBy = user

    // TODO deplacer ce code dans un service
    // Check if task assigned
    const taskAssigned = await this.queryBus.execute(
      new FindTaskAssignQuery(sendMessageDto.taskId, messageModel.createdBy.id)
    )
    if (taskAssigned === null) {
      throw new ForbiddenException('Task not assigned')
    }

    // If the mail already exists in draft, we return it
    const messageModelIfExist = await this.queryBus.execute(
      new FindMessageByTaskQuery(sendMessageDto.taskId)
    )
    if (
      messageModelIfExist &&
      messageModelIfExist.createdBy?.id === taskAssigned.assign?.id
    ) {
      return messageModelIfExist
    }

    // Create
    if (!messageModel.to) {
      if (false === messageModel.isDraft) {
        throw new BadRequestException('to should not be empty')
      }

      messageModel.to = []
    }

    const taskModel = await this.queryBus.execute<FindTaskQuery, TaskModel>(
      new FindTaskQuery(messageModel.taskId)
    )
    messageModel.contactId = taskModel.contactId
    messageModel.sequenceContactId = taskModel.sequenceContactId
    messageModel.sequenceStepId = taskModel.sequenceContact?.getSequenceStepId()
    // TODO FIN

    return await this.commandBus.execute(
      new SendMessageCommand(messageModel, 1, false)
    )
  }
}
