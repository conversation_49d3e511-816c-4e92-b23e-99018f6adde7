import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  HttpStatus,
  Param,
  Post,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiPayloadTooLargeResponse,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { AuthGuard } from '@nestjs/passport'
import { CommandBus } from '@nestjs/cqrs'
import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { instanceToPlain } from 'class-transformer'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import { ReplyMessageDto } from '../../dto/reply-message.dto'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { MessageModel } from '../../../../../app/mailer/domain/model/message.model'
import { MessagePipe } from '../../pipe/message.pipe'
import { ReplyMessageCommand } from '../../../../../app/mailer/application/command/reply-message/reply-message.command'

@Controller()
@ApiTags('Mailer')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class ReplyMessageController {
  constructor(
    readonly commandBus: CommandBus,
    @InjectMapper()
    private readonly mapper: Mapper
  ) {}

  @Post('/:organizationId/mailer/message/:messageId/reply')
  @ApiOperation({
    summary:
      'Endpoint to reply to an email (max: 10MB of attachments / request)',
  })
  @UseGuards(AuthGuard('jwt'))
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiPayloadTooLargeResponse({ description: 'Attachments too large' })
  @ApiBearerAuth('access-token')
  @ApiResponse({
    status: HttpStatus.CREATED,
    schema: {
      $ref: getSchemaPath(MessageModel),
    },
  })
  async invoke(
    @Param('organizationId') organizationId: string,
    @Param(MessagePipe) messageToReplyModel,
    @Body() replyMessageDto: ReplyMessageDto,
    @User() user: UserModel
  ): Promise<MessageModel> {
    if (messageToReplyModel.createdBy.id !== user.id) {
      throw new ForbiddenException()
    }
    const responseMessageModel: MessageModel = instanceToPlain(
      replyMessageDto
    ) as MessageModel

    responseMessageModel.organizationId = organizationId
    responseMessageModel.taskId = messageToReplyModel.taskId
    responseMessageModel.createdBy = user

    return await this.commandBus.execute(
      new ReplyMessageCommand(
        messageToReplyModel,
        responseMessageModel,
        1,
        replyMessageDto.addSignature
      )
    )
  }
}
