import { TestBed } from '@automock/jest'
import { HubspotContactListService } from './hubspot-contact-list.service'
import { HubspotAuthorizationService } from './hubspot-authorization.service'
import { QueryBus } from '@nestjs/cqrs'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../../lead/domain/model/company-repository.interface'
import { SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/contacts'
import { PublicObjectList } from '@hubspot/api-client/lib/codegen/crm/lists/models/PublicObjectList'
import { Client } from '@hubspot/api-client'
import { CompanyModel } from '../../../../hubspot-lead/domain/model/company.model'

class HubspotClientScenario {
  private paginationState = {
    currentPage: 0,
    totalPages: 1,
  }

  constructor(
    private readonly mock: jest.Mocked<HubspotAuthorizationService>
  ) {}

  returnsListsOnSearch(lists: Partial<PublicObjectList>[]) {
    this.mock.getSearchHubspotClient.mockImplementation(
      async () =>
        ({
          crm: {
            lists: {
              listsApi: {
                doSearch: jest.fn().mockResolvedValue({
                  lists,
                  hasMore: false,
                  offset: 0,
                }),
              },
            },
          },
        }) as unknown as jest.Mocked<Client>
    )
    return this
  }

  returnsListMembersFromRecordIds(
    recordIds: string[],
    contacts: SimplePublicObject[]
  ) {
    this.mock.getHubspotClient.mockImplementation(
      async () =>
        ({
          crm: {
            lists: {
              membershipsApi: {
                getPage: jest.fn().mockResolvedValue({
                  results: recordIds.map(id => ({ recordId: id })),
                  paging: { next: null },
                }),
              },
            },
            contacts: {
              batchApi: {
                read: jest.fn().mockResolvedValue({
                  results: contacts,
                }),
              },
            },
          },
        }) as unknown as jest.Mocked<Client>
    )
    return this
  }

  returnsPaginatedMembers(recordIds: string[], pageSize = 1) {
    const pages = this.chunkArray(recordIds, pageSize)
    this.paginationState.totalPages = pages.length

    this.mock.getHubspotClient.mockImplementation(
      async () =>
        ({
          crm: {
            lists: {
              membershipsApi: {
                getPage: jest.fn().mockImplementation((_listId, after) => {
                  const pageIndex = after ? parseInt(after, 10) : 0
                  this.paginationState.currentPage = pageIndex + 1

                  return {
                    results:
                      pages[pageIndex]?.map(id => ({ recordId: id })) || [],
                    paging: {
                      next:
                        pageIndex < pages.length - 1
                          ? { after: `${pageIndex + 1}` }
                          : null,
                    },
                  }
                }),
              },
            },
          },
        }) as unknown as jest.Mocked<Client>
    )
    return this
  }

  returnsListsByIds(lists: Partial<PublicObjectList>[]) {
    this.mock.getHubspotClient.mockImplementation(
      async () =>
        ({
          crm: {
            lists: {
              listsApi: {
                getById: jest.fn().mockImplementation((listId: number) => ({
                  list: lists.find(l => l.listId === listId),
                })),
              },
            },
          },
        }) as unknown as jest.Mocked<Client>
    )
    return this
  }

  static from(mock: jest.Mocked<HubspotAuthorizationService>) {
    return new HubspotClientScenario(mock)
  }

  forOrganization(organizationId: string) {
    return this
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from({ length: Math.ceil(array.length / size) }, (_, i) =>
      array.slice(i * size, i * size + size)
    )
  }
}

describe('HubspotContactListService', () => {
  let service: HubspotContactListService
  let hubspotAuthService: jest.Mocked<HubspotAuthorizationService>
  let queryBus: jest.Mocked<QueryBus>
  let companiesRepo: jest.Mocked<CompanyRepositoryInterface>

  beforeEach(() => {
    const { unit, unitRef } = TestBed.create(
      HubspotContactListService
    ).compile()
    service = unit
    hubspotAuthService = unitRef.get(HubspotAuthorizationService)
    queryBus = unitRef.get(QueryBus)
    companiesRepo = unitRef.get(COMPANY_REPOSITORY_INTERFACE)
  })

  describe('fetchLists', () => {
    it('should fetch a list by its id', async () => {
      const orgId = 'org-123'
      const mockList = {
        listId: 1,
        name: 'Test List',
        processingType: 'DYNAMIC',
        additionalProperties: { hs_list_size: '100' },
        objectTypeId: '0-1',
      }

      HubspotClientScenario.from(hubspotAuthService)
        .returnsListsByIds([mockList])
        .forOrganization(orgId)

      const result = await service.fetchListsByIds(orgId, [1])

      expect(result).toEqual([mockList])
    })

    it('should return formatted lists with URLs', async () => {
      // Arrange
      const orgId = 'org-123'
      const mockList = {
        listId: 1,
        name: 'Test List',
        processingType: 'DYNAMIC',
        additionalProperties: { hs_list_size: '100' },
        objectTypeId: '0-1',
      }

      queryBus.execute.mockResolvedValue({
        uiDomain: 'test.hubspot.com',
        portalId: '12345',
      })

      HubspotClientScenario.from(hubspotAuthService)
        .returnsListsOnSearch([mockList])
        .forOrganization(orgId)

      // Act
      const result = await service.fetchLists(orgId)

      // Assert
      expect(result).toEqual([
        {
          listId: 1,
          name: 'Test List',
          isSynced: false,
          dynamic: true,
          size: 100,
          url: 'https://test.hubspot.com/contacts/12345/objectLists/1',
        },
      ])
    })
  })

  describe('previewList', () => {
    it('should return contacts with company names', async () => {
      // Arrange
      const orgId = 'org-123'
      const listId = '456'
      const now = new Date()
      const mockContact = {
        id: 'contact-1',
        createdAt: now,
        updatedAt: now,
        properties: {
          firstname: 'John',
          lastname: 'Doe',
          associatedcompanyid: 'company-1',
        },
      }
      HubspotClientScenario.from(hubspotAuthService)
        .returnsListMembersFromRecordIds([mockContact.id], [mockContact])
        .forOrganization(orgId)

      companiesRepo.findByExternalIds.mockResolvedValue([
        {
          externalId: 'company-1',
          name: 'Test Company',
        } as jest.Mocked<CompanyModel>,
      ])

      // Act
      const result = await service.previewList(orgId, listId)

      // Assert
      expect(result).toEqual([
        {
          id: 'contact-1',
          firstName: 'John',
          lastName: 'Doe',
          companyName: 'Test Company',
        },
      ])
    })
  })

  describe('fetchRecordIdsOfList', () => {
    it('should return all record IDs from paginated API', async () => {
      // Arrange
      const orgId = 'org-123'
      const listId = 789
      const mockIds = ['id1', 'id2', 'id3', 'id4', 'id5', 'id6']

      HubspotClientScenario.from(hubspotAuthService)
        .returnsPaginatedMembers(mockIds)
        .forOrganization(orgId)

      // Act
      const result = await service.fetchRecordIdsOfList(listId, orgId)

      // Assert
      expect(Array.from(result)).toEqual(mockIds)
    })
  })
})
