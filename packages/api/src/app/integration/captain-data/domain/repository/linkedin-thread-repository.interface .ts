import { LinkedinThreadModel } from '../model/linkedin-thread.model'

export const LINKEDIN_THREAD_REPOSITORY_INTERFACE =
  'LINKEDIN_THREAD_REPOSITORY_INTERFACE'

export interface LinkedinThreadRepositoryInterface {
  upsert(linkedinThreadModel: LinkedinThreadModel): Promise<string>
  findByLinkedinThreadUrl(
    linkedinThreadUrl: string
  ): Promise<LinkedinThreadModel | null>
  updateById(
    id: string,
    partialLinkedinThreadModel: Partial<Omit<LinkedinThreadModel, 'id'>>
  ): Promise<void>
  findById(id: string): Promise<LinkedinThreadModel | null>
}
