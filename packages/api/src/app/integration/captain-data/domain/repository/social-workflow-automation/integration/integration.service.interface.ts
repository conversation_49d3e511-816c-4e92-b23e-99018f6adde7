import {
  CreateIntegrationAccountRequestDto,
  UpdateIntegrationAccountRequestDto,
} from '../../../../infrastructure/repository/api/dtos/integrations/integration-account.request.dto'
import { CreateOrUpdateIntegrationAccountInterface } from '../../../../infrastructure/repository/api/interface/integration/create-or-update-integration-account.interface'
import { CaptainDataIntegration } from '../../../../infrastructure/repository/api/interface/integration/captain-data-integration.type'
import { Integration } from '../../../enum/integration.enum'

export interface IntegrationServiceInterface {
  createIntegrationAccount(
    createIntegrationAccountRequestDto: CreateIntegrationAccountRequestDto,
    integration: Integration
  ): Promise<CreateOrUpdateIntegrationAccountInterface>
  updateIntegrationAccount(
    updateIntegrationAccountRequestDto: UpdateIntegrationAccountRequestDto,
    integration: Integration
  ): Promise<CreateOrUpdateIntegrationAccountInterface>
  getIntegrationAccountWithConsumption(
    accountId: string,
    integration: Integration
  ): Promise<CaptainDataIntegration>
}
