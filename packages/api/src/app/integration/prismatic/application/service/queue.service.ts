import { InjectQueue } from '@taskforcesh/nestjs-bullmq-pro'
import { Inject, Injectable } from '@nestjs/common'
import { PRISMATIC_SYNCHRONIZATION_QUEUE } from '../../infrastructure/global'
import type { BulkJobProOptions, QueuePro } from '@taskforcesh/bullmq-pro'
import type { CompanyRepositoryInterface } from '../../../../lead/domain/model/company-repository.interface'
import { COMPANY_REPOSITORY_INTERFACE } from '../../../../lead/domain/model/company-repository.interface'
import type { ContactRepositoryInterface } from '../../../../lead/domain/model/contact-repository.interface'
import { CONTACT_REPOSITORY_INTERFACE } from '../../../../lead/domain/model/contact-repository.interface'
import type { PrismaticPushJobData } from '../../domain/interface/prismatic-push-job-data.interface'

@Injectable()
export class PrismaticQueueService {
  constructor(
    @InjectQueue(PRISMATIC_SYNCHRONIZATION_QUEUE)
    private readonly syncQueue: QueuePro<PrismaticPushJobData>,
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    protected readonly companiesRepository: CompanyRepositoryInterface,
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    protected readonly contactsRepository: ContactRepositoryInterface
  ) {}

  public async pushContactsToQueue(
    organizationId: string,
    processFlowName: string,
    versionSequenceId: string,
    contactIds: string[]
  ) {
    const jobs: {
      name: string
      data: PrismaticPushJobData
      opts?: BulkJobProOptions
    }[] = contactIds.map(contactId => ({
      name: contactId,
      data: {
        contactId,
        organizationId,
        processFlowName,
        versionSequenceId,
      },
      opts: {
        jobId: contactId,
        group: {
          id: organizationId,
        },
      },
    }))

    await this.syncQueue.addBulk(jobs)
  }
}
