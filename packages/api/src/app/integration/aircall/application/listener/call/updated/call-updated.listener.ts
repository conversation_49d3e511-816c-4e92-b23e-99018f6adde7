import { Injectable, Logger } from '@nestjs/common'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import { AircallCallDto } from '../../../../../../../ui/api/integration/aircall/dto/hook/call/aircall-call.dto'
import { validate } from 'class-validator'
import { plainToInstance } from 'class-transformer'
import { CommandBus, EventBus, QueryBus } from '@nestjs/cqrs'
import { AircallHookEvent } from '../../../events/hook/aircall-hook-event.event'
import { CallEvents } from '../../../../../../shared/domain/event/event.enum'
import { FindCallByReferenceIdQuery } from '../../../../../../dialer/application/query/find-call-reference/find-call-reference.query'
import { UpdateCallCommand } from '../../../../../../dialer/application/command/update-call/update-call.command'
import { RecordingLinkAvailableEvent } from '../../../../../../dialer/domain/event/recording-link-available.event'
import { MissedCallEvent } from '../../../../../../shared/domain/event/call/call.event'

@Injectable()
export class CallUpdatedListener {
  private readonly logger = new Logger(CallUpdatedListener.name)

  constructor(
    readonly commandBus: CommandBus,
    private queryBus: QueryBus,
    private eventBus: EventBus,
    private eventEmitter: EventEmitter2
  ) {}

  @OnEvent(CallEvents.AIRCALL_CALL_UPDATED)
  async handleCallUpdatedEvent(event: AircallHookEvent) {
    const aircallCallDto = plainToInstance(AircallCallDto, event.data)
    const validateDto = await this.validationDto(aircallCallDto)

    if (false === validateDto) {
      return
    }

    const callModel = await this.queryBus.execute(
      new FindCallByReferenceIdQuery(
        event.organizationId,
        aircallCallDto.data.id.toString()
      )
    )

    if (!callModel) {
      this.logger.warn(
        `CallModel with id %s not found`,
        aircallCallDto.data.id.toString()
      )
      return
    }

    if (callModel.status !== aircallCallDto.data.status) {
      callModel.status = aircallCallDto.data.status
    }

    if (callModel.answeredAt !== aircallCallDto.data.answeredAt) {
      callModel.answeredAt = aircallCallDto.data.answeredAt
    }

    if (callModel.endedAt !== aircallCallDto.data.endedAt) {
      callModel.endedAt = aircallCallDto.data.endedAt
    }

    if (callModel.missedCallReason !== aircallCallDto.data.missedCallReason) {
      callModel.missedCallReason = aircallCallDto.data.missedCallReason
      const missedCallEvent = new MissedCallEvent()
      missedCallEvent.callId = callModel.id
      missedCallEvent.contactId = callModel.contactId
      missedCallEvent.phonumber = callModel.from
      missedCallEvent.organizationId = callModel.organizationId
      missedCallEvent.callDate = callModel.startedAt

      missedCallEvent.reason = callModel.missedCallReason
      this.eventEmitter.emit(CallEvents.CALL_MISSED, missedCallEvent)
    }

    if (
      null !== aircallCallDto.data.recording &&
      null === callModel.recordingLink
    ) {
      this.eventBus.publish(
        new RecordingLinkAvailableEvent(
          callModel.id,
          aircallCallDto.data.recording,
          callModel.organizationId
        )
      )
    }

    await this.commandBus.execute(new UpdateCallCommand(callModel))
  }

  private async validationDto(
    aircallCallDto: AircallCallDto
  ): Promise<boolean> {
    const validationErrors = await validate(aircallCallDto)

    if (validationErrors.length > 0) {
      this.logger.error(
        { validationErrors },
        'Error while validating AircallCallDto'
      )
      return false
    }

    return true
  }
}
