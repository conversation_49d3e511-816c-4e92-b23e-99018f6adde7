import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { InjectQueue } from '@nestjs/bull'
import { Queue } from 'bull'
import { MailEvents } from '../../../../shared/domain/event/event.enum'
import { MessageEvent } from '../../../../shared/domain/event/mail/message.event'
import { GMAIL_MESSAGE_QUEUE } from '../../domain/global'
import { MessageProvider } from '../../../../mailer/domain/enum/message-provider.enum'

@Injectable()
export class MailCreatedListener {
  private readonly logger = new Logger(MailCreatedListener.name)

  constructor(
    @InjectQueue(GMAIL_MESSAGE_QUEUE)
    private readonly emailMessageQueue: Queue
  ) {}

  @OnEvent(MailEvents.MAIL_CREATED)
  async invoke(event: MessageEvent) {
    const messageModel = event.messageModel

    if (MessageProvider.GMAIL !== event.provider) {
      return
    }

    let jobId
    if (messageModel.sequenceContactId && messageModel.sequenceStepId) {
      // Prevent duplicates from sequence algo
      jobId = `${messageModel.sequenceContactId}-${messageModel.sequenceStepId}`
    }
    const job = await this.emailMessageQueue.add(
      {
        from: messageModel.from,
        messageModel: messageModel,
        addUnsubscribeLink: event.addUnsubscribeLink,
        addSignature: event.addSignature,
      },
      {
        jobId,
        priority: event.priority,
      }
    )
    const isFailed = await job.isFailed()
    if (isFailed) {
      // Job already exists with error, retry it.
      try {
        await job.retry()
      } catch (error) {
        this.logger.warn(error.message)
      }
    }
  }
}
