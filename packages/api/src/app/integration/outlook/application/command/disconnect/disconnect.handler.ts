import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { DisconnectCommand } from './disconnect.command'
import {OutlookAuthorizationService} from "../../service/outlook-authorization.service";

@CommandHandler(DisconnectCommand)
export class DisconnectHandler implements ICommandHandler<DisconnectCommand> {
  constructor(
    private readonly outlookAuthorizationService: OutlookAuthorizationService
  ) {}

  async execute(command: DisconnectCommand): Promise<boolean> {
    return await this.outlookAuthorizationService.disconnect(command.userModel.id)
  }
}
