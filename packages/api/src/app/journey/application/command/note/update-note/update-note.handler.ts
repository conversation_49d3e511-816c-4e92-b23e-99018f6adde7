import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { UpdateNoteCommand } from './update-note.command'
import {
  NOTE_REPOSITORY_INTERFACE,
  NoteRepositoryInterface,
} from '../../../../domain/note/model/note-repository.interface'
import { NoteModel } from '../../../../domain/note/model/note.model'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { NoteEvent } from '../../../../../shared/domain/event/note/note.event'
import { NoteEvents } from '../../../../../shared/domain/event/event.enum'

@CommandHandler(UpdateNoteCommand)
export class UpdateNoteHandler implements ICommandHandler<UpdateNoteCommand> {
  constructor(
    @Inject(NOTE_REPOSITORY_INTERFACE)
    readonly noteRepository: NoteRepositoryInterface,
    private eventEmitter: EventEmitter2
  ) {}

  async execute(updateNoteCommand: UpdateNoteCommand): Promise<NoteModel> {
    const { newValues, noteModel } = updateNoteCommand

    const newNoteModel: NoteModel = {
      ...noteModel,
      ...newValues,
    }

    const updateNoteModel = await this.noteRepository.update(newNoteModel)

    const noteEvent = new NoteEvent()
    noteEvent.noteModel = updateNoteModel
    this.eventEmitter.emit(NoteEvents.NOTE_UPDATED, noteEvent)

    return updateNoteModel
  }
}
