import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { DeleteTaskBySequenceContactCommand } from './delete-task-by-sequence-contact.command'

import { Inject } from '@nestjs/common'
import {
  TASK_REPOSITORY_INTERFACE,
  TaskRepositoryInterface,
} from '../../../../domain/task/model/task-repository.interface'

@CommandHandler(DeleteTaskBySequenceContactCommand)
export class DeleteTaskBySequenceContactHandler
  implements ICommandHandler<DeleteTaskBySequenceContactCommand>
{
  constructor(
    @Inject(TASK_REPOSITORY_INTERFACE)
    private readonly taskRepository: TaskRepositoryInterface
  ) {}

  async execute(command: DeleteTaskBySequenceContactCommand): Promise<void> {
    const { sequenceContactId } = command
    await this.taskRepository.deleteBySequenceContactIds([sequenceContactId])
  }
}
