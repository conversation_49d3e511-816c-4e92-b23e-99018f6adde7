import { Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'

import { SequenceEvents } from '../../../../shared/domain/event/event.enum'
import {
  TaskRepositoryInterface,
  TASK_REPOSITORY_INTERFACE,
} from '../../../domain/task/model/task-repository.interface'

@Injectable()
export class SequenceContactDeletedListener {
  constructor(
    @Inject(TASK_REPOSITORY_INTERFACE)
    private readonly taskRepository: TaskRepositoryInterface
  ) {}

  @OnEvent(SequenceEvents.SEQUENCE_CONTACT_DELETED)
  async handleSequenceContactDeletedEvent(event: {
    sequenceContactIds: string[]
  }) {
    if (event.sequenceContactIds.length) {
      await this.taskRepository.deleteBySequenceContactIds(
        event.sequenceContactIds
      )
    }
  }
}
