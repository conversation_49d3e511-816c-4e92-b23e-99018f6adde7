import { Module } from '@nestjs/common'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { CqrsModule } from '@nestjs/cqrs'
import { TypeOrmModule } from '@nestjs/typeorm'
import { TaskProfile } from './application/profile/task/task.profile'
import { TaskEntity } from './infrastructure/entity/task/task.entity'
import { TASK_REPOSITORY_INTERFACE } from './domain/task/model/task-repository.interface'
import { TaskRepository } from './infrastructure/repository/task/task.repository'
import { CreateTaskHandler } from './application/command/task/create-task/create-task.handler'
import { FindTaskHandler } from './application/query/task/find-task/find-task.handler'
import { TaskService } from './application/service/task/task.service'
import { FindTaskAssignHandler } from './application/query/task/find-task/find-task-assign.handler'
import { PaginationService } from '../shared/infrastructure/service/pagination.service'
import { FindNoteHandler } from './application/query/note/find-note/find-note.handler'
import { FindByUserAndOrganizationHandler } from './application/query/note/find-by-user-and-organization/find-by-user-and-organization.handler'
import { NoteProfile } from './application/profile/note/note.profile'
import { CreateNoteHandler } from './application/command/note/create-note/create-note.handler'
import { UpdateNoteHandler } from './application/command/note/update-note/update-note.handler'
import { DeleteNoteHandler } from './application/command/note/delete-note/delete-note.handler'
import { NOTE_REPOSITORY_INTERFACE } from './domain/note/model/note-repository.interface'
import { NoteRepository } from './infrastructure/repository/note/note.repository'
import { NoteEntity } from './infrastructure/entity/note/note.entity'
import { FindTasksByOrganizationAndAssignHandler } from './application/query/task/find-tasks-by-organization-and-assign/find-tasks-by-organization-and-assign.handler'
import { FindNextTaskByOrganizationAndAssignHandler } from './application/query/task/find-next-task-by-organization-and-assign/find-next-task-by-organization-and-assign.handler'
import { TaskSnoozedListener } from './application/listener/task/task-snoozed.listener'
import { FindNextTaskByOrganizationAndContactHandler } from './application/query/task/find-next-task-by-organization-and-contact/find-next-task-by-organization-and-contact.handler'
import { DeleteTaskAssignHandler } from './application/query/task/delete-task-assign/delete-task-assign.handler'
import { TaskUnassignedListener } from './application/listener/task/task-unassigned.listener'
import { TransferAssignTaskHandler } from './application/query/task/transfer-assign-task/transfer-assign-task.handler'
import { CallCreatedInboundListener } from './application/listener/call/call-created-inbound.listener'
import { UpcomingTasksCronService } from './application/service/task/upcoming-tasks-cron.service'
import { DeleteTaskBulkHandler } from './application/command/task/delete-task/delete-task-bulk.handler'
import { UpdateTaskBulkHandler } from './application/command/task/update-task-bulk/update-task-bulk.handler'
import { UpdateTaskHandler } from './application/command/task/update-task/update-task.handler'
import { CountDailyTasksByAssignHandler } from './application/query/task/count-daily-tasks-by-assign/count-daily-tasks-by-assign.handler'
import { CountTasksCompletedHandler } from './application/query/task/count-tasks-completed/count-tasks-completed.handler'
import { DeleteTaskHandler } from './application/command/task/delete-task/delete-task.handler'
import { SequenceContactDeletedListener } from './application/listener/sequence/sequence-contact-deleted.listener'
import { DeleteTaskBySequenceContactHandler } from './application/command/task/delete-task/delete-task-by-sequence-contact.handler'
const profiles = [TaskProfile, NoteProfile]

const commandHandlers = [
  CreateTaskHandler,
  DeleteTaskHandler,
  DeleteTaskBulkHandler,
  UpdateTaskBulkHandler,
  UpdateTaskHandler,
  CreateNoteHandler,
  UpdateNoteHandler,
  DeleteNoteHandler,
  DeleteTaskBySequenceContactHandler,
]

const queryHandlers = [
  FindTaskHandler,
  FindTaskAssignHandler,
  FindTasksByOrganizationAndAssignHandler,
  FindNextTaskByOrganizationAndAssignHandler,
  FindNoteHandler,
  FindByUserAndOrganizationHandler,
  FindNextTaskByOrganizationAndContactHandler,
  DeleteTaskAssignHandler,
  TransferAssignTaskHandler,
  CountDailyTasksByAssignHandler,
  CountTasksCompletedHandler,
]

const services = [TaskService, PaginationService, UpcomingTasksCronService]

const listeners = [
  TaskSnoozedListener,
  TaskUnassignedListener,
  CallCreatedInboundListener,
  SequenceContactDeletedListener,
]

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    TypeOrmModule.forFeature([TaskEntity, NoteEntity]),
    CqrsModule,
  ],
  providers: [
    {
      provide: TASK_REPOSITORY_INTERFACE,
      useClass: TaskRepository,
    },
    {
      provide: NOTE_REPOSITORY_INTERFACE,
      useClass: NoteRepository,
    },
    ...profiles,
    ...commandHandlers,
    ...queryHandlers,
    ...services,
    ...listeners,
  ],
})
export class JourneyModule {}
