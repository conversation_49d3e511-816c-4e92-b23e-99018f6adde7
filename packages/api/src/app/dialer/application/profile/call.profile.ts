import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { CallModel } from '../../domain/model/call.model'
import { CallEntity } from '../../infrastructure/entity/call.entity'

@Injectable()
export class CallProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, CallModel, CallEntity)
      createMap(mapper, CallEntity, CallModel)
    }
  }
}
