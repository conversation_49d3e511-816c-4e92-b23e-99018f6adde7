import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { PubSubService } from '../../../../shared/infrastructure/service/pub-sub.service'
import { TriggerDownloadRecordingCommand } from './trigger-download-recording.command'

@CommandHandler(TriggerDownloadRecordingCommand)
export class TriggerDownloadRecordingHandler
  implements ICommandHandler<TriggerDownloadRecordingCommand>
{
  constructor(private readonly pubSubService: PubSubService) {}
  async execute(command: TriggerDownloadRecordingCommand): Promise<any> {
    return this.pubSubService.publishMessage(
      `${process.env.ENVIRONMENT}-recording-download-topic`,
      JSON.stringify({
        ...command,
      })
    )
  }
}
