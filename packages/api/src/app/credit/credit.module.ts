import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { TypeOrmModule } from '@nestjs/typeorm'
import { CreditTransactionService } from './application/service/credit-transaction.service'
import { CREDIT_TRANSACTION_REPOSITORY_INTERFACE } from './domain/interface/credit-transaction-repository.interface'
import { CreditTransactionEntity } from './infrastructure/entity/credit-transaction.entity'
import { CreditTransactionRepository } from './infrastructure/repository/credit-transaction.repository'
import { CreditTransactionProfile } from './application/profile/credit-transaction.profile'
import { CreateCreditTransactionHandler } from './application/command/create-credit-transaction/create-credit-transaction.handler'
import { UpdateCreditTransactionHandler } from './application/command/update-credit-transaction/update-credit-transaction.handler'
import { FindTransactionsByOrganizationHandler } from './application/query/find-transactions-by-organization/find-transactions-by-organization.handler'
import { FindTransactionsByOrganizationAndResourceIdHandler } from './application/query/find-transactions-by-organization-and-resource-id/find-transactions-by-organization-and-resource-id.handler'
import { AddCreditHandler } from './application/command/add-credit/add-credit.handler'
import { FindTransactionsByOrganizationAndResourcesIdHandler } from './application/query/find-transactions-by-organization-and-resources-id/find-transactions-by-organization-and-resources-id.handler'
import { CREDIT_TRANSACTION_SERVICE_INTERFACE } from './domain/interface/credit-transaction-service.interface'

const commandHandlers = [
  CreateCreditTransactionHandler,
  UpdateCreditTransactionHandler,
  AddCreditHandler,
]

const queryHandlers = [
  FindTransactionsByOrganizationHandler,
  FindTransactionsByOrganizationAndResourceIdHandler,
  FindTransactionsByOrganizationAndResourcesIdHandler,
]

const mapperProfiles = [CreditTransactionProfile]

const services = [
  {
    provide: CREDIT_TRANSACTION_SERVICE_INTERFACE,
    useClass: CreditTransactionService,
  },
]

@Module({
  imports: [TypeOrmModule.forFeature([CreditTransactionEntity]), CqrsModule],
  providers: [
    {
      provide: CREDIT_TRANSACTION_REPOSITORY_INTERFACE,
      useClass: CreditTransactionRepository,
    },
    ...services,
    ...commandHandlers,
    ...queryHandlers,
    ...mapperProfiles,
  ],
  exports: [CREDIT_TRANSACTION_SERVICE_INTERFACE],
})
export class CreditModule {}
