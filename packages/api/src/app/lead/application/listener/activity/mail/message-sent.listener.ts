import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { ActivityModel } from '../../../../domain/model/activity.model'
import { CreateActivityCommand } from '../../../command/activity/create-activity/create-activity.command'
import { ActivityType } from '../../../../domain/activity-type.enum'
import { FindContactQuery } from '../../../query/find-contact/find-contact.query'
import { MessageEvent } from '../../../../../shared/domain/event/mail/message.event'
import { MessageActivityModel } from '../../../../../shared/domain/model/activity/mail.model'
import { MessageDirection } from '../../../../../mailer/domain/enum/message-direction.enum'
import { BaseEvent } from '@getheroes/shared'
import { WebSocketService } from '../../../../../shared/infrastructure/service/websocket.service'
import { MailEvents } from '../../../../../shared/domain/event/event.enum'

@Injectable()
export class MessageSentListener {
  private readonly logger = new Logger(MessageSentListener.name)

  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    private readonly websocketService: WebSocketService
  ) {}

  @OnEvent(MailEvents.MAIL_SENT)
  async handleMailSentEvent(event: MessageEvent) {
    const messageModel = event.messageModel

    const contactModel = await this.queryBus.execute(
      new FindContactQuery(messageModel.contactId)
    )

    if (!messageModel.createdBy || !contactModel) {
      this.logger.warn(
        `User "${event.messageModel.createdBy.id}" or contact "${event.messageModel.to}" not found`
      )

      return
    }

    if (messageModel.isFromSequence()) {
      return
    }

    const messageActivityModel = new MessageActivityModel()
    messageActivityModel.status = event.messageModel.status
    messageActivityModel.subject = event.messageModel.subject
    messageActivityModel.body = event.messageModel.body
    messageActivityModel.attachments = event.messageModel.attachments
    messageActivityModel.userId = messageModel.createdBy.id
    messageActivityModel.userName = messageModel.createdBy.getFullName()
    messageActivityModel.contactId = contactModel.id
    messageActivityModel.contactName = contactModel.fullNameDisplay
    messageActivityModel.companyName = contactModel.company.name
    messageActivityModel.messageId = event.messageModel.id
    messageActivityModel.contactEmail =
      event.messageModel.direction === MessageDirection.OUTBOUND
        ? event.messageModel.to[0]
        : event.messageModel.from
    messageActivityModel.direction = event.messageModel.direction

    const mailerOutboundMessageType = event.isReply
      ? ActivityType.MAILER_MESSAGE_REPLY_SENT
      : ActivityType.MAILER_MESSAGE_SENT

    const activityModel = new ActivityModel()
    activityModel.resourceId = event.messageModel.id
    activityModel.organizationId = event.messageModel.organizationId
    activityModel.contactId = contactModel.id
    activityModel.payload = messageActivityModel
    activityModel.type =
      event.messageModel.direction === MessageDirection.OUTBOUND
        ? mailerOutboundMessageType
        : ActivityType.MAILER_MESSAGE_RECEIVED

    await this.commandBus.execute(new CreateActivityCommand(activityModel))

    const baseEvent = new BaseEvent(MailEvents.MAIL_SENT, event.messageModel.id)

    await this.websocketService.send(
      event.messageModel.createdBy.id,
      JSON.stringify(baseEvent)
    )
  }
}
