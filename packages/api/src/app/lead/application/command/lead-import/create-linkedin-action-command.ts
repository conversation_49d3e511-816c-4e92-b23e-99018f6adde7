import { Command } from '@nestjs/cqrs'
import { LinkedinActionType } from '../../../domain/object/linkedin-action/linkedin-action-type'

export class CreateLinkedinActionCommand extends Command<void> {
  constructor(
    readonly organizationId: string,
    readonly userId: string,
    readonly leadImportId: string,
    readonly url: string,
    readonly limit: number | undefined,
    readonly type: LinkedinActionType
  ) {
    super()
  }
}
