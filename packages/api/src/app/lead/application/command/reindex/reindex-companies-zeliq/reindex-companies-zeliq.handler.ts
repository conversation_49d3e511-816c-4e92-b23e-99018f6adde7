import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { Inject, Logger } from '@nestjs/common'
import { ReindexCompaniesZeliqCommand } from './reindex-companies-zeliq.command'
import {
  COMPANY_ZELIQ_REPOSITORY_INTERFACE,
  CompanyZeliqRepositoryInterface,
} from '../../../../domain/model/company-zeliq-repository.interface'
import { IndexCompanyZeliqService } from '../../../service/enrichment/index-company-zeliq.service'
import { LeadIndexOperation } from '@getheroes/shared'
import { CacheService } from '../../../../../shared/infrastructure/service/cache.service'

const LIMIT_PER_PAGE = 100
const REINDEXING_KEY = 'reindex_companies_zeliq_processing'
const TTL = 60 * 60 // 1 hour TTL in seconds

@CommandHandler(ReindexCompaniesZeliqCommand)
export class ReindexCompaniesZeliqHandler
  implements ICommandHandler<ReindexCompaniesZeliqCommand>
{
  private readonly logger = new Logger(ReindexCompaniesZeliqHandler.name)

  constructor(
    @Inject(COMPANY_ZELIQ_REPOSITORY_INTERFACE)
    private companyZeliqRepository: CompanyZeliqRepositoryInterface,
    private indexCompanyZeliqService: IndexCompanyZeliqService,
    private cacheService: CacheService
  ) {}

  async execute(command: ReindexCompaniesZeliqCommand): Promise<void> {
    const { iterations, batchSize } = command

    const totalCompaniesZeliq =
      await this.companyZeliqRepository.countReindexRequired()
    this.logger.log(`Total Zeliq companies to reindex: ${totalCompaniesZeliq}`)

    if (totalCompaniesZeliq === 0) {
      this.logger.log('No Zeliq companies require reindexing. Exiting process.')
      return
    }

    // Check if reindexing is already running
    const isProcessing = await this.cacheService.get(REINDEXING_KEY)
    if (isProcessing === 'true') {
      this.logger.warn(
        'Reindexing process for companiesZeliq is already running. Skipping.'
      )
      return
    }

    // Set reindexing key in cache to prevent duplicate execution
    await this.cacheService.set(REINDEXING_KEY, 'true', TTL)

    let totalProcessed = 0

    for (let iteration = 1; iteration <= iterations; iteration++) {
      try {
        const companyZeliqIds =
          await this.companyZeliqRepository.findIdsAndReindexRequired(
            batchSize ?? LIMIT_PER_PAGE
          )

        if (companyZeliqIds.length > 0) {
          await this.indexCompanyZeliqService.bulkIndex(
            companyZeliqIds,
            LeadIndexOperation.BULK_UPDATE
          )

          await this.companyZeliqRepository.updateBulkReindexRequired(
            companyZeliqIds,
            false
          )

          totalProcessed += companyZeliqIds.length
          this.logger.log(
            `Iteration ${iteration}: Processed ${companyZeliqIds.length} Zeliq companies. Total processed so far: ${totalProcessed}`
          )
        }

        if (companyZeliqIds.length < (batchSize ?? LIMIT_PER_PAGE)) {
          this.logger.log(
            'All required Zeliq companies reindexed. Stopping process.'
          )
          break
        }
      } catch (error) {
        this.logger.error(
          { err: error },
          `Error during Zeliq companies reindexing on iteration ${iteration}`
        )
      }
    }

    // Remove reindexing key from cache
    await this.cacheService.delete(REINDEXING_KEY)

    this.logger.log(
      `Reindexing process for Zeliq companies completed. Total processed: ${totalProcessed}`
    )
  }
}
