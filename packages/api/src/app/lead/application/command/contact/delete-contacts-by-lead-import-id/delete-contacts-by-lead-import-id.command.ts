import { IsUUID, validateSync } from 'class-validator'

export interface DeleteContactsByLeadImportIdCommandProps {
  leadImportId: string
  organizationId: string
}

export class DeleteContactsByLeadImportIdCommand {
  @IsUUID()
  private readonly leadImportId: string

  @IsUUID()
  private readonly organizationId: string

  constructor(readonly props: DeleteContactsByLeadImportIdCommandProps) {
    this.leadImportId = props.leadImportId
    this.organizationId = props.organizationId
    if (validateSync(this).length) {
      throw new Error('Invalid props for DeleteContactsByLeadImportIdCommand')
    }
  }
}
