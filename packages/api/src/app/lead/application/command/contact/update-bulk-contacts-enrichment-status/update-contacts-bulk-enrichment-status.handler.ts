import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { UpdateContactsBulkEnrichmentStatusCommand } from './update-contacts-bulk-enrichment-status.command'
import {
  CONTACT_REPOSITORY_INTERFACE,
  ContactRepositoryInterface,
} from '../../../../domain/model/contact-repository.interface'

import { validateOrReject } from 'class-validator'
import { PinoLogger } from 'nestjs-pino'

@CommandHandler(UpdateContactsBulkEnrichmentStatusCommand)
export class UpdateContactsBulkEnrichmentStatusHandler
  implements ICommandHandler<UpdateContactsBulkEnrichmentStatusCommand>
{
  constructor(
    private readonly logger: PinoLogger,
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    readonly contactRepository: ContactRepositoryInterface
  ) {}

  async execute(
    command: UpdateContactsBulkEnrichmentStatusCommand
  ): Promise<void> {
    try {
      await validateOrReject(command)

      await this.contactRepository.updateBulkEnrichmentData(
        command.contactIds,
        command.enrichmentStatus,
        command.enrichmentType
      )

      // TODO: dispatch WS event to update UI for those contacts
    } catch (error) {
      this.logger.error(`${this.constructor.name}: unexpected error`, {
        command,
        error,
      })
      throw error
    }
  }
}
