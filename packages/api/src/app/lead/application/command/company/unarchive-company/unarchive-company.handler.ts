import { Inject, NotFoundException } from '@nestjs/common'
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs'
import { UnarchiveCompanyCommand } from './unarchive-company.command'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../../domain/model/company-repository.interface'
import { IndexQueueService } from '../../../service/index-queue.service'
import { CompanyModel } from '../../../../domain/model/company.model'

@CommandHandler(UnarchiveCompanyCommand)
export class UnarchiveCompanyHandler
  implements ICommandHandler<UnarchiveCompanyCommand>
{
  constructor(
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    private companyRepository: CompanyRepositoryInterface,
    private indexQueueService: IndexQueueService
  ) {}

  async execute(command: UnarchiveCompanyCommand): Promise<CompanyModel> {
    const companyModel = await this.companyRepository.findByIdAndOrganizationId(
      command.organizationId,
      command.companyId,
      true
    )

    if (companyModel === null) {
      throw new NotFoundException(`Company ${command.companyId} not found`)
    }

    const companyModelUpdated =
      await this.companyRepository.unarchive(companyModel)

    this.indexQueueService
      .updateCompany(companyModel.id, null, true)
      .catch(error => {
        //TODO: log strategy
      })

    return companyModelUpdated
  }
}
