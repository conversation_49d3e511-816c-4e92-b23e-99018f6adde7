import { EnrichmentType, LeadSource } from '@getheroes/shared'
import { BadRequestException, ConflictException, Inject } from '@nestjs/common'
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { LeadEvents } from '../../../../../shared/domain/event/event.enum'
import { CompanyCreatedEvent } from '../../../../../shared/domain/event/lead/company-created-event'
import { FieldFamily } from '../../../../domain/field-family.enum'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../../domain/model/company-repository.interface'
import { CompanyModel } from '../../../../domain/model/company.model'
import { EnrichmentModel } from '../../../../domain/model/enrichment.model'
import { isValidDomain } from '../../../../domain/validator/is-domain.validator'
import { CompanyDomainService } from '../../../service/company-domain/company-domain.service'
import { CustomFieldService } from '../../../service/custom-field.service'
import { IndexQueueService } from '../../../service/index-queue.service'
import { PlanCompanyService } from '../../../service/plan/plan-company.service'
import { CreateEnrichmentCommand } from '../../enrichment/create-enrichment/create-enrichment.command'
import { CreateCompanyCommand } from './create-company.command'

@CommandHandler(CreateCompanyCommand)
export class CreateCompanyHandler
  implements ICommandHandler<CreateCompanyCommand>
{
  constructor(
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    private readonly companyRepository: CompanyRepositoryInterface,
    private readonly customFieldService: CustomFieldService,
    private readonly indexQueueService: IndexQueueService,
    private readonly commandBus: CommandBus,
    private readonly planCompanyService: PlanCompanyService,
    private readonly companyDomainService: CompanyDomainService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async execute(command: CreateCompanyCommand): Promise<CompanyModel> {
    command.companyModel.normalize()

    const domain =
      command.companyModel.domain ??
      (await this.companyDomainService.resolveDomainFrom({
        companyName: command.companyModel.name,
        companyUrl: command.companyModel.linkedinUrl,
      }))

    const companiesModel =
      await this.companyRepository.findByOrganizationAndNameOrDomain(
        command.companyModel.organizationId,
        command.companyModel.name,
        isValidDomain(domain) ? domain : null
      )

    //validate custom fields
    await this.customFieldService
      .validate(
        command.companyModel.organizationId,
        FieldFamily.COMPANY,
        command.companyModel.customFields
      )
      .catch(error => {
        throw new BadRequestException(error.message)
      })

    let companyModel: CompanyModel | undefined
    if (companiesModel.length > 0) {
      const existingEnabledCompany = companiesModel.find(
        company => company.availableInWorkspace
      )
      const existingDisabledCompany = companiesModel.find(
        company => !company.availableInWorkspace
      )
      if (existingDisabledCompany && !existingEnabledCompany) {
        companyModel = Object.assign(
          existingDisabledCompany,
          command.companyModel
        )
      } else {
        // TODO: see if exception or if we return company model
        throw new ConflictException(
          'you have already a company with the same name or domain',
          'company_exists'
        )
      }
    }
    // If disabled company exist, enable it, else create a company if none exists
    if (companyModel) {
      companyModel.availableInWorkspace = true
      await this.companyRepository.update(companyModel)
    } else {
      companyModel = await this.companyRepository.create(command.companyModel)
    }

    await this.planCompanyService.autoAssign(companyModel.organizationId, [
      companyModel,
    ])

    //index data
    this.indexQueueService.addCompany(
      companyModel.id,
      null,
      companyModel.source === LeadSource.MANUAL
    )

    //enrich
    const enrichmentModel = new EnrichmentModel()
    enrichmentModel.companiesIds = [companyModel.id]
    enrichmentModel.createdBy = companyModel.createdBy
    enrichmentModel.organizationId = companyModel.organizationId
    enrichmentModel.type = EnrichmentType.GENERAL
    this.commandBus.execute(new CreateEnrichmentCommand(enrichmentModel))

    this.eventEmitter.emit(
      LeadEvents.COMPANY_CREATED,
      new CompanyCreatedEvent(companyModel.id)
    )

    return companyModel
  }
}
