import { EnrichmentType } from '@getheroes/shared'
import { FindEnrichmentsFilters } from '../../../../domain/interface/enrichment/find-enrichments-filter.interface'

export class CountEnrichmentsByOrganizationAndUserQuery {
  constructor(
    public readonly organizationId: string,
    public readonly userId: string,
    public readonly types: EnrichmentType[],
    public readonly filters: FindEnrichmentsFilters
  ) {}
}
