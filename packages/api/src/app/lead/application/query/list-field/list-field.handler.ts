import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { ListFieldQuery } from './list-field.query'
import { FieldFamily } from '../../../domain/field-family.enum'
import { CompanyModel } from '../../../domain/model/company.model'
import { ContactModel } from '../../../domain/model/contact.model'
import { instanceToPlain } from 'class-transformer'
import { EXPOSE_GROUP } from '../../../../shared/globals/expose-group.enum'
import {
  customSortingOrdersByOjbect,
  sortablePropertiesByObject,
} from '../../../../shared/domain/decorator/field-is-sortable.decorator'
import { filterablePropertiesByObject } from '../../../../shared/domain/decorator/field-is-fiterable.decorator'
import { FieldKind } from '../../../../shared/domain/field-kind.enum'
import { validate, ValidationError } from 'class-validator'
import { unCapitalizeFirstLetter } from '../../../../../ui/api/shared/infrastructure/helper/cast.helper'
import {
  excludedFieldsPropertiesByObject,
  fieldTypesPropertiesByObject,
} from '../../../../shared/domain/decorator/field-definition.decorator'
import { FieldModel } from '../../../domain/model/field.model'
import { FieldType } from '../../../../shared/domain/field-type.enum'
import { editablePropertiesByObject } from '../../../../shared/domain/decorator/field-is-editable.decorator'
import { Inject } from '@nestjs/common'
import {
  CUSTOM_FIELD_REPOSITORY_INTERFACE,
  CustomFieldRepositoryInterface,
} from '../../../domain/model/custom-field-repository.interface'

@QueryHandler(ListFieldQuery)
export class ListFieldHandler implements IQueryHandler<ListFieldQuery> {
  constructor(
    @Inject(CUSTOM_FIELD_REPOSITORY_INTERFACE)
    private customFieldRepository: CustomFieldRepositoryInterface
  ) {}

  getParentClassNames(model: object) {
    let result = []
    const parent = Object.getPrototypeOf(Object.getPrototypeOf(model))
    if (parent && parent.constructor && parent.constructor.name !== 'Object') {
      const parentInstance = new parent.constructor()
      result = [
        parent.constructor.name,
        ...this.getParentClassNames(parentInstance),
      ]
    }
    return result
  }
  getFieldsProperties(properties: object, classNames: string[]) {
    let result = []
    for (let i = 0; i < classNames.length; i++) {
      const className = classNames[i]
      if (properties[className] && Array.isArray(properties[className])) {
        result = result.concat(properties[className])
      }
    }

    return result
  }

  getFieldsPropertiesAsDictionary(properties: object, classNames: string[]) {
    let result = {}
    for (let i = 0; i < classNames.length; i++) {
      const className = classNames[i]
      if (properties[className] && typeof properties[className] === 'object') {
        result = { ...result, ...properties[className] }
      }
    }

    return result
  }

  async fieldFromModel(
    model: object,
    parenField?: FieldModel,
    parentJsonPath?: string
  ) {
    let groups = [EXPOSE_GROUP.PUBLIC]
    if (parenField) {
      groups = [EXPOSE_GROUP.LIST_FIELD_RELATION]
    }
    const fields = instanceToPlain(model, {
      groups,
    })

    //get parent class name (example BaseModel)
    const parentClassNames = this.getParentClassNames(model)
    const classNames = [model.constructor.name, ...parentClassNames]

    const sortableFields = this.getFieldsProperties(
      sortablePropertiesByObject,
      classNames
    )

    const customSortableFields = this.getFieldsPropertiesAsDictionary(
      customSortingOrdersByOjbect,
      classNames
    )

    const filterableFields = this.getFieldsProperties(
      filterablePropertiesByObject,
      classNames
    )
    const editableFields = this.getFieldsProperties(
      editablePropertiesByObject,
      classNames
    )
    const excludedFields = this.getFieldsProperties(
      excludedFieldsPropertiesByObject,
      classNames
    )

    let typeFields = {}
    for (let i = 0; i < classNames.length; i++) {
      const parentClassName = classNames[i]
      if (fieldTypesPropertiesByObject[parentClassName]) {
        typeFields = {
          ...typeFields,
          ...fieldTypesPropertiesByObject[parentClassName],
        }
      }
    }

    const validationErrors = await validate(model)
    const formatFields = {}
    validationErrors.map(
      (error: ValidationError) =>
        (formatFields[error.property] = Object.keys(
          error.constraints
        )[0].replace('is', ''))
    )

    let fieldsModel: FieldModel[] = []
    const fieldsKeys = Object.keys(fields)

    for (let i = 0; i < fieldsKeys.length; i++) {
      const field = fieldsKeys[i] //exposed field name
      let fieldPropertyName = fieldsKeys[i] //real field name (not exposed) in the model

      //use the real field name instead of the exposed name
      const typeFieldsKeys = Object.keys(typeFields)
      for (let j = 0; j < typeFieldsKeys.length; j++) {
        const typeFieldsKey = typeFieldsKeys[j]
        const typeField = typeFields[typeFieldsKey]
        if (typeField.exposedName === field) {
          fieldPropertyName = typeFieldsKey
          break
        }
      }

      //build jsonPath
      let jsonPath = parentJsonPath || '$'
      jsonPath = `${jsonPath}.${field}`

      if (excludedFields.includes(field)) {
        continue
      }
      const typeProps = typeFields[fieldPropertyName] || {}
      const type = typeProps.type

      const parentKey = parenField ? parenField.id : undefined
      const fieldModel = new FieldModel({
        id: `${parentKey ? `${parentKey}.` : ''}${field}`,
        name: `${parentKey ? `${parentKey}.` : ''}${field}`,
        sortable: sortableFields.includes(fieldPropertyName),
        filterable: parenField
          ? parenField.filterable &&
            filterableFields.includes(fieldPropertyName)
          : filterableFields.includes(fieldPropertyName),
        jsonPath: jsonPath,
        format: formatFields[field]
          ? unCapitalizeFirstLetter(formatFields[field])
          : undefined,
        kind: FieldKind.STANDARD,
        editable: editableFields.includes(fieldPropertyName),
        type: type,
        customSortingOrder: Object.keys(customSortableFields).includes(
          fieldPropertyName
        )
          ? customSortableFields[fieldPropertyName]
          : undefined,
      })

      //get fields from object recursively
      if (
        (type === FieldType.OBJECT || type === FieldType.ARRAY) &&
        typeProps.typeClass
      ) {
        if (type === FieldType.ARRAY) {
          jsonPath = `${jsonPath}[*]`
        }

        const recursiveFieldsModel = await this.fieldFromModel(
          new typeProps.typeClass(),
          fieldModel,
          jsonPath
        )
        fieldsModel = fieldsModel.concat(recursiveFieldsModel)
        continue
      }
      fieldsModel.push(fieldModel)
    }
    return fieldsModel
  }

  async getCustomFields(organizationId: string, fieldFamily: FieldFamily) {
    const customFields =
      await this.customFieldRepository.findByOrganizationIdAndFamily(
        organizationId,
        fieldFamily
      )

    return customFields.map(customField => {
      return new FieldModel({
        id: customField.fieldId,
        name: customField.fieldLabel,
        sortable: customField.isSortable,
        filterable: true,
        jsonPath: `$.${customField.fieldId}`,
        format: customField.fieldFormat,
        kind: FieldKind.CUSTOM,
        editable: customField.isEditable,
        type: customField.fieldType,
      })
    })
  }

  async execute(query: ListFieldQuery): Promise<FieldModel[]> {
    let model: object
    if (query.family === FieldFamily.COMPANY) {
      model = new CompanyModel()
    } else {
      model = new ContactModel()
    }

    const fieldsFromModel = await this.fieldFromModel(model)
    if (query.kind === FieldKind.STANDARD) {
      return fieldsFromModel
    } else {
      const customFields = await this.getCustomFields(
        query.organizationId,
        query.family
      )
      //custom only
      if (query.kind === FieldKind.CUSTOM) {
        return customFields
      } else {
        //all
        return [...fieldsFromModel, ...customFields]
      }
    }
  }
}
