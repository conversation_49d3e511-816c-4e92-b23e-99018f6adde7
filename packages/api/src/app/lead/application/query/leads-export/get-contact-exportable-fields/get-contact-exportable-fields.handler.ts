import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs'
import {
  GetContactExportableFieldsQuery,
  GetContactExportableFieldsResponse,
} from './get-contact-exportable-fields.query'

import { LogFeature } from '../../../../../shared/logger.service'
import { TryCatchLogger } from '../../../../../shared/domain/decorator/try-catch-logger.decorator'
import { validateOrReject } from 'class-validator'
import {
  contactExportableFieldsConfig,
  defaultCompanyFieldsForContactExports,
} from './exportable-contact-fields.config'
import { companyExportableFieldsConfig } from '../get-company-exportable-fields/exportable-company-fields.config'

import { LeadExportableField } from '../../../../domain/interface/lead-export.interface'
import { FieldKind } from '../../../../../shared/domain/field-kind.enum'
import {
  LeadFieldExportCategoryEnum,
  LeadImportContextEnum,
} from '@getheroes/shared'
import { Inject } from '@nestjs/common'
import {
  CONTACT_REPOSITORY_INTERFACE,
  ContactRepositoryInterface,
} from '../../../../domain/model/contact-repository.interface'
import { SearchQueryDto } from '../../../../../../ui/api/shared/infrastructure/dto/search-query.dto'
import { GetOneLeadImportByIdQuery } from '../../../../../lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'

@QueryHandler(GetContactExportableFieldsQuery)
export class GetContactExportableFieldsHandler
  implements IQueryHandler<GetContactExportableFieldsQuery>
{
  constructor(
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    private readonly contactRepository: ContactRepositoryInterface,
    private readonly queryBus: QueryBus
  ) {}

  @TryCatchLogger({
    feature: LogFeature.LEAD_EXPORT,
    message: 'Failed to get exportable contact fields',
  })
  async execute(
    query: GetContactExportableFieldsQuery
  ): Promise<GetContactExportableFieldsResponse> {
    await validateOrReject(query)
    const { role, searchDto, organizationId } = query

    let exportableFields = [...contactExportableFieldsConfig]

    // add all company-exportable fields when exporting contacts
    exportableFields.push(
      ...companyExportableFieldsConfig.map(field => ({
        ...field,
        id: `company.${field.id}`,
        jsonPath: `$.company.${field.jsonPath.replace('$.', '')}`,
        isDefault: defaultCompanyFieldsForContactExports.includes(field.id),
      }))
    )

    if (role) {
      exportableFields = exportableFields.filter(
        field => !field.permissions || field.permissions.includes(role)
      )
    }

    const exportableCategories = Array.from(
      new Set(
        exportableFields.map((field: LeadExportableField) => field.category)
      )
    )

    if (searchDto) {
      const customFields = await this.getCustomFields(searchDto, organizationId)

      if (customFields.length > 0) {
        exportableFields.push(...customFields)

        if (
          !exportableCategories.includes(
            LeadFieldExportCategoryEnum.CUSTOM_FIELDS
          )
        ) {
          exportableCategories.push(LeadFieldExportCategoryEnum.CUSTOM_FIELDS)
        }
      }
    }

    return {
      exportableFields,
      exportableCategories,
    }
  }

  private async getCustomFields(
    searchDto: SearchQueryDto,
    organizationId: string
  ): Promise<LeadExportableField[]> {
    const customFields: string[] = []

    const leadImportId = searchDto?.filters?.find(
      filter => filter.field === 'leadImportIds'
    )?.value

    if (!leadImportId) {
      return []
    }
    const leadImportModel = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(leadImportId, organizationId)
    )
    if (leadImportModel.context === LeadImportContextEnum.ENRICHMENT_HUB) {
      const hubCustomFields =
        await this.contactRepository.getUniqueCustomFieldKeys(
          leadImportId,
          organizationId
        )
      customFields.push(...hubCustomFields)
    }

    return customFields.map(field => ({
      id: field,
      kind: FieldKind.CUSTOM,
      category: LeadFieldExportCategoryEnum.CUSTOM_FIELDS,
      isDefault: false,
      order: 3,
    }))
  }
}
