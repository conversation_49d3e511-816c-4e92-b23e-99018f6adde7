import { v4 } from 'uuid'

type IdField<T> = {
  [K in keyof T]: T[K] extends string | number ? K : never
}[keyof T]

export class BatchCreator<T> {
  private readonly groups: Map<string, T[]> = new Map<string, T[]>()

  public constructor(
    private readonly items: T[],
    private readonly identifiers: IdField<T>[]
  ) {
    this.createGroups()
  }

  private findGroupFrom(item: T) {
    const groupKey = [...this.groups.keys()].find(
      groupKey => this.findItemOccurrence(item, groupKey) != null
    )

    return groupKey
  }

  private findItemOccurrence(item: T, key: string) {
    const groupItems = this.groups.get(key) ?? []
    return groupItems.find(groupItem =>
      this.identifiers
        .filter(
          identifier =>
            item[identifier] != null || groupItem[identifier] != null
        )
        .some(identifier => {
          const itemValue = item[identifier]
          const groupItemValue = groupItem[identifier]

          if (itemValue == null && groupItemValue != null) {
            return false
          }
          if (itemValue != null && groupItemValue == null) {
            return false
          }
          return itemValue === groupItemValue
        })
    )
  }

  private createGroups() {
    for (const item of this.items) {
      const groupKey = this.findGroupFrom(item)
      if (groupKey == null) {
        this.groups.set(v4(), [item])
        continue
      }

      const groupItems = this.groups.get(groupKey) ?? []
      this.groups.set(groupKey, [...groupItems, item])
    }
  }

  private get hasRemainingItems() {
    return [...this.groups.values()].some(group => group.length > 0)
  }

  public createFor(size: number) {
    const batches: T[][] = []

    while (this.hasRemainingItems) {
      const batch: T[] = []
      const usedGroups = new Set<string | number>()

      for (const [id, group] of this.groups) {
        if (group.length > 0 && !usedGroups.has(id)) {
          batch.push(group.shift())
          usedGroups.add(id)
        }

        if (batch.length === size) break
      }

      if (batch.length > 0) batches.push(batch)
      else break
    }

    return batches
  }
}
