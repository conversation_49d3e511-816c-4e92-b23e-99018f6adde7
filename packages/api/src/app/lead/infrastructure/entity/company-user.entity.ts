import { AutoMap } from '@automapper/classes'
import {
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
} from 'typeorm'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { CompanyEntity } from './company.entity'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'

@Entity({ name: 'companies_users' })
@Index(['company.id', 'user.id'], { unique: true })
@Index('idx_companies_users_user_id_company_id_id', [
  'user.id',
  'company.id',
  'id',
])
export class CompanyUserEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @ManyToOne(() => CompanyEntity, company => company.assignUsers, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn({ name: 'company_id' })
  @AutoMap(() => CompanyEntity)
  company: Relation<CompanyEntity>

  @ManyToOne(() => UserEntity, { nullable: false })
  @JoinColumn({ name: 'user_id' })
  @AutoMap(() => UserEntity)
  user: UserEntity

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity
}
