import { ApiPropertyOptional } from '@nestjs/swagger'
import { AutoMap } from '@automapper/classes'

export class MetricValuesObject {
  constructor(values: {
    min?: string | number
    max?: string | number
    avg?: string | number
    current?: string | number
  }) {
    if ('max' in values) {
      this.max = this.castToFloat(values.max)
    }

    if ('min' in values) {
      this.min = this.castToFloat(values.min)
    }

    if ('avg' in values) {
      this.avg = this.castToFloat(values.avg)
    }

    if ('current' in values) {
      this.current = this.castToFloat(values.current)
    }
  }

  castToFloat(value): number | null {
    let castedValue = null
    if (typeof value === 'number') castedValue = Number(value.toFixed(2))
    if (typeof value === 'string' && value !== '')
      castedValue = Number(Number(value).toFixed(2))
    if (typeof castedValue !== 'number' || Number.isNaN(castedValue)) {
      return null
    }
    return castedValue
  }

  @ApiPropertyOptional({ nullable: true })
  @AutoMap()
  min?: number | null

  @ApiPropertyOptional({ nullable: true })
  @AutoMap()
  max?: number | null

  @ApiPropertyOptional({ nullable: true })
  @AutoMap()
  avg?: number | null

  @ApiPropertyOptional({ nullable: true })
  @AutoMap()
  current?: number | null
}
