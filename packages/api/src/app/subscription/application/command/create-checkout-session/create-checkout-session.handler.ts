import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryBus } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'

import { CreateCheckoutSessionCommand } from './create-checkout-session.command'
import {
  PAYMENT_PROVIDER_SERVICE_INTERFACE,
  PaymentProviderServiceInterface,
} from '../../../domain/payment-provider-service.interface'
import { FindSubscriptionsByOrganizationIdQuery } from '../../query/find-subscriptions-by-organization-id/find-subscriptions-by-organization-id.query'
import { SubscriptionInterface } from '../../../../shared/domain/model/subscription/subscription.interface'
import { OrganizationSubscriptionStatus } from '../../../../shared/domain/organization-subscription-status.enum'
import { OrganizationServiceSubscriptionPlan } from '@getheroes/shared'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'

@CommandHandler(CreateCheckoutSessionCommand)
export class CreateCheckoutSessionHandler
  implements ICommandHandler<CreateCheckoutSessionCommand>
{
  constructor(
    private queryBus: QueryBus,
    @Inject(PAYMENT_PROVIDER_SERVICE_INTERFACE)
    private readonly paymentProviderService: PaymentProviderServiceInterface
  ) {}

  private logger = new LoggerService({
    context: CreateCheckoutSessionHandler.name,
    feature: LogFeature.SUBSCRIPTION,
  })

  @TryCatchLogger({
    feature: LogFeature.SUBSCRIPTION,
    message: 'Failed to create checkout session',
  })
  async execute({
    organizationId,
    userId,
    createCheckoutSessionDto,
  }: CreateCheckoutSessionCommand): Promise<string> {
    const subscriptions = await this.queryBus.execute<
      FindSubscriptionsByOrganizationIdQuery,
      SubscriptionInterface[]
    >(new FindSubscriptionsByOrganizationIdQuery(organizationId))

    const activeOrganizationSubscriptions = subscriptions.some(
      subscription =>
        subscription.type === createCheckoutSessionDto.type &&
        subscription.status === OrganizationSubscriptionStatus.ACTIVE &&
        subscription.plan !== OrganizationServiceSubscriptionPlan.FREE &&
        subscription.isTrial === false
    )

    if (activeOrganizationSubscriptions) {
      throw new Error('Active subscription already exists')
    }

    const checkoutSessionId =
      await this.paymentProviderService.createCheckoutSession(
        organizationId,
        userId,
        createCheckoutSessionDto
      )

    this.logger.log({
      data: { organizationId, userId, createCheckoutSessionDto },
      message: 'Checkout session created',
    })

    return checkoutSessionId
  }
}
