# Subscription Module

## Overview

The Subscription module is the feature that enables organizations to subscribe to different service plans and purchase credits. This module handles the entire subscription lifecycle, from creating subscriptions to processing payments through Stripe integration, managing billing periods, and applying feature access based on subscription plans.

## Key Features

- Service subscription management (FREE, STARTER, ESSENTIAL, ADVANCED, ENTERPRISE plans)
- Credit subscription management (MOBILE plans)
- Stripe integration for payment processing
- Subscription lifecycle management (creation, updates, cancellation)
- Billing portal and checkout session creation
- Webhook handling for Stripe events
- Free trial and free plan management
- Subscription-based feature access control

## Architecture

The Subscription module follows a clean architecture pattern with:

- **Domain Layer**: Contains the core business logic, models, and interfaces
- **Application Layer**: Implements use cases through commands, queries, and event handlers
- **Infrastructure Layer**: Provides implementations for repositories and external services
- **UI Layer**: Exposes the functionality through REST API endpoints

### Key Components

#### Models

- `SubscriptionModel`: Core domain model representing a subscription
- `StripeWebhookEventModel`: Model for tracking and processing Stripe webhook events

#### Repositories

- `SubscriptionRepository`: Manages persistence of subscription entities
- `StripeWebhookEventRepository`: Manages persistence of Stripe webhook event entities

#### Services

- `StripeService`: Implements the payment provider interface for Stripe integration
- `FreeTrialCronService`: Manages free trial expirations
- `FreePlanCronService`: Manages free plan subscriptions

#### Command Handlers

- `CreateSubscriptionHandler`: Creates new subscriptions
- `UpdateSubscriptionHandler`: Updates existing subscriptions
- `HandleStripeEventHandler`: Processes Stripe webhook events
- `CreateCheckoutSessionHandler`: Creates Stripe checkout sessions
- `CancelSubscriptionsHandler`: Cancels subscriptions

#### Query Handlers

- `FindSubscriptionsByOrganizationIdHandler`: Retrieves subscriptions for an organization
- `FindSubscriptionByIdHandler`: Retrieves a specific subscription
- `GetActiveBillingPeriodHandler`: Determines the active billing period

#### Event Listeners

- `SubscriptionsUpdatedListener`: Notifies the frontend of subscription changes

## Subscription Types

The module supports two main subscription types:

1. **Service Subscriptions** (`OrganizationSubscriptionType.SERVICE`): Provide access to platform features
2. **Credit Subscriptions** (`OrganizationSubscriptionType.CREDIT`): Provide credits for usage-based features

Each organization can have one of these subscription types. An organization must at all times have a service subscription.

## Service Subscription Plans

Service subscriptions come in several plans, defined in `OrganizationServiceSubscriptionPlan`:

1. **FREE**: Basic plan with limited features
2. **STARTER**: Individual plan
3. **ESSENTIAL**: rarely used plan
4. **ADVANCED**: rarely used plan
5. **ENTERPRISE**: Custom plan

## Credit Subscription Plans

Credit subscriptions provide additional credits for specific features:

1. **MOBILE** (`OrganizationCreditSubscriptionPlan.MOBILE`): General-purpose credits
2. **EXPORT** (`OrganizationCreditSubscriptionPlan.EXPORT`): legacy, not used

## Subscription Intervals

Subscriptions can have different billing intervals:

1. **MONTH** (`OrganizationSubscriptionInterval.MONTH`): Monthly billing
2. **YEAR** (`OrganizationSubscriptionInterval.YEAR`): Annual billing (with discount)

## Subscription Status

The subscription status is tracked through `OrganizationSubscriptionStatus`:

1. **ACTIVE**: Subscription is active and in good standing
2. **INCOMPLETE**: Payment has failed but can be retried
3. **INCOMPLETE_EXPIRED**: Payment has failed and cannot be retried
4. **PAST_DUE**: Payment is past due
5. **CANCELED**: Subscription has been canceled
6. **UNPAID**: Subscription is unpaid

## Credits System

Credits are the currency used within the platform for various operations:

- Each service plan comes with a monthly allocation of credits
- Additional credits can be purchased through credit subscriptions
- Credits are consumed for operations like:
  - Email and phone enrichment
  - Lead personality generation
  - Other platform actions

The `CreditTransactionModel` tracks credit usage with:
- `operationType`: CREDIT (adding credits) or DEBIT (consuming credits)
- `cost`: The number of credits to add or remove from the organization
- `resourceName`: The feature using the credits
- `resourceId`: The specific resource using the credits

## Stripe Integration

The module integrates with Stripe for payment processing:

- `StripeService` implements the `PaymentProviderServiceInterface`
- Stripe webhooks are processed to update subscription status
- Checkout sessions are created for new subscriptions
- Billing portal sessions allow users to manage their subscriptions
- Stripe metadata is used to track organization and subscription details

### Webhook Processing Flow

1. Stripe sends a webhook event to the API
2. `ValidateStripeWebhookEventHandler` validates the event signature
3. `CreateStripeWebhookEventHandler` creates a record of the event
4. `HandleStripeEventHandler` routes the event to the appropriate handler:
   - `HandleStripeSubscriptionEventHandler` for subscription events
   - `HandleStripeInvoiceEventHandler` for invoice events
5. The appropriate handler updates the subscription status and organization features
6. Events arriving in the wrong order of processing are stored and processed later
7. Storing the events allow for duplicate prevention and debugging

## Free Trial and Free Plan

The module includes special handling for free trials and free plans:

- New organizations start with a free trial of the STARTER plan
- Free trial lasts for 14 days (`FREE_TRIAL_DAYS`)
- After the trial, organizations are downgraded to the FREE plan
- The FREE plan includes 50 credits per month (`CREDITS_ON_FREE_PLAN`)
- `FreeTrialCronService` manage these transitions
- `FreePlanCronService` renews credits for the FREE plan (as no stripe webhook is involved)

## Feature Access Control

Subscriptions determine feature access through integration with the Feature module:

- Each plan has associated feature gateways and limits
- When a subscription is created or updated, feature access is updated
- The `ApplyFeatureGatewayAndLimitsHandler` applies these changes

## API Endpoints

The module exposes several REST API endpoints:

- `POST /:organizationId/subscriptions/checkout`: Create a checkout session
- `POST /:organizationId/subscriptions/billing-portal`: Create a billing portal session
- `GET /:organizationId/subscriptions`: Get all subscriptions for an organization
- `GET /:organizationId/subscriptions/:id`: Get a specific subscription
- `POST /stripe/webhook`: Handle Stripe webhook events
- `GET /:organizationId/subscriptions/service/:subscriptionId/discounts`: Get upgrade discounts

## Data Structure and Relationships

The Subscription module uses several database tables to manage subscriptions and their relationships with other entities in the system.

### Database Schema

#### Main Tables

1. **organization_subscriptions**: Stores subscription information
   - Primary key: `id` (UUID)
   - Foreign keys:
     - `organization_id` → `organizations.id`
     - `created_by_id` → `users.id`
   - Key fields:
     - `type`: Type of subscription (SERVICE or CREDIT)
     - `plan`: Subscription plan (FREE, STARTER, etc.)
     - `status`: Current status of the subscription
     - `interval`: Billing interval (MONTH or YEAR)
     - `amount`: Price in cents
     - `previous_amount`: Previous price for tracking changes
     - `seats`: Number of seats for service subscriptions
     - `credits`: Number of credits allocated
     - `start_date`: When the subscription started
     - `current_period_start_date`: Start of current billing period
     - `current_period_end_date`: End of current billing period
     - `cancel_at`: When the subscription will be canceled
     - `is_trial`: Whether this is a trial subscription
     - `stripe_subscription_id`: ID of the subscription in Stripe

2. **stripe_webhook_event**: Stores Stripe webhook events
   - Primary key: `id` (UUID)
   - Key fields:
     - `stripe_event_id`: ID of the event in Stripe
     - `stripe_customer_id`: ID of the customer in Stripe
     - `organization_id`: ID of the organization
     - `event_type`: Type of Stripe event
     - `event`: JSON data of the event
     - `status`: Processing status of the event

#### Related Tables

1. **organizations**: Stores organization information
   - Relevant fields:
     - `stripe_customer_id`: ID of the customer in Stripe
     - `plan`: Current service subscription plan
     - `credit_balance`: Current credit balance
     - `credit_consumed`: Total credits consumed
     - `currency`: Billing currency

2. **credit_transactions**: Stores credit transaction history
   - Tracks credit additions and deductions
   - Links to organizations and subscriptions

### Entity Relationships

```
+----------------+       +----------------+
|                |       |                |
| organizations  |<------| subscriptions  |
|                |       |                |
+----------------+       +----------------+
        ^                        ^
        |                        |
        |                        |
        v                        |
+----------------+               |
|                |               |
|     users      |---------------+
|                |
+----------------+
        ^
        |
        v
+----------------+       +----------------+
|                |       |                |
|  organization  |       |     stripe     |
|    members     |       | webhook events |
|                |       |                |
+----------------+       +----------------+
                                 ^
                                 |
                                 v
                         +----------------+
                         |                |
                         |     credit     |
                         | transactions   |
                         |                |
                         +----------------+
```

### Key Relationships

1. **Subscription to Organization**: Each subscription belongs to one organization
   - An organization can have multiple subscriptions (one SERVICE and zero or one CREDIT)
   - The `organization_id` field in `organization_subscriptions` links to `organizations.id`

2. **Subscription to User**: Each subscription is created by one user
   - The `created_by_id` field in `organization_subscriptions` links to `users.id`

3. **Organization to Stripe**: Each organization has one Stripe customer
   - The `stripe_customer_id` field in `organizations` stores the Stripe customer ID
   - This links the organization to Stripe for billing purposes

4. **Subscription to Stripe**: Each subscription may have a Stripe subscription
   - The `stripe_subscription_id` field links to a subscription in Stripe
   - Free plans don't have a Stripe subscription ID

5. **Credit Transactions to Organization**: Credit transactions are linked to organizations
   - Credits are added when subscriptions are created or renewed
   - Credits are deducted when features are used

### Data Flow

1. **Subscription Creation**:
   - When a user subscribes to a plan, a record is created in `organization_subscriptions`
   - For paid plans, a Stripe checkout session is created
   - When payment is successful, Stripe sends a webhook event
   - The webhook event is processed and the subscription is activated

2. **Subscription Renewal**:
   - Stripe automatically renews subscriptions at the end of each billing period
   - Stripe sends webhook events for renewal
   - The system processes these events to update subscription dates and add credits

3. **Credit Management**:
   - When a subscription is created or renewed, credits are added to the organization
   - When features are used, credits are deducted
   - All credit operations are recorded in the `credit_transactions` table

4. **Plan Changes**:
   - When a user changes their plan, the subscription is updated
   - For upgrades, prorated charges may be applied
   - For downgrades, changes typically take effect at the end of the billing period

### Special Considerations

1. **Free Trial Handling**:
   - New organizations start with a trial of the STARTER plan
   - The `is_trial` field is set to `true`
   - The `FreeTrialCronService` monitors trial expirations
   - When a trial expires, the organization is downgraded to the FREE plan

2. **Free Plan Handling**:
   - Free plans don't have a Stripe subscription
   - The `FreePlanCronService` manages credit renewals for free plans

3. **Stripe Webhook Processing**:
   - All Stripe events are stored in the `stripe_webhook_event` table
   - This provides an audit trail and enables replay of events if needed
   - Events are processed in order to maintain data consistency

## Integration with Other Modules

The Subscription module integrates with several other modules:

1. **Organization Module**: Updates organization plan and credits
2. **Feature Module**: Controls feature access based on subscription plan
3. **Credit Module**: Manages credit allocations and transactions

## Error Handling

The module includes comprehensive error handling:

- `TryCatchLogger` decorator for error logging
- Validation of Stripe webhook events
- Transaction-based updates to ensure data consistency
- Retry mechanisms for failed operations

## Best Practices for Development

1. **Adding a New Subscription Plan**:
   - Add the plan to the appropriate enum (`OrganizationServiceSubscriptionPlan` or `OrganizationCreditSubscriptionPlan`)
   - Configure Stripe products and prices
   - Update the `stripe-prices-ids.ts` configuration
   - Update feature access rules for the new plan

2. **Handling Stripe Events**:
   - Use the `HandleStripeEventHandler` as the entry point
   - Create specific handlers for new event types
   - Ensure idempotency by checking for existing events
   - Use the `StripeEventMapper` to map Stripe events to domain models

3. **Error Handling**:
   - Use the `TryCatchLogger` decorator for error logging
   - Implement retries for transient failures
   - Store webhook events for debugging and replay

4. **Testing**:
   - Use mock Stripe events for testing
   - Test the complete subscription lifecycle
   - Verify feature access changes with subscription changes

## Troubleshooting

Common issues and their solutions.

[Use these awesome logs](https://app.datadoghq.eu/logs?query=env%3Aprod%20service%3A%28dashboard-api-service%20OR%20dashboard-consumer-service%20OR%20dashboard-cron-service%29%20-%22%2Fapi%2F%3A%22%20%40data.feature%3A%22Subscription%22&agg_m=count&agg_m_source=base&agg_q=status%2Cservice&agg_q_source=base%2Cbase&agg_t=count&clustering_pattern_field_path=message&cols=%40data.feature%2Ccontext%2C%40data.organizationId&fromUser=true&messageDisplay=inline&refresh_mode=sliding&sort_m=%2C&sort_m_source=%2C&sort_t=%2C&storage=hot&stream_sort=time%2Cdesc&top_n=10%2C10&top_o=top%2Ctop&viz=stream&x_missing=true%2Ctrue&from_ts=1742350999557&to_ts=1742379799557&live=true) to help you debug the issue.

1. **For plan and credit issues**:
   - Get the Stripe events in the database to understand the flow
   - (ask an AI to summarize the events for you)
   - Write an integration test to reproduce the issue using stripe events
   - fix the issue AFTER the test is written

3. **Feature access not reflecting subscription plan**:
   - Verify the subscription status is ACTIVE
   - Check if the feature gateways have been applied
   - Ensure the subscription plan is correctly mapped to features
