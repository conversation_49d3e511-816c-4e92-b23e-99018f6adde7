import { OrganizationModel } from '../../../organization/domain/organization.model'
import Stripe from 'stripe'
import { CreateSubscriptionDTO } from '../../domain/dto/create-subscription.dto'

export const STRIPE_EVENT_MAPPER_INTERFACE = 'STRIPE_EVENT_MAPPER_INTERFACE'

export interface StripeEventMapperInterface {
  buildCreateSubscriptionDTO(
    organizationModel: OrganizationModel,
    stripeSubscriptionData: Stripe.Subscription
  ): Promise<CreateSubscriptionDTO>
}
