import { AutoMap } from '@automapper/classes'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'
import { OrganizationInterface } from '../../../shared/domain/organization.interface'
import { OrganizationSubscriptionType } from '../../../shared/domain/organization-subscription-type.enum'
import { OrganizationCreditSubscriptionPlan } from '../../../shared/domain/organization-subscription-plan.enum'
import { OrganizationServiceSubscriptionPlan } from '@getheroes/shared'
import { OrganizationSubscriptionStatus } from '../../../shared/domain/organization-subscription-status.enum'
import { OrganizationSubscriptionInterval } from '../../../shared/domain/organization-subscription-interval.enum'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'

enum ServiceOrCreditSubscriptionPlanEnum {
  FREE = 'FREE',
  STARTER = 'STARTER',
  ESSENTIAL = 'ESSENTIAL',
  ADVANCED = 'ADVANCED',
  ENTERPRISE = 'ENTERPRISE',
  MOBILE = 'MOBILE',
  EXPORT = 'EXPORT',
}

@Entity({ name: 'organization_subscriptions' })
@Index('IDX_subscriptions_organization_id', ['organizationId'])
export class SubscriptionEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @ManyToOne('organizations')
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'FK_organization_subscripions_organization_id',
  })
  organization: OrganizationInterface

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string

  @ManyToOne(() => UserEntity)
  @JoinColumn({
    name: 'created_by_id',
    foreignKeyConstraintName:
      'FK_organization_subscriptions_created_by_user_id',
  })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity

  @Column({
    type: 'enum',
    enum: OrganizationSubscriptionType,
    enumName: 'organization_subscription_type_enum',
  })
  @AutoMap(() => String)
  type: OrganizationSubscriptionType

  @Column({
    type: 'enum',
    enum: ServiceOrCreditSubscriptionPlanEnum,
    enumName: 'organization_subscription_plan_enum',
  })
  @AutoMap(() => String)
  plan: OrganizationServiceSubscriptionPlan | OrganizationCreditSubscriptionPlan

  @Column({
    type: 'enum',
    enum: OrganizationSubscriptionStatus,
    enumName: 'organization_subscription_status_enum',
  })
  @AutoMap(() => String)
  status: OrganizationSubscriptionStatus

  @Column({
    type: 'enum',
    enum: OrganizationSubscriptionInterval,
    enumName: 'organization_subscription_interval_enum',
  })
  @AutoMap(() => String)
  interval: OrganizationSubscriptionInterval

  /* price in cents for current plan */
  @Column('integer')
  @AutoMap(() => Number)
  amount: number

  /* price in cents for previous plan, to track upgrade / downgrade delta */
  @Column('integer', { nullable: true, default: null })
  @AutoMap(() => Number)
  previousAmount: number

  @UpdateDateColumn({ type: 'timestamptz', nullable: true, default: null })
  @AutoMap()
  amountUpdatedAt: Date

  @Column('integer')
  @AutoMap(() => Number)
  seats: number

  @Column('integer')
  @AutoMap(() => Number)
  credits: number

  @Column('timestamptz')
  @AutoMap()
  startDate: Date

  @Column('timestamptz', { nullable: true })
  @AutoMap()
  currentPeriodStartDate: Date

  @Column('timestamptz', { nullable: true })
  @AutoMap()
  currentPeriodEndDate: Date

  @Column('timestamptz', { nullable: true })
  @AutoMap()
  cancelAt: Date

  @Column('boolean')
  @AutoMap(() => Boolean)
  isTrial: boolean

  @Column('varchar', { nullable: true })
  @AutoMap(() => String)
  stripeSubscriptionId: string
}
