import { AttachmentModel } from '../../../../mailer/domain/model/message.model'

export class MessageActivityModel {
  public status: string | null = null
  public subject: string | null = null
  public body: string | null = null
  public attachments: AttachmentModel[] | null = null
  public userId: string | null = null
  public userName: string | null = null
  public contactId: string | null = null
  public contactName: string | null = null
  public contactEmail: string | null = null
  public companyName: string | null = null
  public direction: string | null = null
  public messageId: string | null = null
  public nbClick: number | null = null
  public nbOpen: number | null = null
}
