import { IsUUID, validateSync } from 'class-validator'

export class LeadImportReviewLeadsExcludedEvent {
  @IsUUID()
  readonly organizationId: string

  @IsUUID()
  readonly leadImportId: string

  constructor(payload: { organizationId: string; leadImportId: string }) {
    this.organizationId = payload.organizationId
    this.leadImportId = payload.leadImportId

    if (validateSync(this).length) {
      throw new Error('Invalid event')
    }
  }
}
