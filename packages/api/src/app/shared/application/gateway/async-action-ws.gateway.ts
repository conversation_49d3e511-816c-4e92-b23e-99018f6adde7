import { Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'

import { BaseEvent, NameEvent } from '@getheroes/shared'
import { AsyncActionEvents } from '../../domain/event/event.enum'
import { AsyncActionUpdatedEvent } from '../../domain/event/async-action/async-action-updated.event'
import { AsyncActionCreatedEvent } from '../../domain/event/async-action/async-action-created.event'
import { WebSocketService } from '../../infrastructure/service/websocket.service'
import { validateOrReject } from 'class-validator'
import { TryCatchLogger } from '../../domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../logger.service'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../domain/interface/async-action-service.interface'

@Injectable()
export class AsyncActionWebsocketGateway {
  constructor(
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface,
    private readonly websocketService: WebSocketService
  ) {}

  @TryCatchLogger({
    message: 'Failed to send websocket notification',
    stopErrorPropagation: true,
    feature: LogFeature.ASYNC_ACTIONS,
  })
  @OnEvent(AsyncActionEvents.ASYNC_ACTION_CREATED)
  @OnEvent(AsyncActionEvents.ASYNC_ACTION_UPDATED)
  async sendWebsocketNotification(
    event: AsyncActionCreatedEvent | AsyncActionUpdatedEvent
  ) {
    await validateOrReject(event)

    const asyncActionModel = await this.asyncActionService.getById(
      event.asyncActionId
    )

    const websocketEvent = new BaseEvent(
      NameEvent.ASYNC_ACTION_UPDATED,
      asyncActionModel
    )

    await this.websocketService.send(
      asyncActionModel.createdById,
      JSON.stringify(websocketEvent)
    )
  }
}
