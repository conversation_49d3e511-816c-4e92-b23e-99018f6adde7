import { Inject, Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { UserEvents } from '../../../shared/domain/event/event.enum'
import { UserCreatedEvent } from '../../../shared/domain/event/user/user-created.event'
import { SlackService } from '../../../shared/infrastructure/service/slack.service'
import {
  USER_REPOSITORY_INTERFACE,
  UserRepositoryInterface,
} from '../../domain/interface/user-repository.interface'
import { SlackConfig } from '../../../../config/slack.config'
import { ConfigService } from '@nestjs/config'
import { CheckWhitelistedEmailQuery } from '../../../admin/application/query/check-whitelisted-email/check-whitelisted-email.query'
import { CheckWhitelistedDomainQuery } from '../../../admin/application/query/check-whitelisted-domain/check-whitelisted-domain.query'
import { BlockDomainCommand } from '../../../admin/application/command/block-domain/block-domain.command'
import { GetUserQuery } from '../query/get-user/get-user.query'

const SUSPICIOUS_USERS_THRESHOLD = 2 // Threshold for considering suspicious behavior
const SLACK_CHANNEL_ID = 'C07J83TN7L7' // Slack channel ID
const UNBLOCK_DOMAIN_URL =
  'https://getheroes.retool.com/apps/3bf4485c-6c3c-11ef-9490-7f50032535e9/%5BOPS%5D%20Domain%20Blocker#searchTerm=' // URL to block domain

@Injectable()
export class AAPMListener {
  private readonly aapmBotToken: string

  constructor(
    @Inject(USER_REPOSITORY_INTERFACE)
    private readonly userRepository: UserRepositoryInterface,
    private readonly slackService: SlackService,
    private readonly configService: ConfigService,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus
  ) {
    const slackConfig = this.configService.get<SlackConfig>('slack')
    this.aapmBotToken = slackConfig.aapmBotToken
  }

  private readonly logger = new Logger(AAPMListener.name)

  @OnEvent(UserEvents.USER_CREATED)
  async handleUserCreatedEvent(event: UserCreatedEvent) {
    if (process.env.ENVIRONMENT !== 'prod') return
    if (!this.aapmBotToken) {
      this.logger.warn('Missing bot token, skipping Slack notification.')
      return
    }

    this.logger.log(
      `Handling UserCreatedEvent for user ID: ${event.userModel.getId()}`
    )

    try {
      const {
        id: userId,
        email: userEmail,
        createdAt: userCreatedAt,
      } = event.userModel
      const userDomain = this.extractDomainFromEmail(userEmail)

      // Execute both whitelist checks in parallel
      const [isEmailWhitelisted, isDomainWhitelisted] = await Promise.all([
        this.queryBus.execute(
          new CheckWhitelistedEmailQuery(userEmail.toLowerCase())
        ),
        this.queryBus.execute(
          new CheckWhitelistedDomainQuery(userDomain.toLowerCase())
        ),
      ])

      // Check if the email is whitelisted
      if (isEmailWhitelisted) {
        this.logger.log(
          `User email ${userEmail} is whitelisted. Skipping suspicious activity check.`
        )
        return
      }

      // Check if the domain is whitelisted
      if (isDomainWhitelisted) {
        this.logger.log(
          `Domain ${userDomain} is whitelisted. Skipping suspicious activity check.`
        )
        return
      }

      // Check if the user is considered risky
      const user = await this.queryBus.execute(new GetUserQuery(userId))
      if (!user.isConsideredRisky) {
        this.logger.log(
          `User ${userEmail} is not considered risky - skipping suspicious activity check`
        )
        return
      }

      // Check if the email follows the suspicious pattern (e.g., <EMAIL>)
      const emailPattern = /^[a-zA-Z]+\d+@.+$/
      const isSuspiciousEmail = emailPattern.test(userEmail)

      // Fetch users created within the last DAYS_THRESHOLD days with the same email domain
      const users =
        await this.userRepository.getUsersByDomainInDifferentOrganizations(
          userDomain
        )

      // Check if the behavior is suspicious (e.g., multiple users with the same domain in different organizations)
      if (users.length > SUSPICIOUS_USERS_THRESHOLD || isSuspiciousEmail) {
        // Block automatically all users related to this domain
        await this.commandBus.execute(
          new BlockDomainCommand(userDomain, true, false)
        )

        // Send Slack notification
        await this.sendSuspiciousActivityNotification(
          userDomain,
          userId,
          userEmail,
          userCreatedAt,
          users
        )
      }
    } catch (error) {
      this.logger.error('Error handling UserCreatedEvent:', error.stack)
    }
  }

  private extractDomainFromEmail(email: string): string {
    return email.split('@')[1].toLowerCase()
  }

  private async sendSuspiciousActivityNotification(
    userDomain: string,
    userId: string,
    userEmail: string,
    userCreatedAt: Date,
    users: any[]
  ): Promise<void> {
    const formattedDate = userCreatedAt.toLocaleDateString('en-US')
    const formattedTime = userCreatedAt.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    })

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🚨 Suspicious Activity : Domain Automatically Blocked 🚨',
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `A new user with the email \`${userEmail}\` has registered on ${formattedDate} at ${formattedTime}.\n\n*Domain \`${userDomain}\` has been automatically blocked* due to suspicious activity:\n• *${users.length}* other user(s) with this domain were created in different organizations\n• The domain and all its associated users have been blocked`,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'Click the dropdown below to view details for each user.',
        },
        accessory: {
          type: 'static_select',
          placeholder: {
            type: 'plain_text',
            text: 'Details',
            emoji: true,
          },
          options: users.map(user => ({
            text: {
              type: 'plain_text',
              text: `${user.user_email} (${user.o_name})`,
              emoji: false,
            },
            value: `user-${user.user_id}`,
          })),
          action_id: 'user_details_select',
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Action Required:* Review and manage the blocked domain.',
        },
        accessory: {
          type: 'button',
          text: {
            type: 'plain_text',
            text: 'Manage Domain Block',
            emoji: false,
          },
          style: 'danger',
          url: `${UNBLOCK_DOMAIN_URL}${userDomain}`,
          action_id: 'manage-domain-block-action',
        },
      },
    ]

    await this.slackService.sendSlackNotification(
      this.aapmBotToken,
      SLACK_CHANNEL_ID,
      { blocks }
    )
    this.logger.log(
      `Suspicious activity notification sent for domain: ${userDomain}`
    )
  }
}
