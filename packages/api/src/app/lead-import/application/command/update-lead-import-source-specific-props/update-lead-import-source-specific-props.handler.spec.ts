import { Test, TestingModule } from '@nestjs/testing'
import { QueryBus } from '@nestjs/cqrs'
import { EventEmitter2 } from '@nestjs/event-emitter'

import { UpdateLeadImportSourceSpecificPropsHandler } from './update-lead-import-source-specific-props.handler'
import { UpdateLeadImportSourceSpecificPropsCommand } from './update-lead-import-source-specific-props.command'
import { LEAD_IMPORT_REPOSITORY_INTERFACE } from '../../../domain/interface/lead-import-repository.interface'
import {
  LEAD_IMPORT_SERVICE,
  LeadImportServiceInterface,
} from '../../../domain/interface/lead-import-service.interface'
import {
  LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
  LeadImportLeadCountersServiceInterface,
} from '../../../domain/interface/lead-import-lead-counters-service.interface'
import { MockLeadImportRepository } from '../../../infrastructure/repository/lead-import.repository.mock'
import { LeadImportFactoryMock } from '../../../domain/factory/lead-import.factory.mock'
import { LeadImportModel } from '../../../domain/models/lead-import.model'
import { GetOneLeadImportByIdQuery } from '../../query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { LeadImportLimitExceededError } from '../create-lead-import/lead-import-invalid-props.error'
import { LeadImportEvents } from '../../../../shared/domain/event/event.enum'
import { LeadImportErrorEvent } from '../../../../shared/domain/event/lead-import/lead-import-error.event'
import { LeadImportSourceEnum, LeadImportStatusEnum } from '@getheroes/shared'
import { LeadImportSourceCsvFilePropsMinimal } from '../../../../shared/domain/interface/lead-import.interface'

describe('UpdateLeadImportSourceSpecificPropsHandler', () => {
  let handler: UpdateLeadImportSourceSpecificPropsHandler
  let mockLeadImportRepository: MockLeadImportRepository
  let mockLeadImportService: Partial<LeadImportServiceInterface>
  let mockQueryBus: Partial<QueryBus>
  let mockEventEmitter: Partial<EventEmitter2>
  let mockLeadImportLeadCountersService: Partial<LeadImportLeadCountersServiceInterface>

  // Create a reusable mock for CSV source specific props
  const mockCsvSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
    spaceId: 'space-123',
    spaceName: 'Test Space',
    spaceNamespace: 'test-space',
    spaceMetadata: {},
    spaceEnvironmentId: 'env-123',
    sheetId: 'sheet-123',
    sheetName: 'Test Sheet',
    sheetFields: [
      { key: 'firstName', label: 'First Name', type: 'string' },
      { key: 'lastName', label: 'Last Name', type: 'string' },
      { key: 'email', label: 'Email', type: 'string' },
    ],
    sheetRecordCounts: {
      total: 100,
      valid: 90,
      error: 10,
    },
  }

  beforeEach(async () => {
    mockLeadImportRepository = new MockLeadImportRepository()

    mockLeadImportService = {
      validateImportLimits: jest.fn(),
      attemptToStartImportProcess: jest.fn(),
    }

    mockQueryBus = {
      execute: jest.fn(),
    }

    mockEventEmitter = {
      emit: jest.fn(),
    }

    mockLeadImportLeadCountersService = {
      computeNbLeadsTotal: jest.fn().mockResolvedValue(10),
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateLeadImportSourceSpecificPropsHandler,
        {
          provide: LEAD_IMPORT_REPOSITORY_INTERFACE,
          useValue: mockLeadImportRepository,
        },
        {
          provide: LEAD_IMPORT_SERVICE,
          useValue: mockLeadImportService,
        },
        {
          provide: QueryBus,
          useValue: mockQueryBus,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
          useValue: mockLeadImportLeadCountersService,
        },
      ],
    }).compile()

    handler = module.get<UpdateLeadImportSourceSpecificPropsHandler>(
      UpdateLeadImportSourceSpecificPropsHandler
    )
  })

  it('should update source specific props successfully', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    })

    // Create updated source props
    const updatedSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
      ...mockCsvSourceSpecificProps,
      sheetName: 'Updated Sheet Name',
    }

    const updatedLeadImport = LeadImportModel.create({
      ...leadImport,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)

    jest
      .spyOn(mockLeadImportRepository, 'updateSourceSpecificProps')
      .mockResolvedValue(updatedLeadImport)

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    // Act
    const result = await handler.execute(command)

    // Assert
    expect(mockQueryBus.execute).toHaveBeenCalledWith(
      expect.any(GetOneLeadImportByIdQuery)
    )
    expect(mockLeadImportService.validateImportLimits).toHaveBeenCalledWith({
      source: leadImport.source,
      sourceSpecificProps: command.sourceSpecificProps,
    })
    expect(
      mockLeadImportRepository.updateSourceSpecificProps
    ).toHaveBeenCalledWith({
      id: leadImport.id,
      sourceSpecificProps: command.sourceSpecificProps,
    })
    expect(
      mockLeadImportService.attemptToStartImportProcess
    ).toHaveBeenCalledWith(updatedLeadImport)
    expect(result).toEqual(updatedLeadImport)
  })

  it('should compute nbLeadsTotal if it is not set', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      status: LeadImportStatusEnum.DRAFT,
      sourceSpecificProps: {},
      nbLeadsTotal: 0,
    })

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)

    jest
      .spyOn(mockLeadImportRepository, 'updateSourceSpecificProps')
      .mockResolvedValue(leadImport)

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    })

    // Act
    await handler.execute(command)

    // Assert
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).toHaveBeenCalledWith(leadImport)
  })

  it('should not compute nbLeadsTotal if it is already set', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
      nbLeadsTotal: 10, // Already set
    })

    const updatedSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
      ...mockCsvSourceSpecificProps,
      sheetRecordCounts: {
        ...mockCsvSourceSpecificProps.sheetRecordCounts,
        valid: 95, // Changed valid count
      },
    }

    const updatedLeadImport = LeadImportModel.create({
      ...leadImport,
      sourceSpecificProps: updatedSourceSpecificProps,
      nbLeadsTotal: 10, // Already set
    })

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)

    jest
      .spyOn(mockLeadImportRepository, 'updateSourceSpecificProps')
      .mockResolvedValue(updatedLeadImport)

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    // Act
    await handler.execute(command)

    // Assert
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).not.toHaveBeenCalled()
  })

  it('should handle LeadImportLimitExceededError and emit error event', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    })
    const error = new LeadImportLimitExceededError('Import limit exceeded')

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)
    mockLeadImportService.validateImportLimits = jest
      .fn()
      .mockImplementation(() => {
        throw error
      })

    // Create updated source props with too many records
    const updatedSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
      ...mockCsvSourceSpecificProps,
      sheetRecordCounts: {
        total: 15000,
        valid: 12000, // Too many records
        error: 3000,
      },
    }

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrow(
      LeadImportLimitExceededError
    )

    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      LeadImportEvents.LEAD_IMPORT_ERROR,
      expect.any(LeadImportErrorEvent)
    )
  })

  it('should handle generic errors and emit error event', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    })
    const error = new Error('Generic error')

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)
    mockLeadImportService.validateImportLimits = jest
      .fn()
      .mockImplementation(() => {
        throw error
      })

    const updatedSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
      ...mockCsvSourceSpecificProps,
      sheetName: 'Updated Sheet',
    }

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrow(Error)

    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      LeadImportEvents.LEAD_IMPORT_ERROR,
      expect.any(LeadImportErrorEvent)
    )
  })

  it('should update CSV file source specific props with new record counts', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    })

    // Create updated source props with different record counts
    const updatedSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
      ...mockCsvSourceSpecificProps,
      sheetName: 'Updated Sheet',
      sheetRecordCounts: {
        total: 120,
        valid: 110,
        error: 10,
      },
    }

    const updatedLeadImport = LeadImportModel.create({
      ...leadImport,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    mockQueryBus.execute = jest.fn().mockResolvedValue(leadImport)

    jest
      .spyOn(mockLeadImportRepository, 'updateSourceSpecificProps')
      .mockResolvedValue(updatedLeadImport)

    const command = new UpdateLeadImportSourceSpecificPropsCommand({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      sourceSpecificProps: updatedSourceSpecificProps,
    })

    // Act
    const result = await handler.execute(command)

    // Assert
    expect(mockLeadImportService.validateImportLimits).toHaveBeenCalledWith({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: updatedSourceSpecificProps,
    })
    expect(result.sourceSpecificProps).toEqual(updatedSourceSpecificProps)
    expect(
      (result.sourceSpecificProps as LeadImportSourceCsvFilePropsMinimal)
        .sheetRecordCounts.valid
    ).toBe(110)
  })
})
