import { Injectable, Logger } from '@nestjs/common'
import { SyncService } from '../sync.service'
import { SyncSettings } from '../../../domain/type/sync.settings.type'
import {
  Filter,
  FilterOperatorEnum,
} from '@hubspot/api-client/lib/codegen/crm/objects'
import { PublicObjectSearchRequest } from '@hubspot/api-client/lib/codegen/crm/objects/notes/models/PublicObjectSearchRequest'
import { CollectionResponseWithTotalSimplePublicObjectForwardPaging } from '@hubspot/api-client/lib/codegen/crm/objects/notes/models/CollectionResponseWithTotalSimplePublicObjectForwardPaging'
import { DataSource, In, IsNull, MoreThan } from 'typeorm'
import { HubspotAuthorizationService } from '../../../../integration/hubspot/application/service/hubspot-authorization.service'
import { InjectDataSource } from '@nestjs/typeorm'
import { SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/objects/notes/models/SimplePublicObject'
import { ContactEntity } from '../../../../lead/infrastructure/entity/contact.entity'
import { CommandBus } from '@nestjs/cqrs'
import { plainToInstance } from 'class-transformer'
import { ContactModel } from '../../../../lead/domain/model/contact.model'
import { InjectMapper } from '@automapper/nestjs'
import { Mapper } from '@automapper/core'
import { CallEntity } from '../../../../dialer/infrastructure/entity/call.entity'
import { CallModel } from '../../../../dialer/domain/model/call.model'
import { CallDirectionType } from '../../../../shared/domain/call-direction.enum'
import { CallStatus } from '../../../../shared/domain/call-status.enum'
import { CallMissedReason } from '../../../../shared/domain/call-missed-reason.enum'
import { CreateCallCommand } from '../../../../dialer/application/command/create-call/create-call.command'
import { EntityType } from '../../../domain/type/entity.type'

const PAGE_SIZE = 50
const MAX_LOOP_ITERATIONS = 10000

const HUBSPOT_CALL_DISPOSITIONS = {
  BUSY: '************************************',
  CONNECTED: 'f240bbac-87c9-4f6e-bf70-924b57d47db7',
  LEFT_LIVE_MESSAGE: 'a4c4c377-d246-4b32-a13b-75a56a4cd0ff',
  LEFT_VOICE_MAIL: 'b2cf5968-551e-4856-9783-52b3da59a7d0',
  NO_ANSWER: '73a0d17f-1163-4015-bdd5-ec830791da20',
  WRONG_NUMBER: '17b47fee-58de-441e-a44c-c6300d46f273',
}
const CallStatusMapping = {
  HUBSPOT_TO_ZELIQ: {
    [HUBSPOT_CALL_DISPOSITIONS.BUSY]: CallStatus.HANGUP,
    [HUBSPOT_CALL_DISPOSITIONS.CONNECTED]: CallStatus.DONE,
    [HUBSPOT_CALL_DISPOSITIONS.LEFT_LIVE_MESSAGE]: CallStatus.MISSED,
    [HUBSPOT_CALL_DISPOSITIONS.LEFT_VOICE_MAIL]: CallStatus.MISSED,
    [HUBSPOT_CALL_DISPOSITIONS.NO_ANSWER]: CallStatus.MISSED,
    [HUBSPOT_CALL_DISPOSITIONS.WRONG_NUMBER]: CallStatus.MISSED,
  },
  ZELIQ_TO_HUBSPOT: {
    [CallStatus.DONE]: HUBSPOT_CALL_DISPOSITIONS.CONNECTED,
    [CallStatus.HANGUP]: HUBSPOT_CALL_DISPOSITIONS.BUSY,
    [CallStatus.MISSED]: HUBSPOT_CALL_DISPOSITIONS.NO_ANSWER,
  },
}

const CallMissedReasonMapping = {
  HUBSPOT_TO_ZELIQ: {
    [HUBSPOT_CALL_DISPOSITIONS.BUSY]: CallMissedReason.NO_AVAILABLE_AGENT,
    [HUBSPOT_CALL_DISPOSITIONS.CONNECTED]: null,
    [HUBSPOT_CALL_DISPOSITIONS.LEFT_LIVE_MESSAGE]: null,
    [HUBSPOT_CALL_DISPOSITIONS.LEFT_VOICE_MAIL]: null,
    [HUBSPOT_CALL_DISPOSITIONS.NO_ANSWER]: CallMissedReason.NO_AVAILABLE_AGENT,
    [HUBSPOT_CALL_DISPOSITIONS.WRONG_NUMBER]: null,
  },
  ZELIQ_TO_HUBSPOT: {
    [CallMissedReason.NO_AVAILABLE_AGENT]: HUBSPOT_CALL_DISPOSITIONS.NO_ANSWER,
  },
}
const PROPAGATION_DELAY = 2 //minutes
@Injectable()
export class SyncCallsService {
  private readonly logger = new Logger(SyncCallsService.name)

  constructor(
    private readonly syncService: SyncService,
    private readonly hubspotAuthorizationService: HubspotAuthorizationService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly commandBus: CommandBus,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  private getSyncDateMinusPropagationDelay(date: Date) {
    return new Date(date.getTime() - PROPAGATION_DELAY * 60 * 1000)
  }

  async sync(
    organizationId: string,
    settings: SyncSettings,
    lastSyncDate: Date
  ) {
    await this.pull(organizationId, lastSyncDate).catch(error => {
      this.logger.error(
        { err: error, organizationId },
        `Error syncing calls (pull)`
      )
    })

    if (settings.push.includes('call' as EntityType)) {
      await this.push(organizationId, lastSyncDate).catch(error => {
        this.logger.error(
          { err: error, organizationId },
          `Error syncing calls (push)`
        )
      })
    }
  }

  private async pull(organizationId: string, lastSyncDate: Date) {
    const hubSpotClient =
      await this.hubspotAuthorizationService.getHubspotClient(organizationId)
    const filters: Array<Filter> = []

    filters.push({
      propertyName: 'hs_lastmodifieddate',
      operator: 'GT' as FilterOperatorEnum,
      value: this.getSyncDateMinusPropagationDelay(lastSyncDate).toISOString(),
    })
    filters.push({
      propertyName: 'hs_call_status',
      operator: 'EQ' as FilterOperatorEnum,
      value: 'COMPLETED',
    })

    const publicObjectSearchRequest: PublicObjectSearchRequest = {
      filterGroups: [{ filters }],
      properties: [
        'hs_timestamp',
        'hs_call_body',
        'hs_call_callee_object_id',
        'hs_call_callee_object_type_id',
        'hs_call_direction',
        'hs_call_disposition',
        'hs_call_duration',
        'hs_call_from_number',
        'hs_call_recording_url',
        'hs_call_status',
        'hs_call_title',
        'hs_call_source',
        'hs_call_to_number',
        'hubspot_owner_id',
        'hs_activity_type',
        'hs_attachment_ids',
      ],
      limit: PAGE_SIZE,
      after: 0,
      sorts: ['-createdate'],
    }
    let hasMore = true

    //loop through all emails in hubspot
    while (hasMore) {
      let response: CollectionResponseWithTotalSimplePublicObjectForwardPaging =
        null
      try {
        await this.syncService.searchRateLimitPause()
        response = await hubSpotClient.crm.objects.calls.searchApi.doSearch(
          publicObjectSearchRequest
        )
        publicObjectSearchRequest.after = +response?.paging?.next?.after
      } catch (error) {
        this.logger.error(
          { err: error, organizationId },
          `Error fetching calls from hubspot`
        )
      }

      hasMore = !!response?.paging

      if (response?.results?.length) {
        //create emails in zeliq
        await this.createObjectsInZeliq(
          organizationId,
          response?.results,
          'calls'
        )
      }
    }
  }

  private async createObjectsInZeliq(
    organizationId: string,
    data: SimplePublicObject[],
    type: string
  ) {
    const CallRepository = this.dataSource.getRepository(CallEntity)
    const hubSpotClient =
      await this.hubspotAuthorizationService.getHubspotClient(organizationId)

    await this.syncService.standardRateLimitPause()
    const associationResultsResponse =
      await hubSpotClient.crm.associations.v4.batchApi.getPage(
        type,
        'contacts',
        {
          inputs: data.map(item => {
            return { id: item.id }
          }),
        }
      )

    const associationResults = associationResultsResponse.results || []
    const associationsMap = {}
    //build association map
    for (let i = 0; i < data.length; i++) {
      const callObject = data[i]
      const associationResult = associationResults.find(
        item => item._from.id === callObject.id
      )
      if (!associationResult) {
        continue
      }
      associationsMap[callObject.id] = associationResult.to?.[0]?.toObjectId
    }

    //keep only new emails
    const existingObjects = await CallRepository.find({
      select: ['externalId'],
      where: { organizationId, externalId: In(Object.keys(associationsMap)) },
    })

    //keep only synced contacts
    const contactIds = Object.values(associationsMap)
    const contacts = await this.dataSource.getRepository(ContactEntity).find({
      //select: ['id', 'externalId'],
      where: { externalId: In(contactIds), organizationId },
      relations: ['company', 'assignUser', 'createdBy'],
    })

    for (let i = 0; i < data.length; i++) {
      const callObject = data[i]
      const callObjectId = callObject.id
      if (existingObjects.find(call => call.externalId === callObjectId)) {
        continue
      }
      const contactExternalId = associationsMap[callObjectId]
      const contact = contacts.find(
        contact => +contact.externalId === contactExternalId
      )

      const contactModel = this.mapper.map(contact, ContactEntity, ContactModel)

      if (!contact) {
        continue
      }

      //TODO fetch user from emailObjectHeaders
      const createdBy = contactModel.assignUser || contactModel.createdBy

      const plainCall = {
        organizationId: organizationId,
        externalId: callObjectId,
        referenceId: callObjectId,
        createdBy: createdBy,
        contactId: contact.id,
        direction:
          callObject?.properties?.hs_call_direction === 'INBOUND'
            ? CallDirectionType.INBOUND
            : CallDirectionType.OUTBOUND,
        numberFrom:
          callObject?.properties?.hs_call_from_number || contact?.phones?.[0],
        numberTo:
          callObject?.properties?.hs_call_to_number || contact?.phones?.[0],
        provider: 'hubspot',
        typeDialer: 'hubspot',
        status:
          CallStatusMapping.HUBSPOT_TO_ZELIQ[
            callObject?.properties?.hs_call_disposition
          ] || CallStatus.DONE,
        missedCallReason:
          CallMissedReasonMapping.HUBSPOT_TO_ZELIQ[
            callObject?.properties?.hs_call_disposition
          ] || null,
        startedAt: new Date(callObject?.properties?.hs_timestamp),
        endedAt: new Date(
          new Date(callObject?.properties?.hs_timestamp).getTime() +
            (+callObject?.properties?.hs_call_duration || 600000)
        ),
      }

      const callModel = plainToInstance(CallModel, plainCall, {
        ignoreDecorators: true,
      })
      try {
        await this.commandBus.execute(new CreateCallCommand(callModel))

        this.logger.log(
          { organizationId },
          `Created ${type} in zeliq: ${callObject.id}`
        )
      } catch (error) {
        this.logger.error(
          { err: error, organizationId },
          `Failed to create ${type} in zeliq`
        )
      }
    }
  }

  private async push(organizationId: string, lastSyncDate: Date) {
    await this.pushNewData(organizationId, lastSyncDate)
  }

  private async pushNewData(organizationId: string, lastSyncDate: Date) {
    //TODO add a query to get notes
    const CallRepository = this.dataSource.getRepository(CallEntity)

    let hasMore = true
    let nbLoops = 0
    let lastCallId = null

    //Subtract 5 minutes from lastSyncDate to handle network/hubspot downtime
    const lastSyncDateMinus5Min = new Date(
      lastSyncDate.getTime() - 5000 * 60 * 1000
    )

    const baseCondition = {
      organizationId,
      externalId: IsNull(),
      status: In([CallStatus.DONE, CallStatus.MISSED, CallStatus.HANGUP]),
    }

    const dateCondition = lastSyncDateMinus5Min
      ? { updatedAt: MoreThan(lastSyncDateMinus5Min.toISOString()) }
      : {}

    while (hasMore && nbLoops < MAX_LOOP_ITERATIONS) {
      const query = CallRepository.createQueryBuilder('calls')
        .leftJoin('calls.contact', 'contact')
        .select('calls.id', 'id')
        .addSelect('calls.externalId', 'objectId')
        .addSelect('calls.numberFrom', 'from')
        .addSelect('calls.numberTo', 'to')
        .addSelect('calls.direction', 'direction')
        .addSelect('calls.status', 'status')
        .addSelect('calls.missedCallReason', 'missedCallReason')
        .addSelect('calls.startedAt', 'startedAt')
        .addSelect('calls.endedAt', 'endedAt')
        .addSelect('contact.externalId', 'associatedObjectId')
        .where({ ...baseCondition, ...dateCondition })
        .andWhere('contact.externalId IS NOT NULL')

      if (lastCallId) {
        query.andWhere('calls.id > :lastCallId', {
          lastCallId: lastCallId,
        })
      }

      query.orderBy('calls.id', 'ASC')
      query.limit(PAGE_SIZE)

      const calls = await query.getRawMany()
      if (calls?.length) {
        await this.createObjectsInHubspot(organizationId, calls, 'calls')
        const lastCall = calls[calls.length - 1]
        lastCallId = lastCall.id
      }

      hasMore = calls.length === PAGE_SIZE
      nbLoops++
    }
  }

  async createObjectsInHubspot(
    organizationId: string,
    data: any[],
    type: string
  ) {
    const hubSpotClient =
      await this.hubspotAuthorizationService.getHubspotClient(organizationId)

    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      try {
        await this.syncService.standardRateLimitPause()
        const response = await hubSpotClient.crm.objects.calls.basicApi.create({
          associations: [
            {
              types: [
                {
                  associationTypeId: 194,
                  associationCategory: 'HUBSPOT_DEFINED',
                },
              ],
              to: { id: item.associatedObjectId },
            },
          ],
          properties: {
            hs_timestamp: item.startedAt.toISOString(),
            hs_call_direction:
              item.direction === CallDirectionType.INBOUND
                ? 'INBOUND'
                : 'OUTBOUND',
            hs_call_disposition:
              CallStatusMapping.ZELIQ_TO_HUBSPOT[item.status] ||
              HUBSPOT_CALL_DISPOSITIONS.CONNECTED,
            hs_call_duration: Math.floor(
              new Date(item.endedAt).getTime() -
                new Date(item.startedAt).getTime()
            ).toString(),
            hs_call_from_number: item.from,
            hs_call_to_number: item.to,
            hs_call_status: 'COMPLETED',
          },
        })

        if (response?.id) {
          await this.dataSource
            .getRepository(CallEntity)
            .update(
              { id: item.id },
              { externalId: response.id, updatedAt: item.updatedAt }
            )

          this.logger.log(
            { organizationId },
            `Created ${type} in hubspot: ${response.id}`
          )
        } else {
          this.logger.error(
            { organizationId },
            `Failed to create ${type} in hubspot`
          )
        }
      } catch (error) {
        this.logger.error(
          { err: error, organizationId },
          `Error creating ${type} in hubspot`
        )
      }
    }
  }
}
