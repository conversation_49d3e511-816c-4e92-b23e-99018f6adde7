import { Inject } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import {
  OrganizationMemberRepositoryInterface,
  ORGANIZATION_MEMBER_REPOSITORY_INTERFACE,
} from '../../../domain/organization-member-repository.interface'
import { OrganizationMemberModel } from '../../../domain/organization-member.model'
import { FindOrganizationMemberQuery } from './find-organization-member.query'

@QueryHandler(FindOrganizationMemberQuery)
export class FindOrganizationMemberHandler
  implements IQueryHandler<FindOrganizationMemberQuery>
{
  constructor(
    @Inject(ORGANIZATION_MEMBER_REPOSITORY_INTERFACE)
    private organizationMemberRepository: OrganizationMemberRepositoryInterface
  ) {}

  execute(
    query: FindOrganizationMemberQuery
  ): Promise<OrganizationMemberModel> {
    return this.organizationMemberRepository.findById(
      query.organizationMemberId
    )
  }
}
