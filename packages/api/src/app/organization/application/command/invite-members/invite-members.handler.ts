import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { UserService } from '../../../../shared/application/service/user.service'
import { InvitationMemberModel } from '../../../domain/invitation-member.model'
import { OrganizationMemberModel } from '../../../domain/organization-member.model'
import { InvitationMemberService } from '../../service/invitation-member.service'
import { OrganizationService } from '../../service/organization.service'
import { InviteMembersCommand } from './invite-members.command'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../../../shared/logger.service'

interface InvitationMemberResult {
  invitationMembers: InvitationMemberModel[]
  organizationMembers: OrganizationMemberModel[]
}

@CommandHandler(InviteMembersCommand)
export class InviteMembersHandler
  implements ICommandHandler<InviteMembersCommand>
{
  constructor(
    private readonly invitationMemberService: InvitationMemberService,
    private readonly userService: UserService,
    private readonly organizationService: OrganizationService
  ) {}

  @TryCatchLogger({
    feature: LogFeature.ORGANIZATION,
    message: 'Failed to invite members',
  })
  async execute(
    command: InviteMembersCommand
  ): Promise<InvitationMemberResult> {
    const invitationMembersCreated = []
    const organizationMembersCreated = []
    await Promise.all(
      command.invitationMembers.map(async invitationMember => {
        const user = await this.userService.getUserByEmail(
          invitationMember.email
        )
        if (user !== null) {
          let organizationMember = new OrganizationMemberModel(
            user,
            command.organization,
            invitationMember.role,
            command.user
          )
          organizationMember =
            await this.organizationService.addMember(organizationMember)
          if (organizationMember) {
            organizationMembersCreated.push(organizationMember)
          }
        } else {
          const invitationMemberModel =
            await this.invitationMemberService.invite(
              invitationMember,
              command.organization,
              command.user
            )
          if (invitationMemberModel) {
            invitationMembersCreated.push(invitationMemberModel)
          }
        }
      })
    )
    return {
      invitationMembers: invitationMembersCreated,
      organizationMembers: organizationMembersCreated,
    }
  }
}
