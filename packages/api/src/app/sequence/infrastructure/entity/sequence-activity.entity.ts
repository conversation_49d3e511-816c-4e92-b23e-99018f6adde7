import { AutoMap } from '@automapper/classes'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'
import {
  Column,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { SequenceEntity } from './sequence.entity'
import { SequenceStepEntity } from './sequence-step.entity'
import { SequenceActivityContent } from '../../domain/types/sequence-activity-content.types'
import { SequenceContactEntity } from './sequence-contact.entity'

@Entity('sequences_activity')
@Index('IDX_sequencesActivity_sequenceContactId_stepId_type', [
  'sequenceContactId',
  'stepId',
  'type',
])
@Index('IDX_sequencesActivity_type_resourceId', ['type', 'resourceId'])
export class SequenceActivityEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @AutoMap()
  @Column({ name: 'created_by_id' })
  createdById: string

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity

  @Column({ name: 'sequence_id' })
  @AutoMap()
  sequenceId: string

  @ManyToOne(() => SequenceEntity)
  @JoinColumn({ name: 'sequence_id' })
  @AutoMap(() => SequenceEntity)
  sequence: SequenceEntity

  @Column({ name: 'sequence_contact_id' })
  @AutoMap()
  sequenceContactId: string

  @Index()
  @ManyToOne(() => SequenceContactEntity)
  @JoinColumn({ name: 'sequence_contact_id' })
  @AutoMap(() => SequenceContactEntity)
  sequenceContact: SequenceContactEntity

  @Column({ name: 'sequence_step_id' })
  @AutoMap()
  stepId: string | null

  @ManyToOne(() => SequenceStepEntity)
  @JoinColumn({ name: 'sequence_step_id' })
  @AutoMap(() => SequenceStepEntity)
  step: SequenceStepEntity | null

  @Column()
  @AutoMap(() => String)
  type: string

  @Column({ type: 'timestamptz' })
  @AutoMap()
  date: Date

  @Index('IDX_sequencesActivity_content', { synchronize: false }) // GIN index manually created in migration
  @Column({ type: 'jsonb', nullable: true })
  @AutoMap(() => Object)
  content?: SequenceActivityContent

  @Column({ nullable: false })
  @AutoMap()
  status: string

  @Column({ nullable: true })
  @AutoMap()
  resourceId?: string

  @DeleteDateColumn({ type: 'timestamptz' })
  @Index()
  @AutoMap()
  deletedAt: Date
}
