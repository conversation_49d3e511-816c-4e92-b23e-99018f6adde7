import { AutoMap } from '@automapper/classes'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { SequenceActivityType, SequenceStepType } from '@getheroes/shared'
import { SequenceActivityContent } from '../../domain/types/sequence-activity-content.types'
import { SequenceModel } from './sequence.model'
import { SequenceStepModel } from './sequence-step.model'
import { SequenceActivityStepStatus } from '../enum/sequence-activity-step-status.enum'
import { SequenceContactModel } from './sequence-contact.model'

export class SequenceActivityModel {
  @AutoMap()
  id: string

  @AutoMap()
  createdById: string

  @AutoMap(() => UserEntity)
  createdBy: UserEntity

  @AutoMap()
  sequenceId: string

  @AutoMap(() => SequenceModel)
  sequence: SequenceModel

  @AutoMap()
  sequenceContactId: string

  @AutoMap(() => SequenceContactModel)
  sequenceContact: SequenceContactModel

  @AutoMap()
  stepId: string

  @AutoMap(() => SequenceStepModel)
  step: SequenceStepModel | null

  @AutoMap(() => String)
  type: SequenceActivityType

  @AutoMap()
  date: Date

  @AutoMap(() => Object)
  content?: SequenceActivityContent

  @AutoMap(() => String)
  status: SequenceActivityStepStatus

  @AutoMap()
  resourceId?: string

  @AutoMap()
  deletedAt: Date

  isLinkedinActivity(): boolean {
    return (
      this.type === SequenceActivityType.STEP_LINKEDIN ||
      this.type === SequenceActivityType.LINKEDIN_RECEIVED
    )
  }

  isMailActivity(): boolean {
    return (
      this.type === SequenceActivityType.STEP_EMAIL ||
      this.type === SequenceActivityType.EMAIL_RECEIVED ||
      this.type === SequenceActivityType.STEP_REPLY_IN_THREAD ||
      this.type === SequenceActivityType.STEP_MANUAL_EMAIL
    )
  }

  isCallActivity(): boolean {
    return this.type === SequenceActivityType.STEP_CALL
  }

  static isLinkedinActivityFromSequenceStepType(
    sequenceStepType: SequenceStepType
  ): boolean {
    return (
      sequenceStepType === SequenceStepType.LINKEDIN_VISIT_PROFILE ||
      sequenceStepType === SequenceStepType.LINKEDIN_SEND_INVITATION ||
      sequenceStepType === SequenceStepType.LINKEDIN_SEND_MESSAGE
    )
  }

  static isMailActivityFromSequenceStepType(
    sequenceStepType: SequenceStepType
  ): boolean {
    return (
      sequenceStepType === SequenceStepType.EMAIL ||
      sequenceStepType === SequenceStepType.REPLY_IN_THREAD ||
      sequenceStepType === SequenceStepType.MANUAL_EMAIL
    )
  }

  static isCallActivityActivityFromSequenceStepType(
    sequenceStepType: SequenceStepType
  ): boolean {
    return sequenceStepType === SequenceStepType.CALL
  }

  static getTypeFromSequenceStepType(
    sequenceStepType: SequenceStepType
  ): SequenceActivityType {
    switch (sequenceStepType) {
      case SequenceStepType.LINKEDIN_VISIT_PROFILE:
      case SequenceStepType.LINKEDIN_SEND_INVITATION:
      case SequenceStepType.LINKEDIN_SEND_MESSAGE:
        return SequenceActivityType.STEP_LINKEDIN
      case SequenceStepType.EMAIL:
        return SequenceActivityType.STEP_EMAIL
      case SequenceStepType.CALL:
        return SequenceActivityType.STEP_CALL
      case SequenceStepType.MANUAL_EMAIL:
        return SequenceActivityType.STEP_MANUAL_EMAIL
      case SequenceStepType.REPLY_IN_THREAD:
        return SequenceActivityType.STEP_REPLY_IN_THREAD
    }
  }

  static isManualStep(sequenceStepType: SequenceStepType): boolean {
    return (
      sequenceStepType === SequenceStepType.CALL ||
      sequenceStepType === SequenceStepType.MANUAL_EMAIL
    )
  }
}
