import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { FindSequenceContactPastActivitiesBySequenceAndContactsQuery } from './find-sequence-past-activities-by-sequence-and-contacts.query'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../../domain/repository/sequence-contact-repository.interface'
import { SequenceStepActivitiesObject } from '../../../../domain/object/sequence-step-activities.object'
import { SequenceStepStatus } from '../../../../domain/enum/sequence-step-status.enum'
import { SequenceContactModel } from '../../../../domain/model/sequence-contact.model'
import { SequenceStepModel } from '../../../../domain/model/sequence-step.model'
import { ScheduleEmailService } from '../../../service/schedule-email.service'
import { ConfigScheduleService } from '../../../service/config-schedule.service'
import { FormatTemplateCommand } from '../../../../../template-lead/application/command/format-template/format-template.command'
import { StepContentMailObject } from '../../../../domain/object/step-content-mail.object'
import { plainToInstance } from 'class-transformer'
import { StepCompleteContentMailObject } from '../../../../domain/object/step-complete-content-mail.object'
import { FindGrantedGmailAuthorizationQuery } from '../../../../../integration/gmail/application/query/find-gmail-authorization/find-granted-gmail-authorization.query'
import { FindInitialMessageForStepAndSequenceContactQuery } from '../../../../../mailer/application/query/find-message-by-step-and-contact-id/find-initial-message-by-step-and-contact.query'
import { ContactModel } from '../../../../../lead/domain/model/contact.model'
import {
  SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE,
  SequenceActivityRepositoryInterface,
} from '../../../../domain/repository/sequence-activity-repository.interface'

@QueryHandler(FindSequenceContactPastActivitiesBySequenceAndContactsQuery)
export class FindSequenceContactPastActivitiesBySequenceAndContactsHandler
  implements
    IQueryHandler<FindSequenceContactPastActivitiesBySequenceAndContactsQuery>
{
  nextStepDate = null

  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly scheduleEmailService: ScheduleEmailService,
    private readonly configScheduleService: ConfigScheduleService,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
    @Inject(SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE)
    private readonly sequenceActivityRepository: SequenceActivityRepositoryInterface
  ) {}

  async execute(
    query: FindSequenceContactPastActivitiesBySequenceAndContactsQuery
  ) {
    const { sequenceId, contactIds, organizationId } = query

    const sequenceContactModels =
      await this.sequenceContactRepository.findBySequenceAndContactsAndOrganization(
        sequenceId,
        contactIds,
        organizationId
      )

    const sequenceStepActivities: SequenceStepActivitiesObject[] = []

    for (const sequenceContactModel of sequenceContactModels) {
      const contact = sequenceContactModel.contact
      const gmailAuthorization = await this.queryBus.execute(
        new FindGrantedGmailAuthorizationQuery(contact.assignUser?.id)
      )

      const pastActivities =
        await this.sequenceActivityRepository.findBySequenceContactId(
          sequenceContactModel.id
        )

      for (const pastActivity of pastActivities) {
        const step = pastActivity.step
        if (!step) continue

        const { email: emailContact } =
          await this.scheduleEmailService.getEmail(
            contact as ContactModel,
            sequenceContactModel
          )

        const activity: SequenceStepActivitiesObject = Object.assign(step, {
          contactId: sequenceContactModel.contactId,
          status: SequenceStepStatus.PAST,
          date: pastActivity.date,
          formattedContent: await this.getStepContent(
            step,
            SequenceStepStatus.PAST,
            sequenceContactModel,
            emailContact,
            gmailAuthorization?.email
          ),
        })
        sequenceStepActivities.push(activity)
      }
    }
    return sequenceStepActivities
  }

  private async getStepContent(
    stepModel: SequenceStepModel,
    status: SequenceStepStatus,
    sequenceContactModel: SequenceContactModel,
    emailContact: string,
    emailUser: string
  ): Promise<StepCompleteContentMailObject> {
    if (status === SequenceStepStatus.PAST) {
      const messageModel = await this.queryBus.execute(
        new FindInitialMessageForStepAndSequenceContactQuery(
          stepModel.id,
          sequenceContactModel.id
        )
      )

      if (messageModel) {
        const content = new StepCompleteContentMailObject()
        content.body = messageModel.body
        content.subject = messageModel.subject
        content.attachments = messageModel.attachments
        content.from = messageModel.from
        content.to = messageModel.to[0]
        content.nbOpens = messageModel.openedDates
          ? messageModel.openedDates.length
          : 0
        content.nbClicks = messageModel.linkClickedDates
          ? messageModel.linkClickedDates.length
          : 0

        return content
      }
    }

    const stepContentMail = plainToInstance(
      StepContentMailObject,
      stepModel.content
    )
    const formatTemplateCommand = new FormatTemplateCommand(
      sequenceContactModel.sequence.organizationId,
      stepContentMail.subject,
      sequenceContactModel.contactId
    )

    const mailSubject = await this.commandBus.execute(formatTemplateCommand)
    const mailBody = await this.commandBus.execute(formatTemplateCommand)

    const content = new StepCompleteContentMailObject()
    content.body = mailBody
    content.subject = mailSubject
    content.attachments = stepContentMail.attachments
    content.to = emailContact
    content.from = emailUser
    return content
  }
}
