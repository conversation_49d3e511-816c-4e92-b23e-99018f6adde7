import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import {
  SEQUENCE_REPOSITORY_INTERFACE,
  SequenceRepositoryInterface,
} from '../../../domain/repository/sequence-repository.interface'
import { CountSequencesQuery } from './count-sequences.query'
import { SequenceStatus } from '../../../domain/sequence-status.enum'

@QueryHandler(CountSequencesQuery)
export class CountSequencesHandler
  implements IQueryHandler<CountSequencesQuery>
{
  constructor(
    @Inject(SEQUENCE_REPOSITORY_INTERFACE)
    private sequenceRepository: SequenceRepositoryInterface
  ) {}

  execute({
    organizationId,
    statuses = [],
  }: CountSequencesQuery): Promise<Record<SequenceStatus, number>> {
    return this.sequenceRepository.countByOrganization(organizationId, {
      status: statuses,
      skip: 0,
    })
  }
}
