import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import {
  LinkedinEvents,
  SequenceEvents,
} from '../../../../shared/domain/event/event.enum'
import { SequenceStepSucceededEvent } from '../../../../shared/domain/event/sequence/sequence-step-succeeded.event'
import { SequenceActivityType } from '@getheroes/shared'
import { LinkedinVisitProfileEvent } from '../../../../shared/domain/event/linkedin/visit-profile.event'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { SequenceActivityModel } from '../../../domain/model/sequence-activity.model'
import { CreateSequenceActivityCommand } from '../../command/create-sequence-activity/create-sequence-activity.command'
import { SequenceActivityStepStatus } from '../../../domain/enum/sequence-activity-step-status.enum'
import { ActivityModel } from '../../../../lead/domain/model/activity.model'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'
import { CreateActivityCommand } from '../../../../lead/application/command/activity/create-activity/create-activity.command'
import { FindContactQuery } from '../../../../lead/application/query/find-contact/find-contact.query'
import { ContactInterface } from '../../../../shared/domain/model/lead/contact.interface'
import { ActivityType } from '../../../../lead/domain/activity-type.enum'
import { LinkedinVisitProfileActivityModel } from '../../../../shared/domain/model/activity/linkedin-visit-profile.model'

@Injectable()
export class LinkedinVisitProfileSucceededListener {
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface
  ) {}

  @OnEvent(LinkedinEvents.VISIT_PROFILE_SUCCEEDED)
  async handleLinkedinVisitProfileSucceededEvent(
    event: LinkedinVisitProfileEvent
  ) {
    const sequenceStepSucceededEvent = new SequenceStepSucceededEvent()
    sequenceStepSucceededEvent.sequenceContactId = event.sequenceContactId
    this.eventEmitter.emit(
      SequenceEvents.SEQUENCE_STEP_SUCCEEDED,
      sequenceStepSucceededEvent
    )

    // Create sequence activity
    const sequenceActivityModel = new SequenceActivityModel()
    sequenceActivityModel.sequenceId = event.sequenceId
    sequenceActivityModel.stepId = event.sequenceStepId
    sequenceActivityModel.sequenceContactId = event.sequenceContactId
    sequenceActivityModel.createdById = event.userId
    sequenceActivityModel.content = event.content
    sequenceActivityModel.date = event.date
    sequenceActivityModel.status = SequenceActivityStepStatus.DONE
    sequenceActivityModel.type = SequenceActivityType.STEP_LINKEDIN
    sequenceActivityModel.content = event.content

    await this.commandBus.execute(
      new CreateSequenceActivityCommand(sequenceActivityModel)
    )

    const sequenceContactModel = await this.sequenceContactRepository.findById(
      event.sequenceContactId
    )
    const contact = await this.queryBus.execute<
      FindContactQuery,
      ContactInterface
    >(new FindContactQuery(sequenceContactModel.contactId))

    // Create contact activity
    const activityPayload = new LinkedinVisitProfileActivityModel()
    activityPayload.userId = event.userId
    activityPayload.userName = sequenceContactModel.createdBy.getFullName()
    activityPayload.contactName = contact.fullNameDisplay
    activityPayload.contactLinkedinUrl = contact.linkedinUrl
    activityPayload.companyId = contact.company?.id
    activityPayload.companyName = contact.company?.name
    activityPayload.sequenceId = sequenceContactModel.sequenceId
    activityPayload.sequenceName = sequenceContactModel.sequence.name

    const activityModel = new ActivityModel()
    activityModel.organizationId = sequenceContactModel.sequence.organizationId
    activityModel.contactId = event.contactId
    activityModel.payload = activityPayload
    activityModel.type = ActivityType.LINKEDIN_PROFIL_VISITED

    await this.commandBus.execute(new CreateActivityCommand(activityModel))
  }
}
