import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import { SequenceActivityType, SequenceContactStatus } from '@getheroes/shared'
import {
  MailEvents,
  SequenceEvents,
} from '../../../shared/domain/event/event.enum'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../domain/repository/sequence-contact-repository.interface'
import { MessageDirection } from '../../../mailer/domain/enum/message-direction.enum'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { FindContactQuery } from '../../../lead/application/query/find-contact/find-contact.query'
import { SequenceActivityModel as LeadActivityModel } from '../../../shared/domain/model/activity/sequence.model'
import { SequenceActivityModel } from '../../domain/model/sequence-activity.model'
import { ActivityModel } from '../../../lead/domain/model/activity.model'
import { CreateActivityCommand } from '../../../lead/application/command/activity/create-activity/create-activity.command'
import { ActivityType } from '../../../lead/domain/activity-type.enum'
import { MessageEvent } from '../../../shared/domain/event/mail/message.event'
import { CreateSequenceActivityCommand } from '../command/create-sequence-activity/create-sequence-activity.command'
import { SequenceActivityStepStatus } from '../../domain/enum/sequence-activity-step-status.enum'

@Injectable()
export class MailAnsweredListener {
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly eventEmitter: EventEmitter2,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  @OnEvent(MailEvents.MAIL_ANSWERED)
  async handleMailAnsweredEvent(event: MessageEvent) {
    const messageModel = event.messageModel

    if (
      MessageDirection.INBOUND !== messageModel.direction ||
      !messageModel.sequenceContactId
    ) {
      return
    }

    const sequenceContactModel = await this.sequenceContactRepository.findById(
      messageModel.sequenceContactId
    )

    if (!sequenceContactModel) {
      return
    }

    const hasAlreadyAnswered =
      sequenceContactModel.status === SequenceContactStatus.CONTACT_ANSWERED

    sequenceContactModel.status = SequenceContactStatus.CONTACT_ANSWERED
    await this.sequenceContactRepository.update(sequenceContactModel)

    // get contact
    const contact = await this.queryBus.execute(
      new FindContactQuery(sequenceContactModel.contactId)
    )

    // get sequence
    const sequence = sequenceContactModel.sequence

    // Create lead activity when contact replies
    const leadActivityModel = new LeadActivityModel()
    leadActivityModel.userId = sequenceContactModel.createdBy.id
    leadActivityModel.userName = sequenceContactModel.createdBy.getFullName()
    leadActivityModel.contactName = contact.fullNameDisplay
    leadActivityModel.contactEmail = messageModel.from
    leadActivityModel.sequenceId = sequence.id
    leadActivityModel.sequenceName = sequence.name

    const activityModel = new ActivityModel()
    activityModel.resourceId = sequenceContactModel.id
    activityModel.organizationId = sequence.organizationId
    activityModel.contactId = contact.id
    activityModel.payload = leadActivityModel
    activityModel.type = ActivityType.SEQUENCE_CONTACT_ANSWERED

    await this.commandBus.execute(new CreateActivityCommand(activityModel))

    if (!hasAlreadyAnswered) {
      // Create sequence activity
      const sequenceActivityModel = new SequenceActivityModel()
      sequenceActivityModel.sequenceId = sequenceContactModel.sequence.id
      sequenceActivityModel.stepId = messageModel.sequenceStepId
      sequenceActivityModel.sequenceContactId = messageModel.sequenceContactId
      sequenceActivityModel.createdById = messageModel.createdBy.id
      sequenceActivityModel.date = messageModel.messageDate || new Date()
      sequenceActivityModel.status = SequenceActivityStepStatus.REPLIED
      sequenceActivityModel.type = SequenceActivityType.EMAIL_RECEIVED
      sequenceActivityModel.content = {
        from: messageModel.from,
        to: messageModel.to[0],
        subject: messageModel.subject,
        body: messageModel.body,
        attachments: messageModel.attachments?.map(attachment => ({
          name: attachment.name,
          url: attachment.url,
        })),
      }
      sequenceActivityModel.resourceId = messageModel.id

      await this.commandBus.execute(
        new CreateSequenceActivityCommand(sequenceActivityModel)
      )
    }

    this.eventEmitter.emit(SequenceEvents.SEQUENCE_CONTACT_ANSWERED, {
      sequenceContactId: sequenceContactModel.id,
    })

    // send event to update reply Kpi
    this.eventEmitter.emit(MailEvents.MAIL_SEQUENCE_ANSWERED, event)
  }
}
