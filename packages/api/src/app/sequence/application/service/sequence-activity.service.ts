import { Inject, Injectable, Logger } from '@nestjs/common'
import { SequenceActivityType, SequenceContactStatus } from '@getheroes/shared'
import { ContactInterface } from '../../../shared/domain/model/lead/contact.interface'
import { SequenceContactModel } from '../../domain/model/sequence-contact.model'
import {
  SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE,
  SequenceActivityRepositoryInterface,
} from '../../domain/repository/sequence-activity-repository.interface'
import { SequenceActivityModel } from '../../domain/model/sequence-activity.model'
import { SequenceActivityStepStatus } from '../../domain/enum/sequence-activity-step-status.enum'
import { SequenceActivityStatus } from '../../domain/enum/sequence-activity-status.enum'
import { FormatTemplateCommand } from '../../../template-lead/application/command/format-template/format-template.command'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { FindLinkedinUserByUserIdQuery } from '../../../integration/captain-data/application/query/linkedin/find-linkedin-user-by-user-id/find-linkedin-user-by-user-id.query'
import { LinkedinUserModel } from '../../../integration/captain-data/domain/model/linkedin-user.model'
import { ProviderService } from '../../../mailer/application/service/provider.service'
import { ContactModel } from '../../../lead/domain/model/contact.model'
import { ScheduleEmailService } from './schedule-email.service'
import { ActivityError, History, Stats } from '../dto/sequence-activity.dto'
import { SequenceContactErrorsService } from './sequence-contact-errors.service'
import { FindInitialMessageForStepAndSequenceContactQuery } from '../../../mailer/application/query/find-message-by-step-and-contact-id/find-initial-message-by-step-and-contact.query'
import { MessageModel } from '../../../mailer/domain/model/message.model'
import { sequenceContactStatusDone } from '../../domain/sequence-contact-status.enum'
import { SequenceStepModel } from '../../domain/model/sequence-step.model'
import { SequenceContactStepModel } from '../../domain/model/sequence-contact-step.model'

@Injectable()
export class SequenceActivityService {
  private readonly logger = new Logger(SequenceActivityService.name)

  constructor(
    @Inject(SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE)
    private readonly sequenceActivityRepository: SequenceActivityRepositoryInterface,
    private readonly providerService: ProviderService,
    private readonly scheduleEmailService: ScheduleEmailService,
    private readonly sequenceContactErrorsService: SequenceContactErrorsService,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  async getPastHistories(
    pastActivities: SequenceActivityModel[],
    sequenceContactStepModels: SequenceContactStepModel[]
  ): Promise<History[]> {
    const histories: History[] = []
    for (const activity of pastActivities) {
      let stats
      if (
        [
          SequenceActivityType.STEP_EMAIL,
          SequenceActivityType.STEP_MANUAL_EMAIL,
          SequenceActivityType.STEP_REPLY_IN_THREAD,
        ].includes(activity.type)
      ) {
        stats = await this.getStats(
          activity.step.id,
          activity.sequenceContact.id,
          activity.sequenceContact.status,
          activity.status
        )
      }
      const { from, to, sender } = await this.getFromAndToAndSender(
        activity.sequence.organizationId,
        activity.sequenceContact.contact,
        activity.sequenceContact.contact.assignUser.id,
        activity.sequenceContact,
        {
          isMail: activity.isMailActivity(),
          isLinkedin: activity.isLinkedinActivity(),
          isCall: activity.isCallActivity(),
        }
      )

      let content = { from, to, ...(activity.content ?? {}), sender }

      // LinkedIn message should be correctly formatted for frontend preview
      if (content?.body) {
        content.body = content.body.replace(/\n/g, '<br />')
      }

      if (activity.status === SequenceActivityStepStatus.SKIPPED) {
        const { body, subject } = await this.getFormattedContent(
          activity.sequence.organizationId,
          activity.sequenceContact.contactId,
          activity.step.content
        )
        content = { ...content, subject, body }
      }
      // Is there any custom template ?
      const sequenceContactStepModel = sequenceContactStepModels.find(
        sequenceContactStepModel =>
          sequenceContactStepModel.stepId === activity.stepId
      )
      histories.push({
        status: SequenceActivityStatus.PAST,
        date: activity.date,
        name: activity.step.name,
        type: activity.type,
        data: {
          step: {
            id: activity.step.id,
            type: activity.step.type,
            waitingBetweenStep: activity.step.waitingBetweenStep,
            order: activity.step.order,
            status: activity.status,
            isMayHaveSkipped: false,
            manual: SequenceActivityModel.isManualStep(activity.step.type),
            stats,
            content:
              sequenceContactStepModel?.stepData ?? activity.step.content,
          },
          content,
          resourceId: activity.resourceId,
        },
      })
    }

    return histories
  }

  async getUpcomingHistories(
    sequenceSteps: SequenceStepModel[],
    sequenceContactModel: SequenceContactModel,
    sequenceContactStepModels: SequenceContactStepModel[],
    contact: ContactInterface
  ): Promise<History[]> {
    const nextExecutedStepOrder =
      sequenceContactModel.getNextExecutedStepOrder()

    if (nextExecutedStepOrder === null) {
      return []
    }

    const upcomingSteps = sequenceSteps
      .filter(step => step.order > nextExecutedStepOrder - 1)
      .sort((a, b) => b.order - a.order)

    const histories: History[] = []
    for (const step of upcomingSteps) {
      const { from, to, sender } = await this.getFromAndToAndSender(
        sequenceContactModel.sequence.organizationId,
        contact,
        contact.assignUser?.id,
        sequenceContactModel,
        {
          isLinkedin:
            SequenceActivityModel.isLinkedinActivityFromSequenceStepType(
              step.type
            ),
          isMail: SequenceActivityModel.isMailActivityFromSequenceStepType(
            step.type
          ),
          isCall:
            SequenceActivityModel.isCallActivityActivityFromSequenceStepType(
              step.type
            ),
        }
      )
      // Is there any custom template ?
      const sequenceContactStepModel = sequenceContactStepModels.find(
        sequenceContactStepModel => sequenceContactStepModel.stepId === step.id
      )
      const content = sequenceContactStepModel?.stepData ?? step.content
      const { body, subject } = await this.getFormattedContent(
        sequenceContactModel.sequence.organizationId,
        sequenceContactModel.contactId,
        content
      )
      const isMayHaveSkipped =
        ((sequenceContactModel.status ===
          SequenceContactStatus.CONTACT_BOUNCED &&
          sequenceContactModel.stepId === step.id) ||
          (sequenceContactModel.status !==
            SequenceContactStatus.CONTACT_BOUNCED &&
            sequenceContactModel.nextStepId === step.id)) &&
        sequenceContactModel.isSkippable()
      histories.push({
        status: SequenceActivityStatus.UPCOMING,
        name: step.name,
        type: SequenceActivityModel.getTypeFromSequenceStepType(step.type),
        data: {
          step: {
            id: step.id,
            type: step.type,
            status:
              sequenceContactModel.status ===
                SequenceContactStatus.ERROR_LINKEDIN_NO_CONNECTION ||
              (sequenceContactModel.nextStepId === step.id &&
                sequenceContactModel.status === SequenceContactStatus.SCHEDULE)
                ? SequenceActivityStepStatus.PENDING
                : undefined,
            waitingBetweenStep: step.waitingBetweenStep,
            order: step.order,
            isMayHaveSkipped,
            manual: SequenceActivityModel.isManualStep(step.type),
            content,
          },
          content: {
            from,
            to,
            subject,
            body,
            sender,
          },
        },
      })
    }

    return histories
  }

  private async getFormattedContent(
    organizationId: string,
    contactId: string,
    content: { subject?: string; body?: string }
  ): Promise<{ subject?: string; body?: string }> {
    let subject
    let body
    try {
      if (content.subject) {
        subject = await this.commandBus.execute(
          new FormatTemplateCommand(
            organizationId,
            content.subject,
            contactId,
            true
          )
        )
      }
      if (content.body) {
        body = await this.commandBus.execute(
          new FormatTemplateCommand(
            organizationId,
            content.body,
            contactId,
            true
          )
        )
      }
    } catch (e) {
      this.logger.warn('Error while formatting content: %s', e.message)
      subject = content.subject
      body = content.body
    }

    return { subject, body }
  }

  private async getFromAndToAndSender(
    organizationId: string,
    contact: ContactInterface,
    userId: string,
    sequenceContactModel: SequenceContactModel,
    type: { isMail: boolean; isLinkedin: boolean; isCall: boolean } = {
      isMail: false,
      isLinkedin: false,
      isCall: false,
    }
  ): Promise<{
    from: string
    to: string
    sender: {
      userId: string
      linkedinId?: string
      email?: string
    }
  }> {
    let from, to: string
    const sender: {
      userId: string
      linkedIn?: string
      email?: string
    } = { userId }

    if (type.isLinkedin) {
      const linkedinUser = await this.queryBus.execute<
        FindLinkedinUserByUserIdQuery,
        LinkedinUserModel
      >(new FindLinkedinUserByUserIdQuery(organizationId, userId))

      to = contact.linkedinUrl
      from = linkedinUser?.linkedinProfileUrl
      sender.linkedIn = linkedinUser?.linkedinProfileUrl
    }
    if (type.isMail) {
      const mailerAuthorization =
        await this.providerService.getAuthorization(userId)
      from = mailerAuthorization?.email

      const availableEmail = await this.scheduleEmailService.getEmail(
        contact as ContactModel,
        sequenceContactModel
      )
      to = availableEmail?.email
      sender.email = availableEmail?.email
    }
    if (type.isCall) {
      from = contact.assignUser?.id
      to = contact.phones?.[0]
    }
    return { from, to, sender }
  }

  async getErrors(
    stepModel: SequenceStepModel,
    sequenceContactstatus: SequenceContactStatus,
    contact: ContactInterface
  ): Promise<ActivityError> {
    let missingVariables = []
    if (
      sequenceContactstatus === SequenceContactStatus.ERROR_MISSING_VARIABLE
    ) {
      missingVariables =
        await this.sequenceContactErrorsService.getMissingVariablesForStep(
          stepModel,
          contact
        )
    }

    const contactErrors = {
      missingVariables:
        missingVariables.length > 0 ? missingVariables : undefined,
      code: this.isContactError(sequenceContactstatus)
        ? sequenceContactstatus
        : undefined,
    }

    const sequenceErrors = {
      code: this.isSequenceError(sequenceContactstatus)
        ? sequenceContactstatus
        : undefined,
    }

    const error = {
      ...(Object.keys(contactErrors).some(
        key => contactErrors[key] !== undefined
      ) && { contact: contactErrors }),
      ...(Object.keys(sequenceErrors).some(
        key => sequenceErrors[key] !== undefined
      ) && { sequence: sequenceErrors }),
    }

    if (Object.keys(error).length) {
      return error
    }

    return undefined
  }

  private isContactError(sequenceContactStatus: SequenceContactStatus) {
    return [
      SequenceContactStatus.CONTACT_UNASSIGNED,
      SequenceContactStatus.CONTACT_BOUNCED,
      SequenceContactStatus.CONTACT_ARCHIVED,
      SequenceContactStatus.ERROR_LINKEDIN_NO_CONNECTION,
      SequenceContactStatus.ERROR_MISSING_VARIABLE,
      SequenceContactStatus.ERROR_EMAIL_INVALID,
      SequenceContactStatus.ERROR_EMAIL_EMPTY,
      SequenceContactStatus.ERROR_LINKEDIN_URL_EMPTY,
    ].includes(sequenceContactStatus)
  }

  private isSequenceError(sequenceContactStatus: SequenceContactStatus) {
    return [
      SequenceContactStatus.ERROR,
      SequenceContactStatus.SDR_EMAIL_NOT_SYNC,
      SequenceContactStatus.SDR_LINKEDIN_NOT_SYNC,
      SequenceContactStatus.SDR_LINKEDIN_INVALID_COOKIE,
      SequenceContactStatus.CONTACT_UNSUBSCRIBED,
    ].includes(sequenceContactStatus)
  }

  private async getStats(
    stepId: string,
    sequenceContactId: string,
    sequenceContactStatus: SequenceContactStatus,
    sequenceActivityStepStatus: SequenceActivityStepStatus
  ): Promise<Stats> {
    const messageModel = await this.queryBus.execute<
      FindInitialMessageForStepAndSequenceContactQuery,
      MessageModel
    >(
      new FindInitialMessageForStepAndSequenceContactQuery(
        stepId,
        sequenceContactId
      )
    )
    return {
      nbClick: messageModel?.linkClickedDates?.length ?? 0,
      nbOpen: messageModel?.openedDates?.length ?? 0,
      isReply: sequenceContactStatus === SequenceContactStatus.CONTACT_ANSWERED,
      isBounced:
        sequenceActivityStepStatus === SequenceActivityStepStatus.BOUNCED,
      isUnsubscribe:
        sequenceContactStatus === SequenceContactStatus.CONTACT_UNSUBSCRIBED,
    }
  }

  fixOrders(
    histories: History[],
    sequenceSteps: SequenceStepModel[]
  ): History[] {
    return histories
      .map(history => {
        const realOrder = this.getRealStepOrder(
          sequenceSteps,
          history.data.step.id
        )
        history.data.step.order = realOrder
        return history
      })
      .sort((a, b) => a.data.step.order - b.data.step.order)
  }

  getRealStepOrder(
    sequenceSteps: SequenceStepModel[],
    targetStepId: string
  ): number {
    const sortedSteps = [...sequenceSteps].sort((a, b) => a.order - b.order)

    const targetIndex = sortedSteps.findIndex(step => step.id === targetStepId)

    return targetIndex + 1
  }

  getLastExecutedOrder(sequenceActivities: SequenceActivityModel[]): number {
    return Math.max(
      0,
      ...sequenceActivities
        .filter(
          activity => activity.status !== SequenceActivityStepStatus.BOUNCED
        )
        .map(activity => activity.step?.order || 0)
    )
  }

  isSequenceDone(sequenceContactModel: SequenceContactModel): boolean {
    return sequenceContactStatusDone().includes(sequenceContactModel.status)
  }

  async getSequenceProgressInPercentage(
    sequenceSteps: SequenceStepModel[],
    sequenceContactModel: SequenceContactModel
  ): Promise<number> {
    const activities =
      await this.sequenceActivityRepository.findBySequenceContactId(
        sequenceContactModel.id
      )

    const uniqueExecutedOrders = [
      ...new Set(activities.map(activity => activity.step?.order ?? 0)),
    ]

    const nextExecutedOrder = sequenceContactModel.getNextExecutedStepOrder()

    if (nextExecutedOrder === null) {
      return 100
    }

    const executedSteps = uniqueExecutedOrders.filter(
      order => order <= nextExecutedOrder - 1
    )

    const progress = (executedSteps.length / sequenceSteps.length) * 100

    return progress
  }
}
