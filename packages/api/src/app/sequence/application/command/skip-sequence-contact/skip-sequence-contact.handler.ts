import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { SkipSequenceContactCommand } from './skip-sequence-contact.command'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { SequenceEvents } from '../../../../shared/domain/event/event.enum'
import { SequenceStepSucceededEvent } from '../../../../shared/domain/event/sequence/sequence-step-succeeded.event'
import { SequenceActivityModel } from '../../../domain/model/sequence-activity.model'
import { SequenceActivityStepStatus } from '../../../domain/enum/sequence-activity-step-status.enum'
import { CreateSequenceActivityCommand } from '../create-sequence-activity/create-sequence-activity.command'

@CommandHandler(SkipSequenceContactCommand)
export class SkipSequenceContactHandler
  implements ICommandHandler<SkipSequenceContactCommand>
{
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly commandBus: CommandBus
  ) {}

  async execute(
    skipSequenceContactCommand: SkipSequenceContactCommand
  ): Promise<void> {
    const { sequenceContactModel, userId } = skipSequenceContactCommand

    if (!sequenceContactModel.isSkippable()) return

    // Fire a succeeded step event to prepare next sequence contact step
    const sequenceStepSucceededEvent = new SequenceStepSucceededEvent()
    sequenceStepSucceededEvent.sequenceContactId = sequenceContactModel.id
    this.eventEmitter.emit(
      SequenceEvents.SEQUENCE_STEP_SUCCEEDED,
      sequenceStepSucceededEvent
    )

    // Create sequence activity
    const sequenceActivityModel = new SequenceActivityModel()
    sequenceActivityModel.sequenceId = sequenceContactModel.sequenceId
    sequenceActivityModel.stepId = sequenceContactModel.getSequenceStepId()
    sequenceActivityModel.sequenceContactId = sequenceContactModel.id
    sequenceActivityModel.createdById = userId
    sequenceActivityModel.date = new Date()
    sequenceActivityModel.status = SequenceActivityStepStatus.SKIPPED
    sequenceActivityModel.type =
      SequenceActivityModel.getTypeFromSequenceStepType(
        sequenceContactModel.getSequenceStepType()
      )

    await this.commandBus.execute(
      new CreateSequenceActivityCommand(sequenceActivityModel)
    )
  }
}
