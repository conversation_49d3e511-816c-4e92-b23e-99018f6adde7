import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'
import { UpdateSequenceContactStatusCommand } from './update-sequence-contact-status.command'

@CommandHandler(UpdateSequenceContactStatusCommand)
export class UpdateSequenceContactStatus<PERSON>andler
  implements I<PERSON>ommandHandler<UpdateSequenceContactStatusCommand>
{
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    readonly sequenceContactRepository: SequenceContactRepositoryInterface
  ) {}

  async execute(
    updateSequenceContactStatusCommand: UpdateSequenceContactStatusCommand
  ): Promise<void> {
    return this.sequenceContactRepository.updateStatus(
      updateSequenceContactStatusCommand.sequenceContactId,
      updateSequenceContactStatusCommand.sequenceContactStatus
    )
  }
}
