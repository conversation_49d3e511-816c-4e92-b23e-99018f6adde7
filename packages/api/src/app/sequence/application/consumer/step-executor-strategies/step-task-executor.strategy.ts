import { CommandBus } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { SequenceContactStatus, SequenceStepType } from '@getheroes/shared'
import {
  CheckRequirementsResult,
  StepExecutorStrategy,
} from './step-executor.strategy'
import { SequenceContactModel } from '../../../domain/model/sequence-contact.model'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'

import { TaskModel } from '../../../../journey/domain/task/model/task.model'
import { CreateTaskCommand } from '../../../../journey/application/command/task/create-task/create-task.command'
import { TaskType } from '../../../../journey/domain/task/task-type.enum'

export class StepTaskExecutorStrategy implements StepExecutorStrategy {
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly commandBus: CommandBus,
    private readonly organizationId: string,
    private readonly sequenceStepType: SequenceStepType
  ) {}

  async getNumberOfRemainingJobs(): Promise<number | undefined> {
    return
  }

  async checkRequirementsForUser(): Promise<CheckRequirementsResult> {
    return CheckRequirementsResult.PASSED
  }

  async checkRequirementsForContact(): Promise<CheckRequirementsResult> {
    return CheckRequirementsResult.PASSED
  }

  async execute(sequenceContactModel: SequenceContactModel): Promise<boolean> {
    sequenceContactModel.status = SequenceContactStatus.SCHEDULE
    await this.sequenceContactRepository.update(sequenceContactModel)

    const taskModel = new TaskModel()
    taskModel.type =
      this.sequenceStepType === SequenceStepType.CALL
        ? TaskType.CALL
        : TaskType.EMAIL
    taskModel.organizationId = this.organizationId
    taskModel.contactId = sequenceContactModel.contactId
    taskModel.sequenceContactId = sequenceContactModel.id
    taskModel.assign = sequenceContactModel.contact.assignUser
    taskModel.createdBy = sequenceContactModel.createdBy
    taskModel.dateOfExecution = new Date()

    await this.commandBus.execute(new CreateTaskCommand(taskModel))
    return true
  }
}
