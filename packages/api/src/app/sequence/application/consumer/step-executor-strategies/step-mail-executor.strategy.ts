import { Inject } from '@nestjs/common'
import { SequenceContactStatus, SequenceStepType } from '@getheroes/shared'
import { ProviderService } from '../../../../mailer/application/service/provider.service'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'
import { ScheduleEmailService } from '../../service/schedule-email.service'
import {
  CheckRequirementsResult,
  StepExecutorStrategy,
} from './step-executor.strategy'
import { DailyLimitService } from '../../../../mailer/application/service/daily-limit.service'
import { ConfigScheduleService } from '../../service/config-schedule.service'
import { MailerAuthorizationInterface } from '../../../../integration/common/domain/interface/mailer-authorization.interface'
import { SequenceContactModel } from '../../../domain/model/sequence-contact.model'

export class StepMailExecutorStrategy implements StepExecutorStrategy {
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly dailyLimitService: DailyLimitService,
    private readonly configSchedule: ConfigScheduleService,
    private readonly providerService: ProviderService,
    private readonly scheduleEmailService: ScheduleEmailService,
    private readonly organizationId: string,
    private readonly assignUserId: string,
    private readonly stepType: SequenceStepType
  ) {}

  private authorization: MailerAuthorizationInterface

  private async getAuthorization(): Promise<MailerAuthorizationInterface> {
    if (this.authorization) return this.authorization
    this.authorization = await this.providerService.getAuthorization(
      this.assignUserId
    )
    return this.authorization
  }

  async getNumberOfRemainingJobs(): Promise<number | undefined> {
    return
  }

  async checkRequirementsForUser(
    sequenceContactModels: SequenceContactModel[]
  ): Promise<CheckRequirementsResult> {
    // Check: If an e-mail address has been configured for the SDR
    const authorization = await this.getAuthorization()

    if (!authorization?.email) {
      // "SDR" has not configured its email address
      const sequenceContactIds = sequenceContactModels.map(
        sequenceContact => sequenceContact.id
      )

      await this.sequenceContactRepository.updateStatusInBulk(
        sequenceContactIds,
        SequenceContactStatus.SDR_EMAIL_NOT_SYNC
      )

      return CheckRequirementsResult.SOFT_FAILED
    }

    // Check: If the daily mail quota has not been exceeded
    const remainingEmailCounter =
      await this.dailyLimitService.remainingEmailCounter(
        authorization.email,
        this.organizationId
      )

    if (remainingEmailCounter <= 0) {
      return CheckRequirementsResult.SOFT_FAILED
    }

    return CheckRequirementsResult.PASSED
  }

  async checkRequirementsForContact(
    sequenceContactModel: SequenceContactModel
  ): Promise<CheckRequirementsResult> {
    const sequenceModel = sequenceContactModel.sequence

    // Check: If is possible to send email with timezone
    const isAvailableToExecute = this.configSchedule.isAvailableToExecute(
      sequenceModel.scheduleDays,
      sequenceModel.scheduleTimeStart,
      sequenceModel.scheduleTimeEnd,
      sequenceModel.scheduleTimezone
    )
    if (!isAvailableToExecute) {
      return CheckRequirementsResult.HARD_FAILED
    }

    // Check: If the daily mail quota has not been exceeded, the counter must be rechecked in a loop
    const authorization = await this.getAuthorization()
    const remainingEmailCounter =
      await this.dailyLimitService.remainingEmailCounter(
        authorization.email,
        this.organizationId
      )

    if (remainingEmailCounter <= 0) {
      return CheckRequirementsResult.HARD_FAILED
    }

    // Check: Defines the number of contacts to be processed per day so as not to exceed the daily quota.
    const numberOfSecondsRemainingInTheDay =
      await this.configSchedule.numberOfSecondsRemainingInTheDay(
        sequenceContactModel.sequence.scheduleTimeEnd,
        sequenceContactModel.sequence.scheduleTimezone
      )
    const numberOfEmailsPerCycle =
      await this.configSchedule.numberOfEmailsPerCycle(
        numberOfSecondsRemainingInTheDay
      )

    if (numberOfEmailsPerCycle <= 0) {
      return CheckRequirementsResult.HARD_FAILED
    }

    return CheckRequirementsResult.PASSED
  }

  async execute(sequenceContactModel: SequenceContactModel): Promise<boolean> {
    // Schedule mail sending
    sequenceContactModel.status = SequenceContactStatus.SCHEDULE
    await this.sequenceContactRepository.update(sequenceContactModel)

    switch (this.stepType) {
      case SequenceStepType.EMAIL:
        return this.scheduleEmailService.sendMail(
          this.organizationId,
          sequenceContactModel
        )
      case SequenceStepType.REPLY_IN_THREAD:
        return this.scheduleEmailService.replyInThread(
          this.organizationId,
          sequenceContactModel
        )
      default:
        throw new Error(`Step type ${this.stepType} not supported`)
    }
  }
}
