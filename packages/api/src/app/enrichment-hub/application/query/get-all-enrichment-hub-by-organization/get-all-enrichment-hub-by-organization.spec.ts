import { beforeEach, describe, expect, it, jest } from '@jest/globals'
import { Test, TestingModule } from '@nestjs/testing'
import { GetAllEnrichmentHubByOrganizationQuery } from './get-all-enrichment-hub-by-organization.query'
import {
  ENRICHMENT_HUB_REPOSITORY_INTERFACE,
  EnrichmentHubRepositoryInterface,
} from '../../../domain/enrichment-hub-repository.interface'
import { EnrichmentHubModel } from '../../../domain/model/enrichment-hub.model'
import { v4 as uuidv4 } from 'uuid'
import { GetAllEnrichmentHubByOrganizationHandler } from './get-all-enrichment-hub-by-organization.handler'
import { Logger } from '@nestjs/common'
import { EnrichmentHubStatusEnum } from '@getheroes/shared'
import { sub } from 'date-fns'

describe('GetAllEnrichmentHubByOrganizationHandler', () => {
  let handler: GetAllEnrichmentHubByOrganizationHandler
  let repository: EnrichmentHubRepositoryInterface

  const mockEnrichmentHubRepository = () => ({
    findByOrganizationId: jest.fn(),
  })

  jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {})

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetAllEnrichmentHubByOrganizationHandler,
        {
          provide: ENRICHMENT_HUB_REPOSITORY_INTERFACE,
          useFactory: mockEnrichmentHubRepository,
        },
      ],
    }).compile()

    handler = module.get<GetAllEnrichmentHubByOrganizationHandler>(
      GetAllEnrichmentHubByOrganizationHandler
    )
    repository = module.get<EnrichmentHubRepositoryInterface>(
      ENRICHMENT_HUB_REPOSITORY_INTERFACE
    )
  })

  it('should return all enrichment hubs for an organization', async () => {
    const organizationId = uuidv4()
    const query = new GetAllEnrichmentHubByOrganizationQuery(organizationId)

    const enrichmentHubModels: EnrichmentHubModel[] = [
      {
        id: 'enrichmentHub-1',
        organizationId: organizationId,
        status: EnrichmentHubStatusEnum.DONE,
        createdAt: new Date(),
      } as unknown as EnrichmentHubModel,
      {
        id: 'enrichmentHub-2',
        organizationId: organizationId,
        status: EnrichmentHubStatusEnum.IN_PROCESS,
        createdAt: new Date(),
      } as unknown as EnrichmentHubModel,
    ]

    jest
      .spyOn(repository, 'findByOrganizationId')
      .mockResolvedValue(enrichmentHubModels)

    const result = await handler.execute(query)

    expect(result).toEqual(enrichmentHubModels)
    expect(repository.findByOrganizationId).toHaveBeenCalledWith(
      organizationId,
      { leadImport: true }
    )
  })

  // Note: The filtering logic has been moved to the repository layer
  it('should return the filtered enrichment hubs from the repository', async () => {
    const organizationId = uuidv4()
    const query = new GetAllEnrichmentHubByOrganizationQuery(organizationId)

    // These models represent what the repository would return after filtering
    const filteredEnrichmentHubModels: EnrichmentHubModel[] = [
      {
        id: 'enrichmentHub-2',
        organizationId: organizationId,
        status: EnrichmentHubStatusEnum.FAILED,
        createdAt: new Date(), // Recent FAILED
      } as unknown as EnrichmentHubModel,
      {
        id: 'enrichmentHub-3',
        organizationId: organizationId,
        status: EnrichmentHubStatusEnum.DONE,
        createdAt: new Date(),
      } as unknown as EnrichmentHubModel,
    ]

    jest
      .spyOn(repository, 'findByOrganizationId')
      .mockResolvedValue(filteredEnrichmentHubModels)

    const result = await handler.execute(query)

    // Should return exactly what the repository returned
    expect(result).toEqual(filteredEnrichmentHubModels)
    expect(repository.findByOrganizationId).toHaveBeenCalledWith(
      organizationId,
      { leadImport: true }
    )
  })
})
