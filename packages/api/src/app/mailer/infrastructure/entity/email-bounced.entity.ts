import { AutoMap } from '@automapper/classes'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'

@Entity({ name: 'email_bounced' })
export class EmailBouncedEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @AutoMap()
  @Column('varchar', { nullable: false, unique: true })
  @Index()
  email: string

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity
}
