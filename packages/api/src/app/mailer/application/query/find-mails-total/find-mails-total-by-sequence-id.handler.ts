import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from '@nestjs/cqrs'
import {Inject} from '@nestjs/common'
import {FindMailsTotalBySequenceIdQuery} from './find-mails-total-by-sequence-id.query'
import {
  MESSAGE_REPOSITORY_INTERFACE,
  MessageRepositoryInterface
} from "../../../domain/interface/repository/message-repository.interface";

@QueryHandler(FindMailsTotalBySequenceIdQuery)
export class FindMailsTotalBySequenceIdHandler
  implements IQueryHandler<FindMailsTotalBySequenceIdQuery>
{
  constructor(
    @Inject(MESSAGE_REPOSITORY_INTERFACE)
    private readonly messageRepository: MessageRepositoryInterface
  ) {}

  async execute(query: FindMailsTotalBySequenceIdQuery): Promise<number> {
    return await this.messageRepository.countResourceThreadIdsBySequenceId(
      query.sequenceId
    )
  }
}
