import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { Inject, Injectable } from '@nestjs/common'
import {
  DNS_SETTINGS_REPOSITORY_INTERFACE,
  DnsSettingsRepositoryInterface,
} from '../../domain/interface/repository/dns-settings-repository.interface'
import { CommandBus } from '@nestjs/cqrs'
import { UpdateDnsSettingsCommand } from '../command/update-dns-settings/update-dns-settings.command'

let isRunning = false

@Injectable()
export class RefreshDnsSettingsCron {
  constructor(
    @Inject(DNS_SETTINGS_REPOSITORY_INTERFACE)
    readonly dnsSettingsRepository: DnsSettingsRepositoryInterface,
    readonly commandBus: CommandBus
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async refreshDns() {
    if (isRunning) {
      return
    }
    isRunning = true

    const refreshDate = new Date()
    refreshDate.setDate(refreshDate.getDate() - 30)

    const settingsToRefresh =
      await this.dnsSettingsRepository.findByUpdatedAt(refreshDate)

    for (let i = 0; i < settingsToRefresh.length; i++) {
      const dnsSettings = settingsToRefresh[i]

      await this.commandBus.execute(
        new UpdateDnsSettingsCommand(
          dnsSettings.organizationId,
          dnsSettings.domain
        )
      )
    }

    isRunning = false
  }
}
