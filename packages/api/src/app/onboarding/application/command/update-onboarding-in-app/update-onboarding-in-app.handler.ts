import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryBus } from '@nestjs/cqrs'
import { OnboardingInAppModel } from '../../../domain/model/onboarding-in-app.model'
import { UpdateOnboardingInAppCommand } from './update-onboarding-in-app.command'
import { FindOneOnboardingInAppQuery } from '../../query/find-one-onboarding-in-app/find-one-onboarding-in-app.query'
import { FindOrganizationQuery } from '../../../../organization/application/query/find-organization/find-organization.query'
import { BaseEvent, NameEvent } from '@getheroes/shared'
import { WebSocketService } from '../../../../shared/infrastructure/service/websocket.service'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../../../shared/logger.service'
import { Inject } from '@nestjs/common'
import {
  ONBOARDING_IN_APP_REPOSITORY_INTERFACE,
  OnboardingInAppRepositoryInterface,
} from '../../../domain/onboarding-in-app-repository.interface'

@CommandHandler(UpdateOnboardingInAppCommand)
export class UpdateOnboardingInAppHandler
  implements ICommandHandler<UpdateOnboardingInAppCommand>
{
  constructor(
    private readonly websocketService: WebSocketService,
    @Inject(ONBOARDING_IN_APP_REPOSITORY_INTERFACE)
    private readonly onboardingInAppRepository: OnboardingInAppRepositoryInterface,
    private readonly queryBus: QueryBus
  ) {}

  @TryCatchLogger({
    feature: LogFeature.ONBOARDING,
    message: 'Failed to update onboarding in app',
  })
  async execute(
    command: UpdateOnboardingInAppCommand
  ): Promise<OnboardingInAppModel> {
    const { isUserNotified, stepName, organizationId } =
      command.updateOnboardingInAppDto

    const onboardingInAppModel: OnboardingInAppModel =
      await this.queryBus.execute(
        new FindOneOnboardingInAppQuery({ id: organizationId }, stepName)
      )

    const updatedOnboardingInApp = await this.onboardingInAppRepository.update(
      new OnboardingInAppModel({
        ...onboardingInAppModel,
        isUserNotified,
      })
    )

    // Dispatch update event via WS
    const baseEvent = new BaseEvent(
      NameEvent.IN_APP_ONBOARDING_UPDATED,
      updatedOnboardingInApp
    )
    const organization = await this.queryBus.execute(
      new FindOrganizationQuery(organizationId)
    )
    const sendPromises = organization.members.map(member => {
      const userId = member.member.id // The naming is misleading here and should be member.user.id
      return this.websocketService.send(userId, JSON.stringify(baseEvent))
    })
    await Promise.all(sendPromises)

    return updatedOnboardingInApp
  }
}
