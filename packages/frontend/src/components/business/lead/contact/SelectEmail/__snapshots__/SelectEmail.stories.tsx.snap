// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/business/lead/contact/SelectEmail Default smoke-test 1`] = `
<div data-testid="storybook-select-email-container"
     class="c-form-field w-full c-form-field--no-stacking"
     style="z-index: var(--zFormFieldForm);"
>
  <div class="c-form-field__form c-select w-full !z-zSelectMenu css-b62m3t-container"
       id="storybook-select-email-select"
  >
    <span id="react-select-storybook-select-email-select-instance-id-live-region"
          class="css-1f43avz-a11yText-A11yText"
    >
    </span>
    <span aria-live="polite"
          aria-atomic="false"
          aria-relevant="additions text"
          class="css-1f43avz-a11yText-A11yText"
    >
    </span>
    <div class="c-select__control css-13cymwt-control">
      <div class="c-select__value-container c-select__value-container--has-value css-art2ul-ValueContainer2">
        <div class="c-select__single-value css-1dimb5e-singleValue">
          <div class="flex gap-2 items-center">
            <div class="flex justify-between items-center w-full label-s">
              <div class="flex items-center">
                <div class="mr-2">
                  <EMAIL>
                </div>
                <div class="mr-2 mt-1">
                  <div data-state="closed">
                    <svg width="12"
                         height="12"
                         viewbox="0 0 12 12"
                         fill="red"
                         xmlns="http://www.w3.org/2000/svg"
                    >
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M6 0.625C3.03147 0.625 0.625 3.03147 0.625 6C0.625 8.96855 3.03147 11.375 6 11.375C8.96855 11.375 11.375 8.96855 11.375 6C11.375 3.03147 8.96855 0.625 6 0.625ZM3.76522 5.98485C3.61877 5.8384 3.38134 5.8384 3.23489 5.98485C3.08844 6.1313 3.08844 6.3687 3.23489 6.51515L4.73489 8.01515C4.88134 8.1616 5.1188 8.1616 5.2652 8.01515L8.7652 4.51516C8.91165 4.36872 8.91165 4.13128 8.7652 3.98483C8.61875 3.83839 8.38135 3.83839 8.2349 3.98483L5.00005 7.21965L3.76522 5.98485Z"
                            fill="var(--backgroundDecorativeBrandContrast)"
                      >
                      </path>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-x-2">
                <div data-state="closed">
                  <div class="c-tag shrink-0 px-2 c-tag--radius-xxl c-tag--default c-tag--size-s">
                    Pro
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="c-select__input-container css-w9q2zk-Input2"
             data-value
        >
          <input class="c-select__input"
                 autocapitalize="none"
                 autocomplete="off"
                 autocorrect="off"
                 id="storybook-select-email"
                 spellcheck="false"
                 tabindex="0"
                 type="text"
                 aria-autocomplete="list"
                 aria-expanded="false"
                 aria-haspopup="true"
                 aria-invalid="false"
                 role="combobox"
                 value
                 style="color: inherit; background: 0px center; opacity: 1; width: 100%; grid-area: 1 / 2 / auto / auto; font: inherit; min-width: 2px; border: 0px; margin: 0px; outline: 0px; padding: 0px;"
          >
        </div>
      </div>
      <div class="c-select__indicators css-4xgw5l-IndicatorsContainer2">
        <span class="c-select__indicator-separator css-1u9des2-indicatorSeparator">
        </span>
        <div class="c-select__indicator c-select__dropdown-indicator css-1xc3v61-indicatorContainer"
             aria-hidden="true"
        >
          <svg height="20"
               width="20"
               viewbox="0 0 20 20"
               aria-hidden="true"
               focusable="false"
               class="css-tj5bde-Svg"
          >
            <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z">
            </path>
          </svg>
        </div>
      </div>
    </div>
    <input name="storybook-select-email"
           type="hidden"
           value="<EMAIL>"
    >
  </div>
</div>
`;
