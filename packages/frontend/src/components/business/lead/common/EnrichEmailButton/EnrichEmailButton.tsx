import { useTranslation } from 'react-i18next'

import { EnrichmentType } from '@getheroes/shared'
import type { Contact } from '@getheroes/shared'
import { Button } from '@getheroes/ui'
import { useGetProvidersStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetProvidersStatus'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'

interface EnrichEmailButtonProps {
  contact: Contact
}

export const EnrichEmailButton = ({ contact }: EnrichEmailButtonProps) => {
  /* Vars */

  const { t } = useTranslation('common')

  /* Queries */

  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()
  const { isLoadingEnrichment } = useGetProvidersStatus({
    enrichmentType: EnrichmentType.EMAIL,
    lead: contact,
  })

  return (
    <Button
      variant="conversion-data"
      size="medium"
      onClick={() => enrichPhoneOrEmail([contact], EnrichmentType.EMAIL)}
      iconLeft="MagicWand"
      fullWidth
      loading={isLoadingEnrichment}
    >
      {t('Enrich email') as string}
    </Button>
  )
}
