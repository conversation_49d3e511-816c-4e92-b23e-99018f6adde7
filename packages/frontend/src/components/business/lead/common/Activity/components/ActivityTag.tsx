import { useTranslation } from 'react-i18next'

import { ActivityType } from '@getheroes/shared'
import { Tag } from '@getheroes/ui'
import { isConnected } from '@internals/components/business/lead/common/Activity/utils/isConnected'
import type {
  Activity as ActivityModel,
  CallActivityModel,
} from '@internals/features/lead/types/activity'

export const ActivityTag = ({ activity }: { activity: ActivityModel }) => {
  /* Vars */

  const { t } = useTranslation('task')

  if (
    activity.type === ActivityType.LINKEDIN_MESSAGE_SENT ||
    activity.type === ActivityType.LINKEDIN_INVITATION_SENT ||
    activity.type === ActivityType.MAILER_MESSAGE_SENT ||
    activity.type === ActivityType.MAILER_MESSAGE_REPLY_SENT
  ) {
    return <Tag label={t('Sent')} color="green" size="small" />
  }

  if (
    activity.type === ActivityType.LINKEDIN_MESSAGE_RECEIVED ||
    activity.type === ActivityType.MAILER_MESSAGE_RECEIVED
  ) {
    return <Tag label={t('Replied')} color="blue" size="small" />
  }

  if (activity.type === ActivityType.LINKEDIN_PROFIL_VISITED) {
    return <Tag label={t('Done')} color="green" size="small" />
  }

  if (activity.type === ActivityType.LINKEDIN_INVITATION_ACCEPTED) {
    return <Tag label={t('Accepted')} color="blue" size="small" />
  }

  if (
    activity.type === ActivityType.CALL_INBOUND ||
    activity.type === ActivityType.CALL_OUTBOUND
  ) {
    const payload = activity.payload as CallActivityModel

    if (
      isConnected({
        start: payload.startAt ? new Date(payload.startAt) : null,
        end: payload.endAt ? new Date(payload.endAt) : null,
      })
    ) {
      return <Tag label={t('Connected')} color="green" size="small" />
    } else {
      return <Tag label={t('Not Connected')} color="red" size="small" />
    }
  }

  if (
    activity.type === ActivityType.SEQUENCE_CONTACT_OPENED ||
    activity.type === ActivityType.MAIL_OPENED
  ) {
    return <Tag label={t('Opened')} color="blue" size="small" />
  }

  if (
    activity.type === ActivityType.SEQUENCE_CONTACT_CLICKED ||
    activity.type === ActivityType.MAIL_LINK_CLICKED
  ) {
    return <Tag label={t('Clicked')} color="blue" size="small" />
  }

  if (activity.type === ActivityType.SEQUENCE_CONTACT_BOUNCED) {
    return <Tag label={t('Bounced')} color="red" size="small" />
  }

  if (activity.type === ActivityType.SEQUENCE_CONTACT_UNSUBSCRIBED) {
    return <Tag label={t('Unsubscribed')} color="red" size="small" />
  }

  return null
}
