import { useEffect, useMemo, useState } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { SequenceActivitiesHistory } from '@getheroes/frontend/types'
import type { Contact, Organization } from '@getheroes/shared'
import { isHistoryLinkedInType } from '@internals/components/business/ContactSequenceActivities/utils/isHistoryLinkedInType'
import type { ThreadType } from '@internals/components/business/lead/contact/Thread/Thread'
import { linkedInThreadAdapter } from '@internals/components/business/lead/contact/Thread/adapters/linkedInThreadAdapter'
import { messageMailAdapter } from '@internals/components/business/lead/contact/Thread/adapters/messageMailAdapter'
import { sequenceActivitiesHistoryAdapter } from '@internals/components/business/lead/contact/Thread/adapters/sequenceActivitiesHistoryAdapter'
import { useLazyGetThreadQuery } from '@internals/features/gmail/api/mailApi'
import { useLazyGetThreadLinkedInQuery } from '@internals/features/integration/api/linkedinApi'
import { useTypedSelector } from '@internals/store/store'

export const useGetThread = ({
  historyPreview,
  contact,
}: {
  historyPreview: SequenceActivitiesHistory | null
  contact?: Contact
}) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [getThreadGmail] = useLazyGetThreadQuery()
  const [getThreadLinkedIn] = useLazyGetThreadLinkedInQuery()

  const [isLoading, setIsLoading] = useState(false)
  const [thread, setThread] = useState<ThreadType[]>([])

  // IF the history has a resourceId
  // THEN the email was sent from the system => fetch the thread
  // ELSE the email was not sent yet => display the content in history.data.content
  useEffect(() => {
    const from = {
      firstName: contact?.assignUser?.firstName,
      lastName: contact?.assignUser?.lastName,
    }
    const to = {
      firstName: contact?.firstName,
      lastName: contact?.lastName,
      linkedInUrl: contact?.linkedinUrl,
    }

    const fetchThread = async () => {
      if (!historyPreview) {
        return
      }

      setIsLoading(true)

      if (historyPreview?.data?.resourceId) {
        let data

        if (isHistoryLinkedInType(historyPreview.type)) {
          data = await getThreadLinkedIn({
            organizationId,
            threadId: historyPreview?.data?.resourceId,
          }).unwrap()
          data = linkedInThreadAdapter(data, { from, to })
        } else {
          data = await getThreadGmail({
            organizationId,
            messageId: historyPreview?.data?.resourceId,
            orderBy: 'asc',
          }).unwrap()
          data = data.map(datum => messageMailAdapter(datum, { from, to }))
        }

        setThread(data)
      } else if (historyPreview.data && historyPreview.data.content) {
        const threadFromHistory = sequenceActivitiesHistoryAdapter(
          historyPreview,
          { from, to }
        )

        if (threadFromHistory) {
          setThread([threadFromHistory])
        }
      }

      setIsLoading(false)
    }

    // noinspection JSIgnoredPromiseFromCall
    fetchThread()
  }, [
    contact,
    getThreadGmail,
    getThreadLinkedIn,
    historyPreview,
    organizationId,
  ])

  return useMemo(
    () => ({
      isLoading,
      thread,
    }),
    [isLoading, thread]
  )
}
