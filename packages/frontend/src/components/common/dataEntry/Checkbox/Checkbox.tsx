import { clsx } from 'clsx'
import type { InputHTMLAttributes, Ref } from 'react'
import { forwardRef } from 'react'

import { OptionalTag } from '@internals/components/common/dataEntry/components/OptionalTag'
import type { FormFields } from '@internals/models/form'

import './Checkbox.scoped.scss'

export type InputCheckboxProps = FormFields & {
  name: string
  label?: string
  isChecked?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  inputClassName?: string
  isLabelOnRight?: boolean
  onBlur?: () => void
  onTrigger?: () => void
  indeterminate?: boolean
  isDisabled?: boolean
  isRequired?: boolean
} & InputHTMLAttributes<HTMLInputElement>

export const InputCheckbox = forwardRef<HTMLInputElement, InputCheckboxProps>(
  (
    {
      dataTestId,
      name,
      label,
      isChecked,
      isShowOptional,
      isDisabled = false,
      onChange,
      onBlur,
      // style
      className,
      labelClassName,
      inputClassName,
      isStackingFormfield = true,
      // validation
      trigger,
      isRequired,
      error,
      indeterminate,
      hidden,
      ...props
    },
    ref: Ref<HTMLInputElement>
  ) => {
    const id = `checkbox-${name}`

    return (
      <div
        className={clsx('flex flex-col c-checkbox', {
          hidden,
        })}
      >
        <div
          className={clsx('c-form-field', className, {
            'c-form-field--no-stacking': !isStackingFormfield,
          })}
        >
          <div className={clsx('flex items-center gap-2')}>
            <input
              id={id}
              data-testid={dataTestId}
              name={name}
              ref={ref}
              checked={isChecked}
              type="checkbox"
              disabled={isDisabled}
              required={isRequired}
              className={clsx('c-checkbox__input', inputClassName, {
                'has-error': error,
                'c-checkbox--no-stacking': !isStackingFormfield,
                indeterminate,
              })}
              onChange={onChange ? e => onChange(e) : () => null}
              onBlur={() => (onBlur && onBlur()) || (trigger && trigger(name))}
              hidden={hidden}
              {...props}
            />
            {label && (
              <label
                className={clsx(
                  'label-xs text-textLabel flex gap-2 items-center cursor-pointer',
                  labelClassName
                )}
                htmlFor={id}
              >
                {label} {isShowOptional && <OptionalTag />}
              </label>
            )}
          </div>
        </div>
        {error && <p className="text-textError text-size02">{error}</p>}
      </div>
    )
  }
)
