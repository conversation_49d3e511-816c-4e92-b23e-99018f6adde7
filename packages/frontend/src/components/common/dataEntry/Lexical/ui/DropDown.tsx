import { clsx } from 'clsx'
import isUndefined from 'lodash/isUndefined'
import type {
  KeyboardEvent,
  ReactElement,
  ReactNode,
  Ref,
  RefObject,
} from 'react'
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { Icon } from '@getheroes/ui'
import { Popover } from '@internals/components/common/dataDisplay/Popover/Popover'
import { PopoverContent } from '@internals/components/common/dataDisplay/Popover/PopoverContent'
import { PopoverTrigger } from '@internals/components/common/dataDisplay/Popover/PopoverTrigger'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { idReferentials } from '@internals/utils/idReferentials'

type DropDownContextType = {
  registerItem: (ref: RefObject<HTMLButtonElement>) => void
}

const DropDownContext = createContext<DropDownContextType | null>(null)

export const DropDownItem = ({
  children,
  className,
  onClick,
  title = '',
  tooltipText,
  renderAs = 'button',
  variantButton = ButtonVariant.LINK,
  icon,
}: {
  children: ReactNode
  className: string
  onClick?: (
    event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>
  ) => void
  title?: string
  tooltipText?: string
  renderAs?: 'button' | 'div'
  variantButton?: ButtonVariant
  icon?: ReactElement | never | undefined
}) => {
  const ref = useRef<HTMLButtonElement>(null)

  const dropDownContext = useContext(DropDownContext)

  const buttonParams = useMemo(() => {
    if (!icon) {
      return {}
    }
    return {
      startIcon: icon,
    }
  }, [icon])

  if (dropDownContext === null) {
    throw new Error('DropDownItem must be used within a DropDown')
  }

  const { registerItem } = dropDownContext

  useEffect(() => {
    if (ref && ref.current) {
      registerItem(ref)
    }
  }, [ref, registerItem])

  return renderAs === 'button' ? (
    <Button
      dataTestId={idReferentials.lexical.components.dropdown.itemButton}
      variant={variantButton}
      className={className}
      onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
        !isUndefined(onClick) && onClick(e)
      }
      ref={ref}
      title={title}
      type="button"
      {...buttonParams}
    >
      {children}
    </Button>
  ) : (
    <div
      className={className}
      onClick={(e: React.MouseEvent<HTMLDivElement>) =>
        !isUndefined(onClick) && onClick(e)
      }
    >
      {children}
    </div>
  )
}

function DropDownItems({
  children,
  dropDownRef,
  onClose,
  className,
}: {
  children: ReactNode
  dropDownRef: Ref<HTMLDivElement>
  onClose: () => void
  className?: string
}) {
  const [items, setItems] = useState<RefObject<HTMLButtonElement>[]>()
  const [highlightedItem, setHighlightedItem] =
    useState<RefObject<HTMLButtonElement>>()

  const registerItem = useCallback(
    (itemRef: RefObject<HTMLButtonElement>) => {
      setItems(prev => (prev ? [...prev, itemRef] : [itemRef]))
    },
    [setItems]
  )

  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
    if (!items) {
      return
    }

    const key = event.key

    if (['Escape', 'ArrowUp', 'ArrowDown', 'Tab'].includes(key)) {
      event.preventDefault()
    }

    if (key === 'Escape' || key === 'Tab') {
      onClose()
    } else if (key === 'ArrowUp') {
      setHighlightedItem(prev => {
        if (!prev) {
          return items[0]
        }
        const index = items.indexOf(prev) - 1
        return items[index === -1 ? items.length - 1 : index]
      })
    } else if (key === 'ArrowDown') {
      setHighlightedItem(prev => {
        if (!prev) {
          return items[0]
        }
        return items[items.indexOf(prev) + 1]
      })
    }
  }

  const contextValue = useMemo(
    () => ({
      registerItem,
    }),
    [registerItem]
  )

  useEffect(() => {
    if (items && !highlightedItem) {
      setHighlightedItem(items[0])
    }

    if (highlightedItem && highlightedItem.current) {
      highlightedItem.current.focus()
    }
  }, [items, highlightedItem])

  return (
    <DropDownContext.Provider value={contextValue}>
      <div
        className={clsx('dropdown')}
        ref={dropDownRef}
        onKeyDown={handleKeyDown}
      >
        {children}
      </div>
    </DropDownContext.Provider>
  )
}

const dropDownPadding = 4

export const DropDown = ({
  disabled = false,
  buttonLabel,
  buttonAriaLabel,
  buttonSize = ButtonSize.SMALL,
  buttonClassName,
  buttonIconClassName,
  children,
  stopCloseOnClickSelf,
  tooltip,
  className,
  handleOnClose,
  onlyIcon,
  icon,
}: {
  disabled?: boolean
  buttonAriaLabel?: string
  tooltip?: string
  buttonClassName?: string
  buttonIconClassName?: string
  className?: string
  buttonLabel?: string | ReactNode
  buttonSize?: ButtonSize
  children: ReactNode
  stopCloseOnClickSelf?: boolean
  handleOnClose?: () => void
  onlyIcon?: boolean
  icon?: ReactElement | never | undefined
}): JSX.Element => {
  const dropDownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement | HTMLDivElement>(null)
  const [showDropDown, setShowDropDown] = useState(false)
  const [checkDirection, setCheckDirection] = useState(false)

  const buttonParams = useMemo(() => {
    if (onlyIcon) {
      return {}
    }
    return {
      endIcon: <Icon name={'NavArrowDown'} />,
    }
  }, [])

  const handleClose = () => {
    setShowDropDown(false)
    handleOnClose && handleOnClose()
    if (buttonRef && buttonRef.current) {
      buttonRef.current.focus()
    }
  }

  useEffect(() => {
    const button = buttonRef.current
    const dropDown = dropDownRef.current
    if (!button || !dropDown) {
      return
    }
    if (showDropDown) {
      const buttonClientRect = button.getBoundingClientRect()
      const dropDownClientRect = dropDown.getBoundingClientRect()
      setCheckDirection(
        buttonClientRect.y + dropDownClientRect.height > window.innerHeight
      )
    }
  }, [showDropDown, buttonRef, dropDownRef])

  useEffect(() => {
    const button = buttonRef.current
    const dropDown = dropDownRef.current
    if (!button || !dropDown) {
      return
    }

    if (showDropDown) {
      const buttonClientRect = button.getBoundingClientRect()
      const dropDownClientRect = dropDown.getBoundingClientRect()
      if (checkDirection) {
        dropDown.style.top = `${
          buttonClientRect.top - dropDownClientRect.height - dropDownPadding
        }px`
      } else {
        dropDown.style.top = `${
          buttonClientRect.top + button.offsetHeight + dropDownPadding
        }px`
      }

      dropDown.style.left = `${Math.min(
        buttonClientRect.left,
        window.innerWidth - dropDown.offsetWidth - 20
      )}px`
    }
  }, [checkDirection, showDropDown, buttonRef, dropDownRef])

  useEffect(() => {
    const button = buttonRef.current

    if (button !== null && showDropDown) {
      const handle = (event: MouseEvent) => {
        const target = event.target
        if (stopCloseOnClickSelf) {
          if (
            dropDownRef.current &&
            dropDownRef.current.contains(target as Node)
          ) {
            return
          }
        }
        if (!button.contains(target as Node)) {
          setShowDropDown(false)
        }
      }
      document.addEventListener('click', handle)

      return () => {
        document.removeEventListener('click', handle)
      }
    }
  }, [dropDownRef, buttonRef, showDropDown, stopCloseOnClickSelf])

  return (
    <Popover
      dataTestId={'lexical-dropdown'}
      isOpenOnHover={false}
      isOpen={showDropDown}
      onOpenChange={() => setShowDropDown(v => !v)}
    >
      <PopoverTrigger
        asChild
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation()
          handleOnClose && handleOnClose()
          setShowDropDown(!showDropDown)
        }}
      >
        <Button
          dataTestId={idReferentials.lexical.components.dropdown.button}
          variant={ButtonVariant.TERTIARY}
          disabled={disabled}
          className={clsx(
            'toolbar-item justify-between items-center',
            buttonClassName
          )}
          ref={buttonRef as Ref<HTMLButtonElement>}
          startIcon={icon}
          tooltipText={tooltip}
          size={buttonSize}
          {...buttonParams}
          // endIcon={<NavArrowDown />}
        >
          {buttonIconClassName && <span className={buttonIconClassName} />}
          {buttonLabel && (
            <div className="flex w-full items-center">
              <span className="label-s text-textLabel">{buttonLabel}</span>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <DropDownItems
          className={className}
          dropDownRef={dropDownRef}
          onClose={handleClose}
        >
          {children}
        </DropDownItems>
      </PopoverContent>

      {/*{showDropDown &&*/}
      {/*  createPortal(*/}
      {/*    <DropDownItems*/}
      {/*      className={className}*/}
      {/*      dropDownRef={dropDownRef}*/}
      {/*      onClose={handleClose}*/}
      {/*    >*/}
      {/*      {children}*/}
      {/*    </DropDownItems>,*/}
      {/*    document.body*/}
      {/*  )}*/}
    </Popover>
  )
}
