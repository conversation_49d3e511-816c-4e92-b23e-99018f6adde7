// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/common/dataEntry/Slider Default smoke-test 1`] = `
<div class="flex justify-center w-full items-center min-h-full">
  <div class="rc-slider c-slider--brand rc-slider-horizontal">
    <div class="rc-slider-rail">
    </div>
    <div class="rc-slider-track"
         style="left: 0%; width: 30%;"
    >
    </div>
    <div class="rc-slider-step">
    </div>
    <div class="rc-slider-handle"
         tabindex="0"
         role="slider"
         aria-valuemin="0"
         aria-valuemax="100"
         aria-valuenow="30"
         aria-disabled="false"
         aria-orientation="horizontal"
         style="left: 30%; transform: translateX(-50%);"
    >
    </div>
  </div>
</div>
`;

exports[`components/common/dataEntry/Slider Plg smoke-test 1`] = `
<div class="flex justify-center w-full items-center min-h-full">
  <div class="rc-slider c-slider--plg rc-slider-horizontal">
    <div class="rc-slider-rail">
    </div>
    <div class="rc-slider-track"
         style="left: 0%; width: 30%;"
    >
    </div>
    <div class="rc-slider-step">
    </div>
    <div class="rc-slider-handle"
         tabindex="0"
         role="slider"
         aria-valuemin="0"
         aria-valuemax="100"
         aria-valuenow="30"
         aria-disabled="false"
         aria-orientation="horizontal"
         style="left: 30%; transform: translateX(-50%);"
    >
    </div>
  </div>
</div>
`;
