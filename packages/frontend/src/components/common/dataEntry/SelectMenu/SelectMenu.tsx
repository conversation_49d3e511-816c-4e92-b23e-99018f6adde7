import { clsx } from 'clsx'
import type { ReactNode } from 'react'
import { useState } from 'react'

import { Popover } from '@internals/components/common/dataDisplay/Popover/Popover'
import { PopoverContent } from '@internals/components/common/dataDisplay/Popover/PopoverContent'
import { PopoverTrigger } from '@internals/components/common/dataDisplay/Popover/PopoverTrigger'
import type { SelectItemProps } from '@internals/components/common/dataEntry/SelectMenu/SelectMenuItem'
import { SelectMenuItem } from '@internals/components/common/dataEntry/SelectMenu/SelectMenuItem'
import type { FormFields } from '@internals/models/form'

import './SelectMenu.scoped.scss'

export interface ItemsWithCategory {
  category: string
  items: SelectItemProps<KeyType>[]
}

export type SelectMenuProps<KeyType = string> = FormFields & {
  isMultipleSelection?: boolean
  items: SelectItemProps<KeyType>[]
  itemsWithCategories?: ItemsWithCategory[]
  button: ReactNode
  openedButton?: ReactNode
  searchOptionsComponent?: JSX.Element
  closeOnSelect?: boolean
  popoverClassName?: string
  isOpenOnHover?: boolean
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}

const DEFAULT_MAX_HEIGHT = 500

export const SelectMenu = <KeyType extends string = string>({
  dataTestId,
  items,
  itemsWithCategories,
  isMultipleSelection,
  button,
  openedButton,
  searchOptionsComponent,
  closeOnSelect = false,
  popoverClassName,
  isOpenOnHover = false,
  container,
}: SelectMenuProps<KeyType>) => {
  const [open, setOpen] = useState<boolean>(false)

  return (
    <Popover
      className={clsx('c-select-menu', popoverClassName)}
      container={container}
      isOpen={open}
      isOpenOnHover={isOpenOnHover}
      onOpenChange={() => setOpen(v => !v)}
    >
      <PopoverTrigger
        className={'w-full justify-between items-center'}
        onClick={() => setOpen(true)}
        asChild
      >
        <div className={'h-full'}>
          {open && openedButton ? openedButton : button}
        </div>
      </PopoverTrigger>

      <PopoverContent
        className={clsx('p-2 flex z-zPopoverAboveDrawer')}
        style={{ maxHeight: DEFAULT_MAX_HEIGHT as number }}
      >
        <div className={'w-full'}>
          {searchOptionsComponent}
          <div className={clsx('c-select-menu__options')}>
            {itemsWithCategories
              ? itemsWithCategories.map(
                  (category: ItemsWithCategory, index: number) => (
                    <div key={`${category}-${index}`}>
                      <div className={'c-select-menu__category'}>
                        {category.category}
                      </div>
                      {category.items.map((item, index) => (
                        <div
                          key={`${item.key}-${index}`}
                          onClick={() => {
                            if (closeOnSelect) {
                              setOpen(false)
                            }
                          }}
                        >
                          <SelectMenuItem<KeyType>
                            dataTestId={`${dataTestId}-item-${item.key.replace(
                              ' ',
                              '-'
                            )}`}
                            item={item}
                            key={item.key}
                            isMultipleSelection={isMultipleSelection}
                          />
                        </div>
                      ))}
                    </div>
                  )
                )
              : items.map((item: SelectItemProps<KeyType>, index: number) => (
                  <div
                    key={`${item.key}-${index}`}
                    onClick={() => {
                      if (closeOnSelect) {
                        setOpen(false)
                      }
                    }}
                  >
                    <SelectMenuItem<KeyType>
                      dataTestId={`${dataTestId}-item-${item.key.replace(
                        ' ',
                        '-'
                      )}`}
                      item={item}
                      key={item.key}
                      isMultipleSelection={isMultipleSelection}
                    />
                  </div>
                ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
