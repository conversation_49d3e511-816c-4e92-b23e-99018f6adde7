@import '@getheroes/frontend/config/styles/components/form-basis';

.c-input-phone {
  @extend %form-appearance;
  display: flex;

  input[type='tel'] {
    background: transparent;
    padding-left: 5px;
    &:focus {
      outline: none;
      border: 0;
    }
  }

  .PhoneInputCountry {
    display: flex;
    flex-direction: row;
    justify-items: center;
    align-items: center;
    .PhoneInputCountrySelect {
      cursor: pointer !important;
      text-overflow: ellipsis;
      width: 70px;
    }
    .PhoneInputCountryIcon {
      width: 30px;
      margin-left: 5px;
      margin-right: 5px;
    }
  }

  .PhoneInputInput {
    width: 100%;
  }
}
