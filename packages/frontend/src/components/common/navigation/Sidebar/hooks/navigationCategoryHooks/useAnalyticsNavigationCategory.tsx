import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { NavigationEvents } from '@getheroes/frontend/hooks'
import { Icon } from '@getheroes/ui'
import type { NavigationListType } from '@internals/components/common/navigation/Sidebar/types'
import { NavigationListCategoryEnum } from '@internals/components/common/navigation/Sidebar/types'
import { privateRoutes } from '@internals/hooks/useRoute'
import { idReferentials } from '@internals/utils/idReferentials'

/**
 * Returns the WhatsNew navigation category and its items for the sidebar
 */
export const useAnalyticsNavigationCategory = (): NavigationListType => {
  const { t } = useTranslation('sidebar')

  return useMemo(
    () => ({
      title: t('Analytics'),
      icon: <Icon name={'GraphUp'} color={'base-placeholder'} />,
      category: NavigationListCategoryEnum.ANALYZE,
      dataTestId: idReferentials.sidebar.categories.analytics,
      route: privateRoutes.analytic.path,
      trackEvent: NavigationEvents.NAV_BAR_BUTTON_CLICK_ANALYTICS,
    }),
    [t]
  )
}
