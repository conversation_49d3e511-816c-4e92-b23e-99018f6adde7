import { clsx } from 'clsx'
import { useMemo, type ReactNode } from 'react'

import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import { DEFAULT_ICON_SIZE } from '@internals/utils/design'

import type { ButtonVariant } from './Button-export'

export type LabelProps = {
  dataTestId?: string
  isLoading?: boolean
  variant?: ButtonVariant
  formIdToSubmit?: string
  startIcon?: ReactNode
  disabled?: boolean
  hasData?: boolean
  children: ReactNode
  endIcon?: ReactNode
}

export const ButtonLabel = ({
  dataTestId,
  isLoading,
  variant,
  formIdToSubmit,
  startIcon,
  disabled,
  hasData,
  children,
  endIcon,
}: LabelProps) => {
  /* Memos */

  const spinnerColor = useMemo(() => {
    if (variant === 'primary' || variant === 'danger') {
      return 'var(--neutral)'
    }

    return undefined
  }, [variant])

  if (isLoading) {
    return <Spinner size={DEFAULT_ICON_SIZE} color={spinnerColor} />
  } else if (formIdToSubmit) {
    return (
      <div
        className={clsx('flex items-center gap-x-1 h-full')}
        data-testid={dataTestId}
      >
        {!isLoading && startIcon && <span>{startIcon}</span>}
        <label
          className={clsx('h-full p-2', {
            'cursor-not-allowed': disabled,
            'text-textSubtle': !hasData,
          })}
          htmlFor={formIdToSubmit}
        >
          {children}
        </label>
        {!isLoading && endIcon && <span>{endIcon}</span>}
      </div>
    )
  } else {
    return (
      <div
        className={clsx('flex items-center gap-x-1 label-s', {
          'text-textSubtle': !hasData,
        })}
        data-testid={dataTestId}
      >
        {!isLoading && startIcon && <span>{startIcon}</span>}
        {children}
        {!isLoading && endIcon && <span>{endIcon}</span>}
      </div>
    )
  }
}
