import type { Meta, StoryObj } from '@storybook/react'

import { UserNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/UserNameCell'

import type { User } from '@getheroes/shared'

const meta: Meta<typeof UserNameCell> = {
  component: UserNameCell,
  argTypes: {
    user: {
      name: 'user',
      description: 'User object',
      control: {
        type: 'object',
      },
    },
  },
}

export default meta

type Story = StoryObj<typeof UserNameCell>

export const Default: Story = {
  args: {
    user: {
      firstName: 'John',
      lastName: 'Doe',
    } as User,
  },
}
