import { useTranslation } from 'react-i18next'

import type { Contact, ExternalContact } from '@getheroes/shared'
import { Badge, Typography } from '@getheroes/ui'
import { GdprOptOutWarningIcon } from '@getheroes/ui-business'
import type { CellRendererParams } from '@internals/components/common/dataDisplay/Grid/types/grid'

import { WrapperCell } from './WrapperCell'

type NameCellProps = {
  contact: Contact | ExternalContact
} & CellRendererParams

export const ContactNameCell = ({ contact, ...props }: NameCellProps) => {
  const { t } = useTranslation('lead')

  const hasEnrichmentNewDataAvailable = Boolean(
    contact?.enrichmentNewDataAvailable
  )

  return (
    <WrapperCell data={contact} {...props}>
      <div className="flex flex-col gap-1">
        <div className="flex gap-1 heading-xs truncate">
          <span>{contact?.firstName}</span>
          <span>{contact?.lastName}</span>
        </div>
        {hasEnrichmentNewDataAvailable && (
          <div className={'flex items-center gap-1'}>
            <Badge color={'blue'} />
            <Typography
              variant="label"
              size="xs"
              color="decorative-blue"
              weight="semi-bold"
            >
              {t('New data')}
            </Typography>
          </div>
        )}
      </div>
      {contact?.gdprOptOut && <GdprOptOutWarningIcon />}
    </WrapperCell>
  )
}
