import { clsx } from 'clsx'
import type { ReactNode } from 'react'

import { Tooltip } from '@getheroes/ui'

import { WrapperCell } from './WrapperCell'

type CommonCellProps = {
  text: string | null | undefined
  children: ReactNode
  className?: string
}
export const TooltipWrapperCell: (props: CommonCellProps) => JSX.Element = ({
  text,
  children,
  className,
}) => (
  <Tooltip content={text || undefined}>
    <WrapperCell data={text === '' ? null : text} className={clsx(className)}>
      {children}
    </WrapperCell>
  </Tooltip>
)
