import type {
  ApplyColumnStateParams,
  ColDef,
  ColumnMovedEvent,
  ColumnPinnedEvent,
  ColumnResizedEvent,
  ColumnVisibleEvent,
  GetRowIdFunc,
  GetRowIdParams,
  GridReadyEvent,
  GridSizeChangedEvent,
  IRowNode,
  IsFullWidthRowParams,
  IsRowSelectable,
  RowClassRules,
  RowClickedEvent,
  RowHeightParams,
  RowSelectedEvent,
  SelectionChangedEvent,
  Theme,
} from 'ag-grid-community'
import {
  CellStyleModule,
  ClientSideRowModelApiModule,
  ClientSideRowModelModule,
  ColumnApiModule,
  ColumnAutoSizeModule,
  ModuleRegistry,
  PinnedRowModule,
  RenderApiModule,
  RowApiModule,
  RowSelectionModule,
  RowStyleModule,
  ScrollApiModule,
  themeQuartz,
} from 'ag-grid-community'
import {
  CellSelectionModule,
  ClipboardModule,
  ColumnMenuModule,
  GroupFilterModule,
  RowGroupingModule,
  RowGroupingPanelModule,
} from 'ag-grid-enterprise'
import { AgGridReact } from 'ag-grid-react'
import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import isUndefined from 'lodash/isUndefined'
import type { LegacyRef, PropsWithChildren } from 'react'
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import useDeepCompareEffect from 'use-deep-compare-effect'

import { selectCurrentFiltersApi } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { ToastType, useToast } from '@getheroes/frontend/hooks'
import { FeatureLimit, LIMIT_SELECTION_BY_REQUEST } from '@getheroes/shared'
import { SkeletonList } from '@internals/components/common/feedback/Skeletons/SkeletonList/SkeletonList'
import type { FeatureGatewayOverlayProps } from '@internals/features/featureGateway/components/FeatureGatewayOverlay/FeatureGatewayOverlay'
import { FeatureGatewayOverlay } from '@internals/features/featureGateway/components/FeatureGatewayOverlay/FeatureGatewayOverlay'
import { useHasAccessToFeature } from '@internals/features/featureGateway/hooks/useHasAccessToFeature'
import { useKeyDown } from '@internals/hooks/useKeyDown'
import { useTypedSelector } from '@internals/store/store'
import type { PaginationMetaType } from '@internals/types/api/pagination'
import { LocalStorageType } from '@internals/types/localStorage'

import { useGridContext } from './GridProvider-export'
import './table-ag.scss'

import { FullWidthCellRenderer } from './components/FullWidthCellRenderer/FullWidthCellRenderer'
import { GridHeader } from './components/GridHeader/GridHeader'
import { GridPagination } from './components/GridPagination/GridPagination'
import { GridSearchBar } from './components/GridSearchBar/GridSearchBar'
import { GroupRowInnerRenderer } from './components/GroupRowInnerRenderer/GroupRowInnerRenderer'
import type {
  AutoSizeStrategyType,
  GridColDef,
  ModeSelectionType,
  RowData,
  RowGroupType,
} from './types/grid'
import {
  APPLY_ORDER,
  AutoSizeStrategyEnum,
  COMMON_WRAPPER_CELL_CLASSNAME,
  GRID_DEFAULT_COLUMN_MAX_WIDTH,
  GRID_DEFAULT_ROW_GROUP_HEIGHT,
  GRID_DEFAULT_ROW_HEIGHT,
  GridDefaultOptions,
  GridRowSelectionEnum,
} from './types/grid'
import {
  createLimitRowSelectable,
  createRowSelection,
  createSelectionColumnDef,
} from './utils/selection'
import { zeliqGridThemeParams } from './utils/theme'

ModuleRegistry.registerModules([
  ClientSideRowModelApiModule,
  RowSelectionModule,
  RowApiModule,
  ClientSideRowModelModule,
  RowStyleModule,
  ColumnApiModule,
  ScrollApiModule,
  RenderApiModule,
  ColumnAutoSizeModule,
  CellStyleModule,
  PinnedRowModule,
  //enterprise
  GroupFilterModule,
  RowGroupingPanelModule,
  RowGroupingModule,
  ColumnMenuModule,
  CellSelectionModule,
  ClipboardModule,
  // ValidationModule /* Development Only */,
  // AllCommunityModule,
  // AllEnterpriseModule,
])

const zeliqGridTheme = themeQuartz.withParams(zeliqGridThemeParams)

/* Render FeatureGatewayOverlay if featureGatewayOverlayProps are defined */
const ConditionalFeatureGatewayOverlay = ({
  children,
  featureGatewayOverlayProps,
  isShowLimitReached,
}: PropsWithChildren<{
  featureGatewayOverlayProps?: FeatureGatewayOverlayProps
  isShowLimitReached: boolean
}>) => {
  return featureGatewayOverlayProps ? (
    <FeatureGatewayOverlay
      {...featureGatewayOverlayProps}
      isActive={isShowLimitReached}
    >
      {children}
    </FeatureGatewayOverlay>
  ) : (
    children
  )
}

const LoaderGrid = () => (
  <div
    className={
      'flex w-full h-full justify-center items-center flex-col bg-white'
    }
  >
    <SkeletonList
      className={'w-full h-full'}
      itemsNumber={40}
      hasBorder={false}
    />
  </div>
)

type GridProps = {
  name: string
  data: RowData[]
  columns: GridColDef[]
  hasSearchBar?: boolean
  isLoading?: boolean
  checkboxSelection?: boolean
  rowSelection?: GridRowSelectionEnum
  rowSelectionIds?: string[]
  shouldSelectCurrentPageOnly?: boolean
  hasPagination?: boolean
  paginationMeta?: PaginationMetaType | undefined
  defaultPageSize?: GridDefaultOptions
  defaultCurrentPage?: GridDefaultOptions
  isRowSelectable?: IsRowSelectable
  onRowClicked?: (data: RowData) => void
  hasWindowResize?: boolean // Only for AutoSizeStrategyEnum.FILL_GRID_WIDTH type
  className?: string
  isRowsEvenOdd?: boolean
  rowsEvenOdd?: number[]
  autoSizeColumnStrategy?: AutoSizeStrategyType
  suppressDragLeaveHidesColumns?: boolean
  columnHoverHighlight?: boolean
  saveToLocalStorage?: boolean
  isDebug?: boolean
  customColDef?: ColDef
  suppressRowClickSelection?: boolean
  shouldLimitSelection?: boolean
  overrideLimitSelection?: number
  shouldDisplayLastPageBtn?: boolean
  shouldDisplayFirstPageBtn?: boolean
  forceSizeColumnsToFit?: boolean
  forceRefresh?: boolean
  handleRowSelected?: (event: RowSelectedEvent) => void
  handleSelectionChanged?: (event: SelectionChangedEvent) => void
  modeSelection?: ModeSelectionType
  featureGatewayOverlayProps?: FeatureGatewayOverlayProps
  shouldDisplaySkeleton?: boolean
  getRowId?: GetRowIdFunc
  disablePaddingRightPagination?: boolean
  hasVirtualization?: boolean
}
/**
 * ```
 * AG GRID
 * ```
 * Zeliq use AG Grid for all tables
 *
 * ```Official Ag Grid Doc``` https://www.ag-grid.com/react-data-grid/
 * ```
 * Props
 * ```
 * @argument `name` - required - Unique name for the grid
 * @argument `isLoading` https://www.ag-grid.com/react-data-grid/grid-options/#reference-overlays
 * @argument `data` https://www.ag-grid.com/react-data-grid/grid-options/#reference-clientRowModel
 * @argument `columns` https://www.ag-grid.com/react-data-grid/column-definitions/
 * @argument `checkboxSelection` https://www.ag-grid.com/react-data-grid/row-selection/
 * @argument `rowSelection` `GridRowSelectionEnum.SINGLE, GridRowSelectionEnum.MULTIPLE` https://www.ag-grid.com/react-data-grid/row-selection/
 * @argument `isRowSelectable` https://www.ag-grid.com/react-data-grid/grid-options/#reference-selection-isRowSelectable
 * @argument `defaultCurrentPage` default: `GridDefaultOptions.PAGE`
 * @argument `defaultPageSize` default: `GridDefaultOptions.PAGE_SIZE`
 * @argument `isRowsEvenOdd` Stripe rows
 * @argument `rowsEvenOdd` Specific index for stripe rows
 * @argument `hasWindowResize` Only for type `AutoSizeStrategyEnum.FILL_SET_CONTENTS`
 * @argument `autoSizeColumnStrategy` https://www.ag-grid.com/react-data-grid/column-sizing/#auto-sizing-columns
 * @argument `customColDef` override the defaulColDef (column definition)
 * @argument `suppressRowClickSelection` https://www.ag-grid.com/angular-data-grid/grid-options/#reference-selection-suppressRowClickSelection
 * @argument `shouldLimitSelection` Limit the max selection
 * @argument `forceSizeColumnsToFit` Force refresh column size to fit - Only for FILL_GRID_WIDTH (all columns is visible)
 * @argument `getRowId` Override the default getRowId - (row) => row.data.externalId
 * @argument ``
 * ```
 * Community edition
 * ```
 * @doc ```Column Resize``` https://www.ag-grid.com/react-data-grid/column-sizing/
 * @doc ```Column Pinning``` https://www.ag-grid.com/react-data-grid/column-pinning/
 * @doc ```Column Menu``` https://www.ag-grid.com/react-data-grid/column-menu/
 * @doc ```Column Headers``` https://www.ag-grid.com/react-data-grid/column-headers/
 * @doc ```Column Groups``` https://www.ag-grid.com/react-data-grid/column-groups/
 * @doc ```Pagination``` https://www.ag-grid.com/react-data-grid/row-pagination/
 * @doc ```Selection``` https://www.ag-grid.com/react-data-grid/row-selection/
 * @doc ```Sorting``` https://www.ag-grid.com/react-data-grid/row-sorting/
 * @doc ```View Refresh```https://www.ag-grid.com/react-data-grid/view-refresh/
 * @doc ```RowsIds``` https://www.ag-grid.com/react-data-grid/row-ids/
 * @doc ```Column Spanning Complex Example``` https://www.ag-grid.com/react-data-grid/column-spanning/#column-spanning-complex-example
 * @doc ```Client-Side Data - Transaction Updates``` https://www.ag-grid.com/react-data-grid/data-update-transactions/
 * @doc
 * ```
 * Enterprise edition
 * ```
 * @doc ```Column Groups``` https://www.ag-grid.com/react-data-grid/grouping/
 * @doc
 * */

export const Grid = memo(
  forwardRef(
    (
      {
        name,
        data = [],
        columns = [],
        hasSearchBar = false,
        isLoading = false,
        checkboxSelection = false,
        rowSelectionIds,
        shouldSelectCurrentPageOnly = false,
        rowSelection = GridRowSelectionEnum.MULTIPLE,
        paginationMeta,
        hasPagination = true,
        defaultPageSize,
        defaultCurrentPage,
        isRowSelectable, // = (params: IRowNode<any>) => !!params.data && params.data.status.includes('hunting') //? Show checkbox selectable, useMemo is best pratice
        onRowClicked,
        hasWindowResize,
        className,
        isRowsEvenOdd,
        rowsEvenOdd,
        autoSizeColumnStrategy,
        suppressDragLeaveHidesColumns = false,
        columnHoverHighlight,
        saveToLocalStorage = false,
        isDebug = true,
        customColDef,
        suppressRowClickSelection = true,
        shouldLimitSelection = true,
        overrideLimitSelection,
        shouldDisplayLastPageBtn = true,
        shouldDisplayFirstPageBtn = true,
        forceSizeColumnsToFit = false,
        forceRefresh = false,
        handleRowSelected,
        handleSelectionChanged,
        modeSelection = 'default',
        featureGatewayOverlayProps,
        shouldDisplaySkeleton = false,
        getRowId,
        disablePaddingRightPagination = false,
        hasVirtualization = false,
      }: GridProps,
      ref: LegacyRef<AgGridReact>
    ) => {
      const { t } = useTranslation('table')
      const { createToast } = useToast()
      const gridRef = useRef<AgGridReact<RowData>>(null)
      const currentFiltersApi = useTypedSelector(selectCurrentFiltersApi)
      const {
        setPageSize,
        setCurrentPage,
        setGridApi,
        setSelections,
        setRowSelected,
        setDefaultSelections,
        setSelection,
        setColumnState,
        setResetSearchText,
        setLimitSelection,
        setIsSelectedAllPage,
        isSelectedAllPage,
        setModeSelection,
        setName,
        reset,
        gridApi,
        currentPage,
        selections,
        defaultSelections,
        limitSelection,
        rowGroups,
        sort,
        pageSize,
      } = useGridContext()

      // Handle feature-gateway: add 1 to pagination and blur current page if limit is reached
      const { accessLimit } = useHasAccessToFeature(
        FeatureLimit.MAX_SEARCH_REQUESTS
      )
      const [isShowLimitReached, setIsShowLimitReached] = useState(
        currentPage * pageSize > (accessLimit || Infinity)
      )
      const isFeatureGatewayActive =
        isShowLimitReached && !!featureGatewayOverlayProps
      useEffect(() => {
        // clear the limit reached warning if filters have changed
        if (isLoading && isShowLimitReached) {
          setIsShowLimitReached(false)
        }
      }, [isLoading, isShowLimitReached])

      useKeyDown(() => {
        setSelections([])
        gridApi.deselectAll()
      }, ['Escape'])

      const getDefaultRowId = useMemo<GetRowIdFunc>(
        () => (params: GetRowIdParams<RowData>) => {
          if (getRowId) {
            return getRowId(params)
          }

          return params.data.id
        },
        [getRowId]
      )

      // Default params columns
      const defaultColDef = useMemo<GridColDef>(() => {
        const colExtraParams: ColDef = {}
        if (
          autoSizeColumnStrategy?.type ===
          AutoSizeStrategyEnum.FILL_SET_CONTENTS
        ) {
          colExtraParams.maxWidth = GRID_DEFAULT_COLUMN_MAX_WIDTH
        }
        return {
          minWidth: 50,
          sortable: false,
          filter: false,
          hide: false,
          enableCellChangeFlash: true,
          cellClass: COMMON_WRAPPER_CELL_CLASSNAME,
          // If you want to set a custom property to header component, you need to set the headerComponent property on the column definition GridColDef.
          headerComponent: GridHeader,
          cellDataType: true,
          ...colExtraParams,
          ...customColDef,
        }
      }, [autoSizeColumnStrategy?.type, customColDef])

      const loadingOverlayComponent = useMemo(() => LoaderGrid, [])
      const rowClassRules = useMemo<RowClassRules>(() => {
        return {
          'ag-specific-row-odd': params => {
            if (isRowsEvenOdd && rowsEvenOdd && params.node.rowIndex) {
              return rowsEvenOdd.includes(params.node.rowIndex)
            } else {
              return false
            }
          },
          'grid-row-odd': params => {
            if (isRowsEvenOdd && !rowsEvenOdd && params.node.rowIndex) {
              return params.node.rowIndex % 2 === 1
            } else {
              return false
            }
          },
        }
      }, [isRowsEvenOdd, rowsEvenOdd])

      const noRowsOverlayComponent = useMemo(
        () => t('There are currently no data.'),
        [] // eslint-disable-line react-hooks/exhaustive-deps
      )
      const autoSizeStrategy = useMemo<AutoSizeStrategyType>(() => {
        return autoSizeColumnStrategy
      }, [autoSizeColumnStrategy])

      /**************/
      /* USE EFFECT */
      /**************/

      useEffect(() => {
        if (isUndefined(name)) {
          throw new Error('Grid name is required')
        }
        setName(name)
      }, [name]) // eslint-disable-line react-hooks/exhaustive-deps

      const updateRowGroupState = useCallback(
        (rowGroups: RowGroupType[]) => {
          if (!isEmpty(gridApi)) {
            gridApi.applyColumnState({
              state: rowGroups.map(column => ({
                colId: column.colId,
                rowGroup: true,
              })),
              applyOrder: APPLY_ORDER,
            } as ApplyColumnStateParams)
          }
          return
        },
        [gridApi]
      )

      useEffect(() => {
        let initTimeout: NodeJS.Timeout | null = null
        if (!isLoading && rowGroups) {
          // Important to update row group state after the data is loaded
          initTimeout = setTimeout(async () => {
            updateRowGroupState(rowGroups)
          }, 0)
        }
        return () => {
          if (initTimeout !== null) {
            clearTimeout(initTimeout)
          }
        }
      }, [
        isLoading,
        rowGroups,
        currentPage,
        sort,
        updateRowGroupState,
        currentFiltersApi,
      ])

      useEffect(() => {
        if (gridRef?.current) {
          gridRef?.current?.api?.setGridOption('loading', isLoading)
        }
      }, [isLoading])

      useEffect(() => {
        if (defaultCurrentPage) {
          setCurrentPage(defaultCurrentPage)
        }
      }, [defaultCurrentPage, setCurrentPage])

      useEffect(() => {
        if (defaultPageSize) {
          setPageSize(defaultPageSize)
        }
      }, [defaultPageSize, setPageSize])

      useEffect(() => {
        if (rowSelectionIds) {
          setDefaultSelections(rowSelectionIds)
        }
      }, [setDefaultSelections]) // eslint-disable-line react-hooks/exhaustive-deps

      useEffect(() => {
        let initTimeout: NodeJS.Timeout | null = null
        if (
          !isEmpty(gridApi) &&
          autoSizeColumnStrategy?.type === AutoSizeStrategyEnum.FILL_GRID_WIDTH
        ) {
          initTimeout = setTimeout(() => {
            gridApi.sizeColumnsToFit()
          }, 300)
        }
        return () => {
          if (initTimeout !== null) {
            clearTimeout(initTimeout)
          }
        }
      }, [autoSizeColumnStrategy?.type, gridApi])

      useEffect(() => {
        let initTimeout: NodeJS.Timeout | null = null
        if (!shouldSelectCurrentPageOnly) {
          // Set selection checkbox rows when the page change
          if (!isLoading) {
            initTimeout = setTimeout(() => {
              if (!isEmpty(gridApi)) {
                const nodesToSelect: IRowNode[] = []
                if (!isEmpty(defaultSelections)) {
                  // ! WIP  Default selection on all pages
                  // const test = uniq([...defaultSelections, ...selections])
                  // gridApi.forEachLeafNode((node: IRowNode) => {
                  //   if (node.data && test.includes(node.data.id)) {
                  //     nodesToSelect.push(node)
                  //   }
                  // })
                  // gridApi.setNodesSelected({
                  //   nodes: nodesToSelect,
                  //   newValue: true,
                  // })
                } else {
                  // Keep selection on all pages
                  gridApi.forEachLeafNode((node: IRowNode) => {
                    if (node.data && selections.includes(node.data.id)) {
                      nodesToSelect.push(node)
                    }
                  })
                  gridApi.setNodesSelected({
                    nodes: nodesToSelect,
                    newValue: true,
                  })
                }
              }
            }, 200)
          }
        }

        if (shouldSelectCurrentPageOnly) {
          // Select current page only
          setSelections([])
        }
        return () => {
          if (initTimeout !== null) {
            clearTimeout(initTimeout)
          }
        }
      }, [currentPage, isLoading, defaultSelections]) // eslint-disable-line react-hooks/exhaustive-deps

      const handleResizeWindow = useCallback(
        (params: GridSizeChangedEvent) => {
          if (
            autoSizeColumnStrategy &&
            autoSizeColumnStrategy.type === AutoSizeStrategyEnum.FILL_GRID_WIDTH
          ) {
            params.api.sizeColumnsToFit()
          }
        },
        [autoSizeColumnStrategy]
      )

      /****************/
      /* USE CALLBACK */
      /****************/

      const [isInit, setIsInit] = useState(false)

      const onGridPreDestroyed = useCallback(() => {
        reset()
      }, [reset])

      const onGridReady = useCallback(
        (params: GridReadyEvent) => {
          if (isEmpty(gridApi)) {
            setGridApi(params?.api)
            // TODO: reset search on tab change lead/company
            // Quick fix for reset search bar when user clicks on other tab
            // In the future, we can add props if we want to keep the search text or not
            setResetSearchText()
            params.api.setGridOption('rowData', data)
            // Localstorage init
            if (saveToLocalStorage && !isInit) {
              if (
                isUndefined(
                  JSON.parse(
                    localStorage.getItem(LocalStorageType.GRID_CACHE) as string
                  )?.[name]
                )
              ) {
                // Init column state to local storage
                setColumnState(params?.api?.getColumnState(), name)
              }
            }
            setIsInit(true)
          }
        },
        [gridApi, setGridApi, saveToLocalStorage, isInit, name, setColumnState]
      )

      useDeepCompareEffect(() => {
        if (!isEmpty(gridApi)) {
          gridApi.setGridOption('rowData', data)
          gridApi.refreshCells({
            force: true,
            suppressFlash: true,
          })
        }
      }, [data, gridApi])

      useEffect(() => {
        setLimitSelection(shouldLimitSelection)
      }, [setLimitSelection, shouldLimitSelection])

      useEffect(() => {
        setModeSelection(modeSelection)
      }, [setModeSelection, modeSelection])

      // Force resize column to fit
      useEffect(() => {
        let initTimeout: NodeJS.Timeout | null = null
        if (
          !isInit ||
          isEmpty(gridApi) ||
          !forceSizeColumnsToFit ||
          autoSizeColumnStrategy?.type !== AutoSizeStrategyEnum.FILL_GRID_WIDTH
        ) {
          return
        }
        initTimeout = setTimeout(
          () => {
            gridApi.sizeColumnsToFit()
          },
          !isLoading ? 200 : 0
        )
        return () => {
          if (initTimeout !== null) {
            clearTimeout(initTimeout)
          }
        }
      }, [
        isInit,
        gridApi,
        isLoading,
        forceSizeColumnsToFit,
        autoSizeColumnStrategy?.type,
      ])

      const limitSelectionNumber =
        overrideLimitSelection || LIMIT_SELECTION_BY_REQUEST

      const onRowSelected = useCallback(
        (event: RowSelectedEvent) => {
          if (
            (!shouldSelectCurrentPageOnly &&
              event.source !== 'rowDataChanged' &&
              event.source !== 'api') ||
            (shouldSelectCurrentPageOnly && event.source === 'rowClicked')
          ) {
            setSelection(
              event.node.data,
              event?.node?.isSelected() || false,
              event.source,
              event.type,
              limitSelectionNumber
            )
          }
          handleRowSelected && handleRowSelected(event)
        },
        [
          handleRowSelected,
          setSelection,
          shouldSelectCurrentPageOnly,
          limitSelectionNumber,
        ]
      )

      const limitRowSelectable = useMemo(
        () =>
          createLimitRowSelectable(
            selections,
            limitSelectionNumber,
            limitSelection,
            isRowSelectable
          ),
        [selections, limitSelectionNumber, limitSelection, isRowSelectable]
      )

      const rowSelectionMemo = useMemo(() => {
        if (!checkboxSelection) {
          return undefined
        }
        return createRowSelection(
          rowSelection,
          checkboxSelection,
          suppressRowClickSelection,
          limitRowSelectable
        )
      }, [
        rowSelection,
        checkboxSelection,
        suppressRowClickSelection,
        limitRowSelectable,
      ])

      const selectionColumnDef = useMemo(() => createSelectionColumnDef(), [])

      useEffect(() => {
        if (selections.length === limitSelectionNumber && limitSelection) {
          // Limit selection
          createToast({
            type: ToastType.MAIN,
            message: t(`The max selection is {{limit}}`, {
              ns: 'table',
              limit: limitSelectionNumber,
            }) as string,
          })
        }
      }, [
        createToast,
        gridApi,
        limitSelection,
        limitSelectionNumber,
        selections,
        selections.length,
        t,
      ])

      const [totalColumnsShowed, setTotalColumnsShowed] = useState(0)
      const onColumnChanged = useCallback(
        (
          event:
            | ColumnVisibleEvent
            | ColumnMovedEvent
            | ColumnPinnedEvent
            | ColumnResizedEvent
        ) => {
          if (saveToLocalStorage) {
            // TODO: Suppress or not virtualization column
            setTotalColumnsShowed(
              gridRef?.current?.api
                ?.getColumns()
                ?.filter(column => column?.isVisible()).length || 0
            )
            setColumnState(event.api.getColumnState(), name)
          }
        },
        [saveToLocalStorage, setColumnState, name]
      )

      // GROUP ROW RENDERER
      const groupRowRendererParams = useMemo(() => {
        return {
          innerRenderer: GroupRowInnerRenderer,
          suppressCount: true,
        }
      }, [])

      const getRowHeight = useCallback(
        (params: RowHeightParams): number | undefined | null => {
          return params.node.group
            ? GRID_DEFAULT_ROW_GROUP_HEIGHT
            : GRID_DEFAULT_ROW_HEIGHT
        },
        []
      )

      const isFullWidthRow = useCallback((params: IsFullWidthRowParams) => {
        return params?.rowNode?.data?.fullWidth || false
      }, [])

      const resetSelectionsHideBanner = useCallback(() => {
        if (modeSelection === 'banner' && isSelectedAllPage) {
          setIsSelectedAllPage(false)
          gridApi.deselectAll()
          setSelections([])
        }
      }, [
        gridApi,
        isSelectedAllPage,
        modeSelection,
        setIsSelectedAllPage,
        setSelections,
      ])

      const theme = useMemo<Theme | 'legacy'>(() => {
        return zeliqGridTheme
      }, [])

      return (
        <div className={'flex flex-col h-full gap-2'}>
          {hasSearchBar && <GridSearchBar className={'mb-2'} />}
          <div className={clsx('c-grid h-full', className)}>
            <ConditionalFeatureGatewayOverlay
              featureGatewayOverlayProps={featureGatewayOverlayProps}
              isShowLimitReached={isShowLimitReached}
            >
              <AgGridReact
                suppressColumnVirtualisation={!hasVirtualization}
                suppressRowVirtualisation={!hasVirtualization}
                // rowBuffer={pageSize * 2} // Enable this when column sequence activity is fixed
                theme={theme}
                ref={gridRef}
                getRowId={getDefaultRowId}
                onGridSizeChanged={handleResizeWindow}
                onGridReady={onGridReady}
                onGridPreDestroyed={onGridPreDestroyed}
                // Size Column
                autoSizeStrategy={autoSizeStrategy} // https://www.ag-grid.com/react-data-grid/column-sizing/#top
                // colResizeDefault={'shift'} // https://www.ag-grid.com/react-data-grid/column-sizing/#shift-resizing
                onColumnMoved={onColumnChanged}
                onColumnVisible={onColumnChanged}
                onColumnPinned={onColumnChanged}
                onColumnResized={onColumnChanged}
                // Columns
                columnDefs={columns}
                defaultColDef={defaultColDef}
                // Rows
                rowModelType={'clientSide'}
                // Boolean
                enableCellTextSelection={false}
                suppressCellFocus={true}
                // Selection
                rowSelection={rowSelectionMemo}
                selectionColumnDef={selectionColumnDef}
                rowHeight={GRID_DEFAULT_ROW_HEIGHT}
                getRowHeight={getRowHeight}
                onRowSelected={onRowSelected}
                onSelectionChanged={(event: SelectionChangedEvent) => {
                  handleSelectionChanged && handleSelectionChanged(event)
                  if (shouldSelectCurrentPageOnly) {
                    setSelections(event.api.getSelectedRows())
                  }
                }}
                // On row clicked
                onRowClicked={(event: RowClickedEvent<RowData>) => {
                  if (event?.event?.defaultPrevented) {
                    // Block row click when the event is defaultPrevented, On your cell component, if you use a onClick, add event.preventDefault()
                    // If you use suppressRowClickSelection at false, Use button ref click with event.stopPropagation() example: useExternalContactsTableColumns.tsx
                    return null
                  }
                  if (isUndefined(event.data)) {
                    return
                  }
                  setRowSelected &&
                    setRowSelected(
                      event.data.id as string,
                      event.rowIndex as number
                    )
                  onRowClicked && onRowClicked(event.data)
                }}
                // Groups
                groupDisplayType={'groupRows'}
                groupRowRendererParams={groupRowRendererParams}
                groupDefaultExpanded={1}
                // Full width row
                fullWidthCellRenderer={FullWidthCellRenderer}
                isFullWidthRow={isFullWidthRow}
                // No rows
                noRowsOverlayComponent={noRowsOverlayComponent}
                overlayNoRowsTemplate={t('No results') as string}
                // Loading
                loadingOverlayComponent={loadingOverlayComponent}
                loading={isLoading}
                // Row class
                rowClassRules={rowClassRules}
                // Hover
                columnHoverHighlight={columnHoverHighlight}
                // Suppress
                suppressDragLeaveHidesColumns={suppressDragLeaveHidesColumns} // If `true`, when you drag a column out of the grid (e.g. to the group zone) the column is not hidden.
                // DEBUG
              />
            </ConditionalFeatureGatewayOverlay>
          </div>
          {hasPagination && paginationMeta && (
            <GridPagination
              shouldDisplayLastPageBtn={shouldDisplayLastPageBtn}
              shouldDisplayFirstPageBtn={shouldDisplayFirstPageBtn}
              pageCount={paginationMeta.totalPages}
              totalItems={paginationMeta.totalItems}
              defaultCurrentPage={
                pageSize === accessLimit && isShowLimitReached
                  ? -1
                  : defaultCurrentPage
              }
              formattedCurrentPage={
                isFeatureGatewayActive ? String(currentPage + 1) : undefined
              }
              gotoPage={pageIndex => setCurrentPage(pageIndex)}
              nextPage={() => {
                resetSelectionsHideBanner()
                if (currentPage * pageSize >= (accessLimit || Infinity)) {
                  setIsShowLimitReached(true)
                } else {
                  setCurrentPage(currentPage + 1)
                }
              }}
              previousPage={() => {
                resetSelectionsHideBanner()
                if (isShowLimitReached) {
                  setIsShowLimitReached(false)
                } else {
                  setCurrentPage(currentPage - 1)
                }
              }}
              setPageSize={(pageSize: number) => {
                resetSelectionsHideBanner()
                setCurrentPage(defaultCurrentPage ?? GridDefaultOptions.PAGE)
                setPageSize(pageSize)
                setIsShowLimitReached(false)
              }}
              isShowLimitedPerPage={true}
              displayTotalItems={true}
              className={clsx({ 'mr-16': !disablePaddingRightPagination })}
              shouldDisplaySkeleton={shouldDisplaySkeleton}
            />
          )}
        </div>
      )
    }
  )
)
