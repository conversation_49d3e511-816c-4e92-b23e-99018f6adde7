export const EmptyCollection = ({
  width = '20.625rem',
  height = '18.875rem',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 330 302"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="160.762" cy="150.762" r="150.762" fill="#D6F8D1" />
      <rect
        opacity="0.8"
        x="82"
        y="54"
        width="156.617"
        height="194.286"
        rx="17.5645"
        fill="white"
      />
      <rect
        opacity="0.6"
        x="99"
        y="78"
        width="122"
        height="17"
        rx="8.5"
        fill="#E4EBE8"
      />
      <rect
        opacity="0.6"
        x="99"
        y="106"
        width="122"
        height="17"
        rx="8.5"
        fill="#E4EBE8"
      />
      <rect
        opacity="0.6"
        x="99"
        y="160"
        width="122"
        height="17"
        rx="8.5"
        fill="#E4EBE8"
      />
      <rect
        opacity="0.6"
        x="99"
        y="188"
        width="122"
        height="17"
        rx="8.5"
        fill="#E4EBE8"
      />
      <g clipPath="url(#clip0_476_157359)">
        <path
          d="M325.807 230.35C322.189 223.257 316.46 217.378 309.906 212.806C303.179 208.113 295.489 204.772 287.523 202.695C279.411 200.58 270.929 199.75 262.551 200.253C266.156 198.469 269.84 196.834 273.59 195.366C281.735 192.176 290.186 189.729 298.796 188.083C302.932 187.292 307.101 186.691 311.294 186.283C311.921 186.222 312.458 185.803 312.458 185.143C312.458 184.574 311.924 183.942 311.294 184.003C302.32 184.876 293.439 186.583 284.8 189.122C276.136 191.668 267.713 195.032 259.695 199.131C258.597 199.692 257.509 200.27 256.425 200.859C249.65 201.8 243.018 203.627 236.762 206.372C229.156 209.71 222.201 214.456 216.482 220.404C210.901 226.211 206.49 233.038 203.451 240.433C200.467 247.699 198.877 255.563 199.007 263.403C199.072 267.221 199.554 271.04 200.442 274.758C200.783 276.186 203.029 275.581 202.686 274.152C200.929 266.811 200.912 259.124 202.422 251.741C203.959 244.228 206.991 237.054 211.238 230.625C215.466 224.226 220.928 218.579 227.314 214.214C234.169 209.529 241.928 206.242 250.047 204.344C250.224 204.303 250.404 204.266 250.583 204.225C245.9 207.076 241.389 210.192 237.074 213.559C230.029 219.055 223.436 225.198 217.992 232.247C212.706 239.088 208.537 246.978 206.946 255.472C205.412 263.672 206.093 272.352 209.721 279.955C213.207 287.259 219.482 292.795 226.86 296.212C234.338 299.674 242.606 301.192 250.812 301.741C259.651 302.332 268.616 301.915 277.338 300.376C286.108 298.831 294.71 296.026 302.44 291.641C309.732 287.504 316.048 281.97 320.85 275.172C325.449 268.663 328.583 261.131 329.64 253.269C330.696 245.419 329.423 237.441 325.805 230.347L325.807 230.35ZM324.146 264.563C321.174 271.621 316.465 278.004 310.661 283.086C304.37 288.591 296.709 292.562 288.743 295.186C280.467 297.912 271.772 299.265 263.062 299.611C254.891 299.936 246.579 299.551 238.592 297.737C231.101 296.034 223.728 292.982 218.152 287.664C212.355 282.137 209.212 274.569 208.615 266.724C207.989 258.499 209.979 250.359 213.784 243.043C217.734 235.449 223.364 228.832 229.617 222.953C235.919 217.029 242.852 211.75 250.209 207.145C252.494 205.714 254.823 204.347 257.19 203.047C263.062 202.272 269.028 202.185 274.928 202.754C283.043 203.538 291.12 205.602 298.519 208.975C305.636 212.222 312.251 216.666 317.49 222.429C322.624 228.076 326.193 234.987 327.317 242.498C328.433 249.953 327.059 257.64 324.146 264.562V264.563Z"
          fill="#4C8676"
        />
        <path
          d="M276.993 242.515C280.197 238.807 283.408 235.105 286.622 231.405C287.067 230.892 287.513 230.381 287.958 229.868C288.4 229.36 288.506 228.553 287.958 228.06C287.471 227.622 286.585 227.517 286.112 228.06C282.815 231.852 279.522 235.645 276.237 239.446C273.49 242.624 270.747 245.806 268.013 248.994C267.398 249.713 266.784 250.434 266.168 251.153C265.85 250.781 265.535 250.41 265.217 250.038C262.547 246.923 259.87 243.814 257.186 240.707C253.982 236.999 250.772 233.297 247.557 229.597C247.112 229.084 246.667 228.573 246.222 228.06C245.778 227.549 244.827 227.58 244.376 228.06C243.867 228.604 243.903 229.323 244.376 229.868C247.674 233.66 250.967 237.453 254.252 241.254C256.998 244.432 259.741 247.614 262.475 250.802C263.142 251.579 263.806 252.359 264.47 253.138C263.197 254.627 261.922 256.113 260.653 257.604C258.238 260.44 255.828 263.28 253.421 266.122C250.761 269.263 248.104 272.405 245.446 275.547C245.089 275.97 244.731 276.392 244.375 276.815C244.148 277.082 243.992 277.359 243.992 277.718C243.992 278.032 244.135 278.406 244.375 278.621C244.855 279.054 245.754 279.172 246.22 278.621C248.925 275.423 251.629 272.224 254.336 269.028C256.772 266.15 259.211 263.275 261.654 260.402C263.155 258.638 264.662 256.878 266.166 255.118C267.389 256.548 268.613 257.977 269.833 259.409C272.247 262.244 274.657 265.085 277.065 267.927C279.724 271.067 282.382 274.21 285.039 277.352C285.396 277.774 285.754 278.197 286.111 278.619C286.547 279.136 287.509 279.095 287.956 278.619C288.471 278.071 288.422 277.364 287.956 276.812C285.251 273.613 282.547 270.415 279.84 267.218C277.404 264.34 274.965 261.465 272.522 258.592C270.97 256.769 269.413 254.951 267.859 253.131C268.226 252.702 268.592 252.271 268.959 251.841C271.629 248.726 274.306 245.616 276.99 242.51L276.993 242.515Z"
          fill="#4C8676"
        />
      </g>
      <path
        d="M25.4116 13.0721C26.4006 20.9195 26.317 29.704 32.0713 35.6118C34.5178 38.1236 37.8802 39.9622 41.1807 40.8444C42.7345 41.2597 45.0786 40.9556 46.3043 39.8803C47.2149 39.0815 45.6133 38.7233 45.0313 38.7918C40.9666 39.2706 36.5325 40.3317 33.3915 43.2547C29.4909 46.8845 28.5901 52.2239 27.6779 57.343C27.2227 59.8976 27.1278 62.3888 27.1278 64.978C27.1278 65.984 27.2195 62.9671 27.2012 61.9613C27.1691 60.2001 26.8239 58.4906 26.3797 56.7987C25.4498 53.2565 24.2111 49.7975 22.2431 46.7456C20.7072 44.364 18.7991 42.3422 16.5955 40.6578C15.0271 39.459 13.108 38.0304 11.1386 37.7188C10.0224 37.5996 6.11169 36.6925 5.6419 38.7918C5.36003 40.0514 8.64784 42.1817 12.1801 39.8803C17.2201 36.7927 20.7189 31.2928 23.0939 25.7998C25.0486 21.2787 25.4116 16.7024 25.4116 11.8125"
        stroke="#326E5E"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M50.0567 60.4652C51.3071 56.7857 51.7434 53.1484 51.0542 49.2381C50.887 48.2896 50.4648 46.3494 49.4113 45.957C48.3123 45.5477 48.38 50.5751 48.3698 50.8553C48.179 56.0724 49.0234 60.9331 51.751 65.3868C54.1674 69.3325 58.2318 71.1785 62.5547 71.5679C64.2612 71.7217 65.9908 71.5681 67.6888 71.3813C68.5963 71.2815 69.6192 71.2285 70.4906 70.8993C70.7092 70.8167 71.3323 70.5475 71.004 70.1995C70.4006 69.5599 69.2479 69.5586 68.4809 69.6942C66.9282 69.9685 65.4438 70.8084 64.0729 71.5835C61.8715 72.8281 59.8202 74.3113 57.8386 75.9142C53.7258 79.2409 50.6078 83.0226 49.2206 88.4242C48.6476 90.6553 48.0207 93.4083 48.3991 95.7482C48.4561 96.1007 48.6904 96.4411 48.8685 95.9193C49.4356 94.2573 49.1516 91.9208 48.9345 90.228C48.2106 84.5818 45.5426 78.5217 41.2333 74.9734C37.6705 72.0398 32.8613 70.6645 28.4053 71.5057C27.5154 71.6737 26.2909 72.0027 25.9409 73.0296C25.6926 73.758 25.8265 74.791 26.3223 75.3777C27.7957 77.1213 31.0888 76.296 32.8866 75.8364C34.4371 75.4401 35.9805 75.0038 37.3827 74.1726C39.4101 72.9707 46.9426 71.5679 50.4052 59.3686"
        stroke="#326E5E"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M71.0624 37.1434C71.493 39.2987 72.3473 40.5678 73.9889 41.964M73.9889 41.964C76.0261 43.6966 78.7412 45.0271 81.404 44.8874C85.109 44.8874 84.6941 42.3294 83.6379 41.6996C82.5817 41.0698 78.8508 42.5004 73.7028 45.9603C68.2849 49.6016 64.7254 59.3333 68.686 58.8046C69.2689 59.1444 70.2295 56.9386 70.1236 54.9171C69.9903 52.3728 68.5879 49.6924 67.3365 47.4687C65.9148 44.9424 63.8717 42.8268 61.6669 41.0698C60.6618 40.2689 59.5807 39.4808 58.3444 39.1494C57.772 38.996 56.5545 38.6942 56.0414 39.1805C55.1988 39.979 55.5934 41.0562 56.3274 41.6996C60.9729 45.7717 68.8138 41.4342 70.9597 36.3348C72.0309 33.7895 72.8712 30.5343 71.7812 27.8601C71.5043 27.1809 71.029 26.0637 70.1236 26.274C69.0812 26.516 68.6684 28.6517 68.4587 29.5084C67.3515 34.0304 70.064 39.3163 73.9889 41.964Z"
        stroke="#326E5E"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <defs>
        <clipPath id="clip0_476_157359">
          <rect
            width="131"
            height="118"
            fill="white"
            transform="translate(199 184)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
