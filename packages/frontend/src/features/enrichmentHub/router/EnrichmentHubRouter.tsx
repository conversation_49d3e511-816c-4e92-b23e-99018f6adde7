import { Navigate, Route, Routes } from 'react-router-dom'

import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { EnrichmentHubV2DetailPage } from '@internals/features/enrichmentHub/pages/EnrichmentHubV2/EnrichmentHubV2DetailPage'
import { EnrichmentHubV2HomePage } from '@internals/features/enrichmentHub/pages/EnrichmentHubV2/EnrichmentHubV2HomePage'
import { EnrichmentHubV2Page } from '@internals/features/enrichmentHub/pages/EnrichmentHubV2/EnrichmentHubV2Page'
import { EnrichmentHubProvider } from '@internals/features/enrichmentHub/providers/EnrichmentHubProvider'
import { useCurrentUser } from '@internals/hooks/useCurrentUser'
import { useRoute } from '@internals/hooks/useRoute'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'

export const EnrichmentHubRouter = () => {
  const { privateRoutes } = useRoute()
  const { currentUser } = useCurrentUser()

  return (
    <EnrichmentHubProvider>
      <Routes>
        <Route
          path={'/'}
          element={
            <ErrorBoundary fallbackRender={() => <ErrorPage />}>
              {currentUser?.userSettings?.isEnrichmentHubHomePageVisited ? (
                <EnrichmentHubV2HomePage />
              ) : (
                <EnrichmentHubV2Page />
              )}
            </ErrorBoundary>
          }
        />

        <Route
          path={privateRoutes.enrichmentHubV2HubId.path}
          element={
            <ErrorBoundary fallbackRender={() => <ErrorPage />}>
              <GridProvider>
                <EnrichmentHubV2DetailPage />
              </GridProvider>
            </ErrorBoundary>
          }
        />

        <Route
          path={'/*'}
          element={<Navigate replace to={privateRoutes.enrichmentHubV2.path} />}
        />
      </Routes>
    </EnrichmentHubProvider>
  )
}
