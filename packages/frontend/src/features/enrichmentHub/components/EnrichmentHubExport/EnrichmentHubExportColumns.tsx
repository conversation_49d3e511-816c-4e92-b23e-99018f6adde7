import { useEffect, useMemo, useState } from 'react'
import { AccordionItem } from 'react-accessible-accordion'
import { useTranslation } from 'react-i18next'

import type {
  LeadExportableFieldDTO,
  LeadFieldExportCategoryEnum,
} from '@getheroes/shared'
import type { OptionType } from '@getheroes/ui'
import {
  Accordion,
  CardV2,
  Divider,
  Radio,
  Select,
  Tag,
  Typography,
} from '@getheroes/ui'
import { LeadExportColumnGroup } from '@internals/features/lead-export/components/LeadExportColumnGroup'
import { LeadExportOption } from '@internals/features/lead-export/enum/LeadExportOption.enum'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'
import { capitalizeFirstLetter } from '@internals/utils/string'

interface EnrichmentHubExportColumnsProps {
  categories: LeadFieldExportCategoryEnum[]
  fields: LeadExportableFieldDTO[]
  isChoice?: boolean
  isExpanded: boolean
}

export const EnrichmentHubExportColumns = ({
  categories,
  fields,
  isChoice = false,
  isExpanded,
}: EnrichmentHubExportColumnsProps) => {
  const { t } = useTranslation('export')
  const { isOriginalColumnsChoice, setIsOriginalColumnsChoice } =
    useLeadExport()

  const defaultFields = fields?.filter(field => field.isDefault)

  const options: OptionType<string>[] = useMemo(
    () => [
      {
        value: LeadExportOption.DEFAULT,
        label: t('Default ({{number}})', {
          number: defaultFields?.length || 0,
        }),
      },
      {
        value: LeadExportOption.ALL,
        label: t('All ({{number}})', { number: fields?.length || 0 }),
      },
      { value: LeadExportOption.PERSONALIZED, label: t('Personalized') },
    ],
    [t, defaultFields?.length, fields?.length]
  )

  const [value, setValue] = useState<string>(options[0].value)

  useEffect(() => {
    if (fields) {
      setValue(options[0].value)
    }
  }, [fields, options])

  const handleSelectChange = (newValue: any) => {
    setIsOriginalColumnsChoice(false)
    setValue(newValue as string)
  }

  return (
    <CardV2 padding="p-4">
      <div className={'flex flex-col gap-4'}>
        <div className="flex justify-between items-center">
          {isChoice ? (
            <Radio
              label={t('Choose fields from Zeliq to export')}
              id="add-fields"
              name="fields-to-export"
              onChange={() => setIsOriginalColumnsChoice(false)}
              checked={!isOriginalColumnsChoice}
            />
          ) : (
            <Typography variant="label" size="s" weight="medium">
              {t('Choose fields from Zeliq to export')}
            </Typography>
          )}
          <Select
            name="select-fields"
            options={options}
            onChange={handleSelectChange}
            value={value}
          />
        </div>
        <Divider />
        <AccordionItem uuid="item-2" dangerouslySetExpanded={isExpanded}>
          <Accordion.Header>{t('View details')}</Accordion.Header>
          <Accordion.Body>
            {value === LeadExportOption.DEFAULT && (
              <div className="flex flex-wrap gap-1.5">
                {defaultFields?.map(({ id }) => (
                  <Tag
                    key={id}
                    color="brand-secondary"
                    label={capitalizeFirstLetter(t(id))}
                  />
                ))}
              </div>
            )}

            {value === LeadExportOption.ALL && (
              <div className="flex flex-wrap gap-1.5">
                {fields?.map(({ id }) => (
                  <Tag
                    key={id}
                    color="brand-secondary"
                    label={capitalizeFirstLetter(t(id))}
                  />
                ))}
              </div>
            )}

            {value === LeadExportOption.PERSONALIZED && (
              <div className="flex flex-col gap-6">
                {categories?.map(exportableCategory => {
                  return (
                    <LeadExportColumnGroup
                      key={exportableCategory}
                      name={t(exportableCategory)}
                      fields={fields?.filter(
                        exportableFields =>
                          exportableFields.category === exportableCategory
                      )}
                    />
                  )
                })}
              </div>
            )}
          </Accordion.Body>
        </AccordionItem>
      </div>
    </CardV2>
  )
}
