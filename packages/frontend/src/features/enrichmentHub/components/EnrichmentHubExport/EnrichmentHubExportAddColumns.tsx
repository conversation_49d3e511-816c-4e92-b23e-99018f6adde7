import { AccordionItem } from 'react-accessible-accordion'
import { useTranslation } from 'react-i18next'

import type { MinimalFlatfileField } from '@getheroes/shared'
import { Accordion, CardV2, Divider, Radio, Tag, Tooltip } from '@getheroes/ui'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'

interface LeadExportAddColumnsProps {
  filename: string
  fields: MinimalFlatfileField[]
  isExpanded: boolean
}

export const EnrichmentHubExportAddColumns = ({
  filename,
  fields,
  isExpanded,
}: LeadExportAddColumnsProps) => {
  const { t } = useTranslation('export')
  const { isOriginalColumnsChoice, setIsOriginalColumnsChoice } =
    useLeadExport()

  const fieldsEnrichedByZeliq: string[] = [
    'email',
    'phone',
    'emailsDeliverability',
    'linkedinUrl',
    'companyDomain',
    'city',
    'country',
    'jobTitle',
  ]

  return (
    <CardV2 padding="p-4">
      <div className={'flex flex-col gap-4'}>
        <Radio
          label={t('Select all {{count}} fields from {{filename}}', {
            count: fields.length,
            filename,
          })}
          id="add-columns"
          name="fields-to-export"
          onChange={() => setIsOriginalColumnsChoice(true)}
          checked={isOriginalColumnsChoice}
        />
        <Divider />
        <AccordionItem uuid="item-1" dangerouslySetExpanded={isExpanded}>
          <Accordion.Header>{t('View details')}</Accordion.Header>
          <Accordion.Body>
            <div className="flex flex-wrap gap-1.5">
              {fields.map(field =>
                fieldsEnrichedByZeliq.includes(field.key) ? (
                  <Tooltip
                    key={field.key}
                    content={t('Data enriched by Zeliq')}
                  >
                    <Tag color={'brand-secondary'} label={field.label} />
                  </Tooltip>
                ) : (
                  <Tag key={field.key} color={'grey'} label={field.label} />
                )
              )}
            </div>
          </Accordion.Body>
        </AccordionItem>
      </div>
    </CardV2>
  )
}
