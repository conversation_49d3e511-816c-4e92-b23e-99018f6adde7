import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type {
  LeadExportableFieldDTO,
  MinimalFlatfileField,
} from '@getheroes/shared'
import i18n from '@internals/config/i18n'
import { EnrichmentHubExportFilters } from '@internals/features/enrichmentHub/components/EnrichmentHubExport/EnrichmentHubExportFilters.enum'
import type { Fields } from '@internals/features/lead/types/csvType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { LeadExportOption } from '@internals/features/lead-export/enum/LeadExportOption.enum'
import type { LeadFilterApiType } from '@internals/models/lead'

export const getEnrichmentEnrichmentHubExportFilters = (
  enrichmentHubExportFilters: EnrichmentHubExportFilters
): LeadFilterApiType[] => {
  switch (enrichmentHubExportFilters) {
    case EnrichmentHubExportFilters.PHONE_OR_EMAIL:
      return [
        {
          field: ExclusiveContactLeadFieldEnum.HAS_PHONE_OR_EMAIL,
          operator: OperatorFilterType.EQUALS,
          value: 'true',
        },
      ]
    case EnrichmentHubExportFilters.EMAIL_ONLY:
      return [
        {
          field: ExclusiveContactLeadFieldEnum.HAS_EMAILS_VALID,
          operator: OperatorFilterType.EQUALS,
          value: 'true',
        },
      ]
    case EnrichmentHubExportFilters.PHONE_ONLY:
      return [
        {
          field: ExclusiveContactLeadFieldEnum.PHONES,
          operator: OperatorFilterType.NOT_EMPTY,
        },
      ]
    case EnrichmentHubExportFilters.NO_DATA:
      return [
        {
          field: ExclusiveContactLeadFieldEnum.EMAILS,
          operator: OperatorFilterType.EMPTY,
        },
        {
          field: ExclusiveContactLeadFieldEnum.PHONES,
          operator: OperatorFilterType.EMPTY,
        },
      ]
    case EnrichmentHubExportFilters.NO_FILTERS:
    default:
      return []
  }
}

export const getEnrichmentHubExportFields = (
  choice: LeadExportOption | 'original-column',
  exportableFields: LeadExportableFieldDTO[] | MinimalFlatfileField[],
  selectedFields: string[]
): Fields => {
  let exportable: Fields = []

  switch (choice) {
    case 'original-column':
      exportable = (exportableFields as MinimalFlatfileField[]).map(
        ({ key }) => ({
          id: key,
          name: i18n.t(key, { ns: 'export' }),
        })
      )
      break
    case LeadExportOption.DEFAULT:
      exportable = (exportableFields as LeadExportableFieldDTO[])
        .filter(({ isDefault }) => isDefault)
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        }))
      break
    case LeadExportOption.ALL:
      exportable = (exportableFields as LeadExportableFieldDTO[]).map(
        ({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        })
      )
      break
    case LeadExportOption.PERSONALIZED:
      exportable = (exportableFields as LeadExportableFieldDTO[])
        .filter(({ id }) => selectedFields.includes(id))
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        }))
      break
  }

  return exportable
}
