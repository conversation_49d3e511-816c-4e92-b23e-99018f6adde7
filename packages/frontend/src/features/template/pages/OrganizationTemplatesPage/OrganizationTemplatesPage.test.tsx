import { screen } from '@testing-library/react'
import { setupServer } from 'msw/node'
import { afterAll, beforeAll, describe, expect, it } from 'vitest'

import type { AuthState } from '@getheroes/frontend/config/store/slices/authSlice'
import { OrganizationTemplatesPage } from '@internals/features/template/pages/OrganizationTemplatesPage/OrganizationTemplatesPage'
import { mockedOrganizations } from '@internals/mocks/organization'
import { getOrganizationTemplatesHandler } from '@internals/mocks/server/handlers/templateHandlers'
import { mockedUser } from '@internals/mocks/user'
import { renderWithProviders } from '@internals/utils/test/renderWithProviders'

describe('[Integration tests] - Templates list tests with success behaviour', () => {
  const mockedServer = setupServer(getOrganizationTemplatesHandler.status200)

  beforeAll(async () => {
    mockedServer.listen()
  })
  afterAll(() => {
    mockedServer.close()
  })

  it.skip('Should display a list of 10 organization templates', async () => {
    // Arrange --------
    const mockedStore = {
      auth: {
        currentUser: mockedUser,
        isConnected: true,
      } as AuthState,
      organization: {
        currentUserOrganizations: mockedOrganizations,
        currentUserOrganization: mockedOrganizations[0],
      },
    }

    // Act --------
    renderWithProviders(<OrganizationTemplatesPage />, {
      preloadedState: mockedStore,
    })

    // Assert --------
    expect(screen.findByTestId('template-10')).toBeTruthy()
  })
})

describe('[Integration tests] - Organization templates page tests with error behaviour', () => {
  const mockedServer = setupServer(getOrganizationTemplatesHandler.status401)

  beforeAll(async () => {
    mockedServer.listen()
  })
  afterAll(() => {
    mockedServer.close()
  })

  it.skip('Should display the correct error when fetch templates failed', async () => {
    // Arrange --------
    const mockedStore = {
      auth: {
        currentUser: mockedUser,
        isConnected: true,
      } as AuthState,
      organization: {
        currentUserOrganizations: mockedOrganizations,
        currentUserOrganization: mockedOrganizations[0],
      },
    }

    // Act --------
    renderWithProviders(<OrganizationTemplatesPage />, {
      preloadedState: mockedStore,
    })

    // Assert --------
    expect(screen.findByText('page with error')).toBeTruthy()
  })
})
