import { Tag } from '@getheroes/ui'
import type {
  OrganizationTemplate,
  RecommendedTemplate,
} from '@internals/models/template'

interface SnippetListItemProps {
  snippet: OrganizationTemplate | RecommendedTemplate
}

export const SnippetListItem = ({ snippet }: SnippetListItemProps) => {
  return (
    <div className="p-2 w-full hover:bg-[--grey100] cursor-pointer">
      <Tag icon={'Code'} label={snippet.name} color={'grey'} />
      <div
        className="line-clamp-1 mt-2 text-xs"
        dangerouslySetInnerHTML={{
          __html: snippet.content,
        }}
      />
    </div>
  )
}
