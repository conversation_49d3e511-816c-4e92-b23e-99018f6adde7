import { useCallback, useMemo } from 'react'

import { OrganizationServiceSubscriptionPlan } from '@getheroes/shared'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'

export const useCurrentPlan = () => {
  const subscriptions = useSubscriptions()
  const currentPlan =
    subscriptions?.currentPlan as OrganizationServiceSubscriptionPlan

  const getAuthorizationForPlans = useCallback(
    (authorizedPlans: OrganizationServiceSubscriptionPlan[]) =>
      currentPlan ? authorizedPlans.includes(currentPlan) : false,
    [currentPlan]
  )

  return useMemo(
    () => ({
      currentPlan,
      getAuthorizationForPlans,
      isFreePlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.FREE,
      ]),
      isStarterPlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.STARTER,
      ]),
      isEssentialPlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.ESSENTIAL,
      ]),
      isFreeOrStarterPlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.FREE,
        OrganizationServiceSubscriptionPlan.STARTER,
      ]),
      isEssentialAdvancedEnterprisePlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.ENTERPRISE,
        OrganizationServiceSubscriptionPlan.ESSENTIAL,
        OrganizationServiceSubscriptionPlan.ADVANCED,
      ]),
      isAdvancedEnterprisePlan: getAuthorizationForPlans([
        OrganizationServiceSubscriptionPlan.ENTERPRISE,
        OrganizationServiceSubscriptionPlan.ADVANCED,
      ]),
    }),
    [currentPlan, getAuthorizationForPlans]
  )
}
