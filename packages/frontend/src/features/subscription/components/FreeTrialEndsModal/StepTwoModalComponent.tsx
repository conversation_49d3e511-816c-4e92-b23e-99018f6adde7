import { clsx } from 'clsx'
import { useTranslation } from 'react-i18next'

import { GreenCircle } from '@internals/assets/svg/icons/GreenCircleSVG'
import { capitalizeFirstLetter } from '@internals/utils/string'

import { FrequencyCard } from './FrequencyCard'

const TitleCard = ({ label }: { label: string }) => (
  <div
    className={'flex flex-col bg-backgroundAccent rounded-3xl p-6 space-y-3'}
  >
    <div
      className={`c-price-table__title relative overflow-hidden flex flex-col justify-center items-center min-w-[352px] min-h-[120px] bg-neutral rounded-3xl py-5`}
    >
      <div className="text-center heading-xl text-textAccent">{label}</div>

      <div className={clsx('absolute left-[350px] top-[-150px]')}>
        <GreenCircle
          height={250}
          width={250}
          fillColor={'var(--backgroundSuccess)'}
        />
      </div>
    </div>
  </div>
)

export const StepTwoModalComponent = () => {
  const { t } = useTranslation('subscription')

  return (
    <div className={'flex flex-col'}>
      <TitleCard label={capitalizeFirstLetter(t('Starter'))} />

      <div className={'flex w-full justify-center space-x-4 my-4'}>
        <FrequencyCard />
        {/*<FrequencyCard isAnnually />*/}
      </div>
    </div>
  )
}
