import React from 'react'
import { useTranslation } from 'react-i18next'

import { Button, Icon } from '@getheroes/ui'
import { ConversionButton } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton'
import {
  ConversionButtonSize,
  ConversionButtonVariant,
} from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { idReferentials } from '@internals/utils/idReferentials'

interface ModalFooterProps {
  handleClose: () => void
  handleSubmit: () => void
}

export const ModalFooter = ({
  handleSubmit,
  handleClose,
}: ModalFooterProps) => {
  const { t } = useTranslation('subscription')

  return (
    <div className={'flex justify-end items-center gap-1'}>
      <Button
        variant={'tertiary-outlined'}
        dataTestId={
          idReferentials.subscription.managePlanPage.creditsCard
            .confirmationModal.cancelButton
        }
        onClick={handleClose}
      >
        {t('Cancel')}
      </Button>

      <ConversionButton
        size={ConversionButtonSize.SMALL}
        onClick={handleSubmit}
        dataTestId={
          idReferentials.subscription.managePlanPage.creditsCard
            .confirmationModal.submitButton
        }
        startIcon={
          <Icon
            name={'DollarCircle'}
            size={'small'}
            color={'decorative-pink'}
          />
        }
        variant={ConversionButtonVariant.PLG}
      >
        {t('Pay & upgrade')}
      </ConversionButton>
    </div>
  )
}
