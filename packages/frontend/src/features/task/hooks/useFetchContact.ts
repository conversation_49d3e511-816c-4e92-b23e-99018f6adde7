import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useGetContactQuery } from '@internals/features/lead/api/contactApi'
import { useTypedSelector } from '@internals/store/store'

export const useFetchContact = (contactId: string | undefined = '') => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  return useGetContactQuery({
    contactId: contactId,
    organizationId,
  })
}
