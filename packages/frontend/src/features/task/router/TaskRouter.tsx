import { Navigate, Outlet, Route, Routes } from 'react-router-dom'

import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { TaskPage } from '@internals/features/task/pages/TaskPage/TaskPage'
import { privateRoutes } from '@internals/hooks/useRoute'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'

export const TaskRouter = () => {
  return (
    <Routes>
      <Route
        path={'/'}
        element={
          <ErrorBoundary fallbackRender={() => <ErrorPage />}>
            <TaskPage />
          </ErrorBoundary>
        }
      >
        <Route path={privateRoutes.taskId.path} element={<Outlet />} />
      </Route>

      <Route
        path={'/*'}
        element={<Navigate replace to={privateRoutes.tasks.path} />}
      />
    </Routes>
  )
}
