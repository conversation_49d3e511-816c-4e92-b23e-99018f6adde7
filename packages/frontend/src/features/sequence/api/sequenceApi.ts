import { isEqual } from 'lodash'

import { api } from '@getheroes/frontend/config/api'
import type { SequenceActivities } from '@getheroes/frontend/types'
import { SequenceContactReviewStatus } from '@getheroes/shared'
import type { ContactSequenceStepActivity } from '@internals/features/lead/types/contactSequenceStepActivity'
import type {
  AddLeadsAndAssignStepResponseType,
  LeadBySequence,
  LeadsBySequenceArgs,
  Sequence,
  SequenceLeadErrors,
  SequenceMacroKpisType,
  SequencesContactSteps,
  SequenceStatus,
  ContactSequenceSteps,
} from '@internals/features/sequence/types/sequence'
import type {
  ListPagination,
  PaginationArgsType,
} from '@internals/types/api/pagination'
import { getPaginationQueryString } from '@internals/utils/getPaginationQueryString'

import { mergeApiCacheForPagination } from '../util/mergeApiCacheForPagination'

export const sequenceApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      addSequence: builder.mutation<
        Sequence,
        { organizationId: string; sequence: Partial<Sequence> }
      >({
        query: ({ organizationId, sequence }) => {
          return {
            url: `${organizationId}/sequences`,
            method: 'POST',
            body: {
              ...sequence,
            },
          }
        },
        invalidatesTags: [
          { type: 'FeatureGateway', id: 'GET_USAGE' },
          { type: 'Sequence', id: 'LIST' },
        ],
      }),
      updateSequence: builder.mutation<
        Sequence,
        {
          organizationId: string
          sequence: Partial<Sequence> & Pick<Sequence, 'id'>
        }
      >({
        query: ({ organizationId, sequence: { id, ...attributes } }) => {
          return {
            url: `${organizationId}/sequences/${id}`,
            method: 'PATCH',
            body: {
              ...attributes,
            },
          }
        },
        invalidatesTags: (_, __, args) => [
          { type: 'Sequence', id: `SEQUENCE_${args.sequence.id}` },
          { type: 'Sequence', id: 'LIST' },
        ],
      }),
      updateContactSequence: builder.mutation<
        LeadBySequence,
        {
          organizationId: string
          sequenceId: string
          contactId: string
        } & Partial<LeadBySequence>
      >({
        query: ({ organizationId, sequenceId, contactId, ...rest }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/${contactId}`,
            method: 'PATCH',
            body: {
              ...rest,
            },
          }
        },
        invalidatesTags: (_, __, args) => [
          { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}_LEADS_LIST` },
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_ACTIVITIES_${args.sequenceId}`,
          },
        ],
      }),
      deleteSequence: builder.mutation<
        Sequence,
        {
          organizationId: string
          sequenceId: string
        }
      >({
        query: ({ organizationId, sequenceId }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}`,
            method: 'DELETE',
          }
        },
        invalidatesTags: () => [
          { type: 'FeatureGateway', id: 'GET_USAGE' },
          { type: 'Sequence', id: 'LIST' },
        ],
      }),
      getSequence: builder.query<
        Sequence,
        { organizationId: string; sequenceId: string }
      >({
        query: ({ organizationId, sequenceId }) =>
          `${organizationId}/sequences/${sequenceId}`,
        providesTags: (_, __, args) => [
          { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}` },
        ],
      }),
      getListSequence: builder.query<
        ListPagination<Sequence>,
        {
          organizationId: string
          statuses?: SequenceStatus[]
          pagination?: PaginationArgsType
          name?: string
        }
      >({
        query: ({ organizationId, statuses, pagination, name }) => {
          const params = new URLSearchParams()

          statuses?.forEach(status => {
            params.append('status', status)
          })

          if (pagination?.page) {
            params.append('page', pagination.page.toString())
          }

          if (pagination?.limitPerPage) {
            params.append('limitPerPage', pagination.limitPerPage.toString())
          }

          if (pagination?.order) {
            params.append('order', pagination.order)
          }

          if (name) {
            params.append('name', name)
          }

          return {
            url: `${organizationId}/sequences`,
            method: 'GET',
            params,
          }
        },
        serializeQueryArgs: ({ endpointName, queryArgs }) => {
          return `${endpointName}-${queryArgs.statuses ?? 'all'}-${queryArgs.name ?? 'all'}-${queryArgs.pagination?.limitPerPage ?? 'all'}-${queryArgs.pagination?.order ?? 'all'}`
        },
        merge: mergeApiCacheForPagination,
        forceRefetch({ currentArg, previousArg }) {
          return isEqual(currentArg, previousArg) === false
        },
        providesTags: [{ type: 'Sequence', id: 'LIST' }],
      }),
      getLeadsBySequence: builder.query<
        LeadsBySequenceWithPagination,
        LeadsBySequenceArgs
      >({
        query: ({
          organizationId,
          sequenceId,
          pagination,
          filters,
          orderBy,
        }) => {
          const params = new URLSearchParams()
          if (pagination?.page) {
            params.append('page', pagination.page.toString())
          }
          if (pagination?.limitPerPage) {
            params.append('limitPerPage', pagination.limitPerPage.toString())
          }
          if (filters?.phones !== undefined) {
            params.append('phones', filters.phones.toString())
          }
          if (filters?.emails !== undefined) {
            params.append('emails', filters.emails.toString())
          }
          if (filters?.reviewStatus) {
            params.append('reviewStatus', filters.reviewStatus)
          }
          if (orderBy) {
            params.append('orderBy', orderBy)
          }

          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts`,
            method: 'GET',
            params,
          }
        },
        providesTags: (_, __, args) => [
          { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}_LEADS_LIST` },
          {
            type: 'Sequence',
            id: `SEQUENCE_${args.sequenceId}_LEADS_LIST_${args.filters?.reviewStatus ?? 'all'}`,
          },
          { type: 'Sequence', id: 'MY_CONTACTS_LEADS' },
        ],
      }),
      getSequenceLeadsErrors: builder.query<
        ListPagination<SequenceLeadErrors>,
        LeadsBySequenceArgs
      >({
        query: ({ organizationId, sequenceId, pagination }) => {
          const params = new URLSearchParams()
          if (pagination?.page) {
            params.append('page', pagination.page.toString())
          }
          if (pagination?.limitPerPage) {
            params.append('limitPerPage', pagination.limitPerPage.toString())
          }

          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/errors`,
            method: 'GET',
            params,
          }
        },
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_${args.sequenceId}_LEADS_ERRORS_LIST`,
          },
        ],
      }),
      getSequenceContactErrors: builder.query<
        SequenceLeadErrors,
        { organizationId: string; sequenceId: string; contactId: string }
      >({
        query: ({ organizationId, sequenceId, contactId }) => {
          return `${organizationId}/sequences/${sequenceId}/contacts/${contactId}/errors`
        },
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_ERRORS_${args.sequenceId}_${args.contactId}`,
          },
        ],
      }),

      deleteLeadsBySequence: builder.mutation<
        void,
        {
          organizationId: string
          sequenceId: string
          contacts: string[]
          deleteAllContactsWithErrors: boolean
        }
      >({
        query: ({
          organizationId,
          sequenceId,
          contacts,
          deleteAllContactsWithErrors,
        }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/delete`,
            method: 'POST',
            body: { contacts, deleteAllContactsWithErrors },
          }
        },
        invalidatesTags: (_, __, args) => {
          const sequenceContactStepsTags = args.contacts.map(contactId => {
            return {
              type: 'Sequence' as const,
              id: `SEQUENCE_CONTACT_STEP_${contactId}`,
            }
          })

          const sequenceContactStepsActivitiesTags = args.contacts.map(
            contactId => {
              return {
                type: 'Sequence' as const,
                id: `SEQUENCE_CONTACT_STEP_ACTIVITIES_${args.sequenceId}_${contactId}`,
              }
            }
          )

          const contactSequenceStepsTags = args.contacts.map(contactId => {
            return {
              type: 'Sequence' as const,
              id: `CONTACT_${contactId}_SEQUENCES_STEP`,
            }
          })

          return [
            ...sequenceContactStepsTags,
            ...sequenceContactStepsActivitiesTags,
            ...contactSequenceStepsTags,
            {
              type: 'Sequence',
              id: `SEQUENCE_${args.sequenceId}_LEADS_ERRORS_LIST`,
            },
            { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}_LEADS_LIST` },
            { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}` },
          ]
        },
      }),

      /** ADD LEADS & ASSIGN **/
      addLeadsAndAssignStep: builder.mutation<
        AddLeadsAndAssignStepResponseType,
        {
          organizationId: string
          sequenceId: string
          contacts: string[]
          deleteContactAlreadyExist: boolean
          views: string[]
          onlyMine: boolean
        }
      >({
        query: ({
          organizationId,
          sequenceId,
          contacts,
          deleteContactAlreadyExist,
          views,
          onlyMine,
        }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts`,
            method: 'POST',
            body: {
              contacts,
              deleteContactAlreadyExist: false,
              views,
              onlyMine,
            },
          }
        },
        invalidatesTags: (_, __, args) => {
          const contacts = args.contacts || []
          const sequenceContactStepsTags = contacts.map(contactId => {
            return {
              type: 'Sequence' as const,
              id: `SEQUENCE_CONTACT_STEP_${contactId}`,
            }
          })

          return [
            { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}_LEADS_LIST` },
            { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}` },
            ...sequenceContactStepsTags,
          ]
        },
      }),
      getMacroKpis: builder.query<
        SequenceMacroKpisType,
        { organizationId: string }
      >({
        query: ({ organizationId }) => `${organizationId}/sequences/kpis`,
      }),
      filterContactsNotInOtherSequence: builder.mutation<
        string[],
        { organizationId: string; contactIds: string[] }
      >({
        query: ({ organizationId, contactIds }) => {
          return {
            url: `${organizationId}/sequences/contacts/filtred`,
            method: 'POST',
            body: { contactIds },
          }
        },
      }),
      consultSequenceActivitesByContactIds: builder.query<
        ListPagination<ContactSequenceStepActivity>,
        {
          organizationId: string
          sequenceId: string
          contactIds: string[]
        }
      >({
        query: ({ organizationId, sequenceId, contactIds }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/steps/past-activities`,
            method: 'POST',
            body: { contactIds },
          }
        },
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_STEP_ACTIVITIES_${args.sequenceId}_${args.contactIds.join(
              '_'
            )}`,
          },
        ],
      }),
      getSequencesContactSteps: builder.query<
        ListPagination<SequencesContactSteps>,
        { organizationId: string; contactId: string }
      >({
        query: ({ organizationId, contactId }) =>
          `${organizationId}/sequences/contacts/${contactId}/steps`,
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_STEP_${args.contactId}`,
          },
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_STEP_LIST`,
          },
        ],
      }),
      updateSequencesContactStep: builder.mutation<
        {
          content: {
            subject?: string
            body: string
          }
        },
        {
          params: {
            organizationId: string
            sequenceId: string
            contactId: string
            stepId: string
          }
          body: {
            content: {
              subject?: string
              body: string
            }
          }
        }
      >({
        query: ({ params, body }) => {
          const { organizationId, sequenceId, contactId, stepId } = params
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/${contactId}/steps/${stepId}`,
            method: 'PUT',
            body,
          }
        },
        invalidatesTags: (_, error, args) => {
          if (error) {
            return []
          }
          return [
            {
              type: 'Sequence',
              id: `SEQUENCE_CONTACT_STEP_${args.params.contactId}`,
            },
            {
              type: 'Sequence',
              id: `SEQUENCE_${args.params.sequenceId}_LEADS_LIST_${SequenceContactReviewStatus.PENDING}`,
            },
            {
              type: 'Sequence',
              id: `SEQUENCE_CONTACT_ACTIVITIES_${args.params.contactId}`,
            },
          ]
        },
      }),
      getSequencesContactStepsActivities: builder.query<
        ListPagination<ContactSequenceStepActivity>,
        {
          organizationId: string
          contactId: string
          sequenceId: string
        } & PaginationArgsType
      >({
        query: ({ organizationId, contactId, sequenceId, ...qs }) => {
          const params = getPaginationQueryString(qs)
          return `${organizationId}/sequences/${sequenceId}/contacts/${contactId}/steps/activities?${params.toString()}`
        },
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_STEP_ACTIVITIES_${args.sequenceId}_${args.contactId}`,
          },
        ],
      }),
      getSequencesContactActivities: builder.query<
        SequenceActivities,
        {
          organizationId: string
          contactId: string
          sequenceId: string
        } & PaginationArgsType
      >({
        query: ({ organizationId, contactId, sequenceId }) => {
          return `${organizationId}/sequences/${sequenceId}/contacts/${contactId}/activities`
        },
        providesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_ACTIVITIES_${args.sequenceId}`,
          },
          {
            type: 'Sequence',
            id: `SEQUENCE_CONTACT_ACTIVITIES_${args.contactId}`,
          },
        ],
      }),
      skipSequenceContact: builder.mutation<
        unknown,
        { organizationId: string; sequenceId: string; contactId: string }
      >({
        query: ({ organizationId, sequenceId, contactId }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/${contactId}/skip`,
            method: 'POST',
            body: {},
          }
        },
        invalidatesTags: (_, error, args) => {
          if (error) {
            return []
          }
          return [
            { type: 'Sequence', id: `SEQUENCE_CONTACT_STEP_${args.contactId}` },
            { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}_LEADS_LIST` },
          ]
        },
      }),
      countSequences: builder.query<
        unknown,
        {
          organizationId: string
          statuses?: SequenceStatus[]
        }
      >({
        query: ({ organizationId, statuses }) => {
          const params = new URLSearchParams()

          statuses?.forEach(status => {
            params.append('status', status)
          })

          return {
            url: `${organizationId}/sequences-count`,
            method: 'GET',
            params,
          }
        },
      }),
      markContactAsReviewed: builder.mutation({
        query: ({
          organizationId,
          sequenceId,
        }: {
          organizationId: string
          sequenceId: string
        }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/review`,
            method: 'PATCH',
            body: {},
          }
        },
        invalidatesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_${args.sequenceId}_LEADS_LIST_${SequenceContactReviewStatus.PENDING}`,
          },
          { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}` },
          { type: 'Sequence', id: 'LIST' },
        ],
      }),
      addContactsFromLeadImport: builder.mutation<
        {
          contactIdsAdded: string[]
          nbExcludeDuplicatesAnotherSequence: number
        },
        {
          organizationId: string
          sequenceId: string
          importId: string
          excludeDuplicatesAnotherSequence: boolean
        }
      >({
        query: ({
          organizationId,
          sequenceId,
          importId,
          excludeDuplicatesAnotherSequence,
        }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/contacts/from-import/${importId}`,
            method: 'POST',
            body: {
              excludeDuplicatesAnotherSequence,
            },
          }
        },
        invalidatesTags: (_, __, args) => [
          {
            type: 'Sequence',
            id: `SEQUENCE_${args.sequenceId}_LEADS_LIST`,
          },
          { type: 'Sequence', id: `SEQUENCE_${args.sequenceId}` },
        ],
      }),
      duplicateSequence: builder.mutation<
        { id: string },
        {
          organizationId: string
          sequenceId: string
          targetSequenceName: string
        }
      >({
        query: ({ organizationId, sequenceId, targetSequenceName }) => {
          return {
            url: `${organizationId}/sequences/${sequenceId}/duplicate`,
            method: 'POST',
            body: {
              targetSequenceName,
            },
          }
        },
        invalidatesTags: [
          { type: 'Sequence', id: 'LIST' },
          { type: 'FeatureGateway', id: 'GET_USAGE' },
        ],
      }),
      contactsSequenceSteps: builder.query<
        ContactSequenceSteps[],
        { organizationId: string; contactIds: string[] }
      >({
        query: ({ organizationId, contactIds }) => {
          return {
            url: `${organizationId}/sequences/contacts/steps`,
            method: 'POST',
            body: { ids: contactIds },
          }
        },
        providesTags: (_, __, args) =>
          args.contactIds.map(contactId => ({
            type: 'Sequence' as const,
            id: `CONTACT_${contactId}_SEQUENCES_STEP`,
          })),
      }),
    }
  },
})

export const {
  useAddSequenceMutation,
  useGetSequenceQuery,
  useGetListSequenceQuery,
  useLazyGetListSequenceQuery,
  useUpdateSequenceMutation,
  useDeleteSequenceMutation,
  useGetLeadsBySequenceQuery,
  useLazyGetSequenceLeadsErrorsQuery,
  useGetSequenceLeadsErrorsQuery,
  useAddLeadsAndAssignStepMutation,
  useDeleteLeadsBySequenceMutation,
  useGetMacroKpisQuery,
  useConsultSequenceActivitesByContactIdsQuery,
  useFilterContactsNotInOtherSequenceMutation,
  useGetSequencesContactStepsQuery,
  useUpdateSequencesContactStepMutation,
  useGetSequencesContactActivitiesQuery,
  useSkipSequenceContactMutation,
  useUpdateContactSequenceMutation,
  useCountSequencesQuery,
  useMarkContactAsReviewedMutation,
  useAddContactsFromLeadImportMutation,
  useDuplicateSequenceMutation,
  useContactsSequenceStepsQuery,
} = sequenceApi

export type LeadsBySequenceWithPagination = ListPagination<LeadBySequence>
