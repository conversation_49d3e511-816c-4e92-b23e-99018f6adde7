import { clsx } from 'clsx'
import { useMemo } from 'react'

import { SequenceStepType } from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

export const StepItemIcon = ({
  type,
  color,
  withBorder = true,
}: {
  type?: SequenceStepApi['type']
  color: 'base-error' | 'base-default'
  withBorder?: boolean
}) => {
  /* Memos */

  const icon = useMemo(() => {
    switch (type) {
      case SequenceStepType.LINKEDIN_SEND_INVITATION:
        return 'Linkedin'
      case SequenceStepType.LINKEDIN_SEND_MESSAGE:
        return 'Linkedin'
      case SequenceStepType.LINKEDIN_VISIT_PROFILE:
        return 'Linkedin'
      case SequenceStepType.EMAIL:
        return 'Mail'
      case SequenceStepType.REPLY_IN_THREAD:
        return 'MailIn'
      case SequenceStepType.CALL:
        return 'Phone'
      default:
        return 'Mail'
    }
  }, [type])

  return (
    <div
      className={clsx('p-0.5 rounded-md', {
        'border-ui-error-default bg-base-error': color === 'base-error',
        border: withBorder,
      })}
    >
      <Icon name={icon} size="small" color={color} />
    </div>
  )
}
