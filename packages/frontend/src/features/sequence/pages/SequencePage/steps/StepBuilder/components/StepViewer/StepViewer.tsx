import isEmpty from 'lodash/isEmpty'
import { type Mouse<PERSON>vent, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { SequenceEvents } from '@getheroes/frontend/hooks'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Helper,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  <PERSON><PERSON><PERSON>,
  Typography,
} from '@getheroes/ui'
import { ConfirmationModal } from '@getheroes/ui-business'
import emptyStateImage from '@internals/assets/images/empty-state-steps.png'
import { useDuplicateSequenceStep } from '@internals/features/sequence/hooks/useDuplicateSequenceStep'
import { BOOK_DEMO_URL } from '@internals/features/subscription/hooks/usePricingTable'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'

import { checkIfStepHasError } from '../../../../utils/checkIfStepHasError'
import { useStepBuilder } from '../StepBuilderProvider'
import { StepCard } from '../StepCard'
import { StepDeleteModal } from '../StepDeleteModal'
import { StepItemFormName } from '../StepItemFormName'
import { StepItemIcon } from '../StepItemIcon'

import { AddLinkedinSteps } from './components/AddLinkedinSteps/AddLinkedinSteps'
import { Email } from './components/Email'
import { EmptyState } from './components/EmptyState'
import { LinkedInDM } from './components/LinkedInDM'
import { LinkedInRequestConnection } from './components/LinkedInRequestConnection'
import { LinkedInVisitProfile } from './components/LinkedInVisitProfile'

const VIDEO_URLS = {
  demo: 'https://www.youtube.com/embed/HaEuufq7iKY?si=bgDdKJhLy6HZ_wlq',
}

const CALL_STEP_BACKGROUND_IMAGE =
  'https://zeliq-dev-public.s3.eu-west-3.amazonaws.com/call-step.png'

export const StepViewer = () => {
  /* #region Vars */

  const { t } = useTranslation('sequence')
  const {
    currentStepMode,
    currentStep,
    onSelectedStep,
    isStepsLoading,
    onUnsavedChangesModalOpen,
    isUnsavedChangesModalOpen,
    onUnsavedChangesConfirm,
    steps,
    sequence,
  } = useStepBuilder()
  const hasError = checkIfStepHasError(currentStep)
  const { sendEvent } = useTrackingContext()

  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false)
  const [isEditName, setIsEditName] = useState(false)

  /* #region Queries */

  const { duplicateSequenceStep } = useDuplicateSequenceStep()

  /* #region Memos */

  const firstEmailStep = useMemo(() => {
    return steps.find(step => step.type === 'email')
  }, [steps])

  const stepViewerContent = useMemo(() => {
    switch (currentStep?.type) {
      case 'linkedin_send_invitation':
        // We need to use a key because of the Lexical editor initialization
        return (
          <LinkedInRequestConnection
            step={currentStep}
            isSequenceLive={sequence?.status === 'in-progress'}
            key={currentStep.id}
          />
        )
      case 'linkedin_visit_profile':
        return <LinkedInVisitProfile />
      case 'linkedin_send_message':
        // We need to use a key because of the Lexical editor initialization
        return (
          <LinkedInDM
            step={currentStep}
            key={currentStep.id}
            isSequenceLive={sequence?.status === 'in-progress'}
          />
        )
      case 'email':
      case 'manual_email':
        // We need to use a key because of the Lexical editor initialization
        return (
          <Email
            step={currentStep}
            key={currentStep.id}
            isSequenceLive={sequence?.status === 'in-progress'}
            isFirstEmailStep={firstEmailStep?.id === currentStep.id}
          />
        )
      case 'reply_in_thread':
        return (
          <Email
            step={currentStep}
            key={currentStep.id}
            isSequenceLive={sequence?.status === 'in-progress'}
            isFirstEmailStep={false}
          />
        )
      case 'call':
        return <Card backgroundImage={CALL_STEP_BACKGROUND_IMAGE} fullHeight />
      default:
        return null
    }
  }, [currentStep, firstEmailStep, sequence?.status])

  /* Functions */

  const handleBookADemo = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()

    window.open(BOOK_DEMO_URL, '_blank', 'noreferrer')
  }

  const duplicateStep = async () => {
    if (currentStep) {
      const newStep = await duplicateSequenceStep({
        name: currentStep.name,
        type: currentStep.type,
        waitingBetweenStep: currentStep.waitingBetweenStep,
        content: currentStep.content,
        order: currentStep.order,
      })

      sendEvent(SequenceEvents.SEQUENCE_V2_STEPS_HEADER_CLICK_DUPLICATE, {
        pages: 'SequenceV2',
        component: 'StepsHeader',
        feature: 'Duplicate',
        action: 'Click',
        type: currentStep?.type ?? '',
      })

      if (newStep) {
        onSelectedStep(newStep.id)
      }
    }
  }

  /* #region Render */

  if (currentStepMode === 'linkedin') {
    return <AddLinkedinSteps />
  }

  if (isStepsLoading) {
    return (
      <div className="flex justify-center items-center h-full flex-1">
        <Spinner color="basic-black" size="small" />
      </div>
    )
  }

  if (!isStepsLoading && isEmpty(steps)) {
    return (
      <div className="flex-1">
        <Card fullHeight backgroundImage={emptyStateImage} isCondense>
          <EmptyState
            title={t('Discover how to use omnichannel sequences!')}
            videoUrl={VIDEO_URLS.demo}
            descriptionText={
              <>
                <Typography variant="heading" size="s" weight="bold">
                  {t('Jump on a live demo')}
                </Typography>
                <Typography
                  variant="label"
                  size="m"
                  weight="medium"
                  color="base-label"
                  align="center"
                >
                  {t('Camille, our Customer Success Manager, offers')}{' '}
                  <Typography
                    variant="label"
                    size="l"
                    weight="bold"
                    color="decorative-brand"
                  >
                    {t('free 30-minute demo sessions')}
                  </Typography>{' '}
                  {t('to help you discover the power of ZELIQ.')}
                </Typography>
              </>
            }
            buttonComponent={
              <Button
                variant="primary"
                size="large"
                fullWidth
                onClick={handleBookADemo}
              >
                {t('Book a demo')}
              </Button>
            }
          />
        </Card>
      </div>
    )
  }

  return (
    <>
      <StepCard className="flex-1">
        <StepCard.Header>
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Badge
                size="small"
                color="light-grey"
                count={currentStep?.order}
              />

              <form className="flex items-center gap-1">
                <StepItemIcon
                  type={currentStep?.type}
                  color={hasError ? 'base-error' : 'base-default'}
                  withBorder={false}
                />

                {currentStep && (
                  <StepItemFormName
                    isEditing={isEditName}
                    handleToogleEditingMode={setIsEditName}
                    step={currentStep}
                  />
                )}
              </form>
            </div>

            <div className="flex items-center gap-1">
              <Tooltip content={t('Edit step')}>
                <IconButton
                  icon="Edit"
                  variant="tertiary-outlined"
                  size="small"
                  onClick={() => setIsEditName(!isEditName)}
                />
              </Tooltip>

              <Tooltip content={t('Duplicate step')}>
                <IconButton
                  icon="Copy"
                  variant="tertiary-outlined"
                  size="small"
                  onClick={duplicateStep}
                />
              </Tooltip>

              <Tooltip content={t('Delete step')}>
                <IconButton
                  icon="Trash"
                  variant="tertiary-outlined"
                  size="small"
                  onClick={() => setIsConfirmationModalOpen(true)}
                />
              </Tooltip>
            </div>
          </div>
        </StepCard.Header>

        <StepCard.Body>
          <div className="h-full flex flex-col gap-4">
            {currentStep?.progression === 'INPROGRESS' && (
              <Helper
                description={t(
                  'The step is already in progress, changes will only affect contacts who haven’t completed it.'
                )}
                color="blue-secondary"
                icon="WarningCircle"
                endComponent={<IconButton icon="Xmark" />}
              />
            )}

            {currentStep?.progression === 'COMPLETED' && (
              <Helper
                description={t(
                  'All leads in the sequence have finished this steps. New leads added will receive the updated content.'
                )}
                color="blue-secondary"
                icon="WarningCircle"
                endComponent={<IconButton icon="Xmark" />}
              />
            )}

            {stepViewerContent}
          </div>
        </StepCard.Body>
      </StepCard>

      <ConfirmationModal
        open={isUnsavedChangesModalOpen}
        onOpenChange={onUnsavedChangesModalOpen}
        title={t('Are you sure you want to leave without saving?')}
        confirmButtonText={t('Yes I want to leave')}
        cancelButtonText={t('No I want to stay')}
        onConfirm={onUnsavedChangesConfirm}
      />

      <StepDeleteModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        step={currentStep}
        steps={steps}
      />
    </>
  )
}
