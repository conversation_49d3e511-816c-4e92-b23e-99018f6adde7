import { useEffect, useMemo, useRef } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ToastType, useToast } from '@getheroes/frontend/hooks'
import type { SequenceActivitiesHistory } from '@getheroes/frontend/types'
import type { Contact, Organization } from '@getheroes/shared'
import {
  EmailAttachment,
  EnrichmentType,
  LocaleEnum,
  toMo,
} from '@getheroes/shared'
import type { SelectValue } from '@getheroes/ui'
import { CardV2, Helper } from '@getheroes/ui'
import { SelectEmails, SelectUser } from '@getheroes/ui-business'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useValidator'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import { useAssignUserToLeadsMutation } from '@internals/features/lead/api/assignApi'
import { useGetProvidersStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetProvidersStatus'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { useUpdateSequenceContactEmail } from '@internals/features/sequence/hooks/useUpdateSequenceContactEmail'
import { useUpdateSequenceContactStep } from '@internals/features/sequence/hooks/useUpdateSequenceContactStep'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

import { useMembersOptions } from '../../../../hooks/useMembersOptions'

interface PreviewEmailProps {
  history: SequenceActivitiesHistory
  contact: Contact
}

interface FormValues {
  subject?: string
  body?: string
  addSignature?: boolean
}

const EMAIL_SUBJECT_MAX_LENGTH = 77

export const PreviewEmail = ({ history, contact }: PreviewEmailProps) => {
  /* #region Vars */

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { t, i18n } = useTranslation('sequence')
  const { createToast } = useToast()

  const lexicalSubjectRef = useRef<LexicalImperativeHandle>(null)
  const lexicalBodyRef = useRef<LexicalImperativeHandle>(null)

  const values = {
    to: history.data.content?.to,
    subject: history.data.step?.content?.subject ?? '',
    body: history.data.step?.content?.body ?? '',
  }

  const methods = useForm<FormValues>({
    values,
    mode: 'onChange',
  })

  const {
    control,
    setValue,
    formState: { errors, isDirty, isValid },
  } = methods

  const membersOptions = useMembersOptions('email')

  /* #region Queries */

  const { checkVariableError } = useVariableValidator({ organizationId })
  const { checkEmptyError } = useValidator()
  const [assignUserToLeads] = useAssignUserToLeadsMutation()

  const [updateSequenceContactStep] = useUpdateSequenceContactStep({
    contactId: contact.id,
    stepId: history?.data?.step?.id ?? '',
  })

  const [
    updateSequenceContactEmail,
    { isLoading: isLoadingUpdateSequenceContactEmail },
  ] = useUpdateSequenceContactEmail(contact)

  const { items: enrichItems, isLoadingEnrichment } = useGetProvidersStatus({
    enrichmentType: EnrichmentType.EMAIL,
    lead: contact ?? {},
  })
  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()

  /* #region Effects */

  useEffect(() => {
    if (lexicalBodyRef.current) {
      lexicalBodyRef.current.setHtmlContent(
        history.data.step?.content?.body ?? ''
      )
      lexicalBodyRef.current.setHasSignature(
        !!history.data.content?.addSignature
      )
      lexicalBodyRef.current.setIsPreview(true)
    }

    if (lexicalSubjectRef.current) {
      lexicalSubjectRef.current.setHtmlContent(
        history.data.step?.content?.subject ?? ''
      )
      lexicalSubjectRef.current.setIsPreview(true)
    }
  }, [history.data])

  /* #region Memos */

  const contactEmailOptions = useMemo(() => {
    const emails = contact.emails || []

    return emails.map(email => {
      const enrichedEmail = contact.emailsCheckResult?.find(
        emailCheckResult => emailCheckResult.mail === email
      )

      return {
        label: `${contact.firstName} ${contact.lastName}`,
        value: email,
        data: {
          firstName: contact.firstName,
          lastName: contact.lastName,
          status: enrichedEmail,
          isNew: !enrichedEmail,
        },
      }
    })
  }, [contact])

  /* #region Functions */

  const handleChangeSubject =
    (callback: (value: string) => void) =>
    ({ raw }: EditorOnChangeType) => {
      callback(raw)
    }

  const handleChangeBody =
    (callback: (value: string) => void) =>
    ({ html, addSignature }: EditorOnChangeType) => {
      if (addSignature !== undefined) {
        setValue('addSignature', addSignature)
        return
      }
      if (html) {
        callback(html)
      }
    }

  const saveEmail = () => {
    const formValues = methods.getValues()
    const canSubmit = isDirty && isValid

    if (canSubmit) {
      const { subject, body } = formValues
      updateSequenceContactStep({
        content: {
          subject,
          body: body as string,
        },
      })
    }
  }

  const handleContactEmailChange = async (value: SelectValue<string>) => {
    const isAddedEmail = !contactEmailOptions.find(
      option => option.value === value
    )

    updateSequenceContactEmail(value as string, isAddedEmail)
  }

  const handleUpdateAssign = async (userId: string) => {
    try {
      await assignUserToLeads({
        organizationId,
        userId,
        contacts: [contact.id],
      }).unwrap()

      createToast({
        message: t('Contact updated successfully'),
        type: ToastType.MAIN,
      })
    } catch (error) {
      createToast({
        message: t('Error updating contact'),
        type: ToastType.ERROR,
      })
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <SelectUser
          onChange={value => handleUpdateAssign(value as string)}
          value={history.data.content?.sender?.userId}
          label={t('From')}
          options={membersOptions}
          size="medium"
          fullWidth
          searchPlaceholder={t('Search an sender')}
          additionalContent={
            <div className="max-w-96">
              <Helper
                description={t(
                  'Updating this sender will apply to other sender steps in the sequence.'
                )}
                color="orange-secondary"
              />
            </div>
          }
        />
        <SelectEmails
          label={t('To')}
          size="medium"
          disabled={isLoadingUpdateSequenceContactEmail}
          additionalContent={
            <div className="max-w-96">
              <Helper
                description={t(
                  'Updating this email will apply to other email steps in the sequence.'
                )}
                color="orange-secondary"
              />
            </div>
          }
          fullWidth
          options={contactEmailOptions}
          error={
            contactEmailOptions.length === 0
              ? t(
                  'This field is required. Please enrich or manually add an email'
                )
              : ''
          }
          value={history.data.content?.to ?? ''}
          // @ts-expect-error TS2322: Type
          onChange={handleContactEmailChange}
          onEnrich={() => enrichPhoneOrEmail([contact], EnrichmentType.EMAIL)}
          isEnrichLoading={isLoadingEnrichment}
          enrichItems={enrichItems}
        />
      </div>

      {history.type === 'step_manual_email' && (
        <div className="h-64">
          <CardV2
            isFullHeight
            backgroundImage={
              i18n.language === LocaleEnum.FRENCH
                ? 'https://zeliq-dev-public.s3.eu-west-3.amazonaws.com/Small_Illustration_Manual_Email_fr.png'
                : 'https://zeliq-dev-public.s3.eu-west-3.amazonaws.com/manual-email-step-live-preview.png'
            }
          />
        </div>
      )}

      {history.type !== 'step_manual_email' && (
        <>
          <Controller
            control={control}
            name="subject"
            rules={{
              validate: {
                variable: value => checkVariableError(value),
                empty: value =>
                  checkEmptyError(value, t('You should add a subject content')),
              },
              maxLength: {
                value: EMAIL_SUBJECT_MAX_LENGTH,
                message: t('{{field}} must not exceed {{number}} characters', {
                  field: 'Message',
                  number: EMAIL_SUBJECT_MAX_LENGTH,
                  ns: 'validation',
                }),
              },
            }}
            disabled={history.type === 'step_reply_in_thread'}
            render={({ field: { name, onChange, disabled } }) => (
              <Lexical
                name={name}
                ref={lexicalSubjectRef}
                dataTestId={
                  idReferentials.sequence.components.addSteps.email.subject
                }
                toolbarOptions={['singleLine', 'variable', 'preview']}
                isEnablePreviewOnClick
                variableReferences={contact}
                placeholder={t('Set the email subject')}
                onChange={handleChangeSubject(onChange)}
                onBlur={saveEmail}
                error={errors.subject?.message}
                variant="input"
                label={t('Subject')}
                editable={!disabled}
              />
            )}
          />

          {/* Email content */}

          <Controller
            control={control}
            name="body"
            rules={{
              validate: {
                variable: value => checkVariableError(value),
                empty: value =>
                  checkEmptyError(value, t('You must add an email content')),
              },
            }}
            render={({ field: { name, onChange } }) => (
              <Lexical
                dataTestId={
                  idReferentials.sequence.components.addSteps.email.body
                }
                ref={lexicalBodyRef}
                name={name}
                error={errors.body?.message}
                className="min-h-20 max-h-96"
                toolbarOptions={['variable', 'clearFormatting', 'preview']}
                floatingToolbarOptions={[
                  'textAlign',
                  'familyFont',
                  'blockTypeFormat',
                ]}
                isEnablePreviewOnClick
                variableReferences={contact}
                onChange={handleChangeBody(onChange)}
                onBlur={saveEmail}
                attachmentOptions={{
                  maxTotalSize: toMo(EmailAttachment.MAX_TOTAL_SIZE),
                  maxFiles: EmailAttachment.MAX_FILE,
                }}
                getAttachments={fileAttachments =>
                  setValue('attachments', fileAttachments)
                }
                label={t('Email content')}
              />
            )}
          />
        </>
      )}
    </div>
  )
}
