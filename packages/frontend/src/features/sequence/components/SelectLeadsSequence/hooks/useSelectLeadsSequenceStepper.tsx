import { isEmpty } from 'lodash'
import { useReducer } from 'react'

import type { Step } from '@internals/components/common/navigation/Stepper/Stepper.type'
import i18n from '@internals/config/i18n'
import type {
  LeadsSelectionInCreationState,
  SelectLeadsSequenceProcessAction,
} from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useSelectLeadsSequenceProcess'
import {
  SelectLeadsSequenceProcessContext,
  SelectLeadsSequenceStepActionType,
  SelectLeadsSequenceStepKeyEnum,
} from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useSelectLeadsSequenceProcess'
import { useStepper } from '@internals/hooks/useStepper'

/**********************
 ** Data **
 **********************/

const getDefaultStepList = (i18nConfig: any) =>
  [
    {
      key: SelectLeadsSequenceStepKeyEnum.SELECT_LEADS_IN_VIEW,
      label: i18nConfig.t('Select leads', { ns: 'sequence' }),
      isActive: true,
      isDisabled: false,
      isCompleted: false,
    },
    {
      key: SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS,
      label: i18nConfig.t('Review selection', { ns: 'sequence' }),
      isActive: false,
      isDisabled: true,
      isCompleted: false,
      customNextButtonLabel: i18n.t('Add leads', { ns: 'sequence' }) as string,
    },
  ] as Step<SelectLeadsSequenceStepKeyEnum>[]

const getDefaultState = (i18n: any) => ({
  currentStepList: getDefaultStepList(i18n),
  selectedViewId: null,
  selectedContactIdList: null,
  isAddAllLeadsInView: false,
})

/**********************
 ** Reducer **
 **********************/

const SelectLeadsSequenceProcessReducer = (
  state: LeadsSelectionInCreationState,
  action: SelectLeadsSequenceProcessAction
): LeadsSelectionInCreationState => {
  const { type, payload } = action

  switch (type) {
    case SelectLeadsSequenceStepActionType.SET_SELECTED_VIEW:
      return {
        ...state,
        selectedViewId: payload?.selectedViewId || null,
      }
    case SelectLeadsSequenceStepActionType.SET_SELECT_SOME_LEADS:
      return {
        ...state,
        selectedContactIdList: payload?.selectedContactIdList || null,
      }

    case SelectLeadsSequenceStepActionType.SET_SELECT_ALL_LEADS_IN_VIEW:
      return {
        ...state,
        isAddAllLeadsInView: payload?.isAddAllLeadsInView || false,
      }

    case SelectLeadsSequenceStepActionType.SET_DISABLE_UNSELECT_LEADS_STEP:
      return {
        ...state,
        currentStepList: state.currentStepList.map(step => {
          if (step.key === SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS) {
            return {
              ...step,
              isCompleted: false,
              isDisabled: true,
            }
          }
          return step
        }),
      }

    case SelectLeadsSequenceStepActionType.SET_ENABLE_UNSELECT_LEADS_STEP:
      return {
        ...state,
        currentStepList: state.currentStepList.map(step => {
          if (step.key === SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS) {
            return {
              ...step,
              isDisabled: false,
            }
          }
          return step
        }),
      }

    case SelectLeadsSequenceStepActionType.SET_STEP_LIST:
      return {
        ...state,
        currentStepList: payload?.currentStepList || state.currentStepList,
      }

    default:
      return state
  }
}

export const SelectLeadsSequenceProcessProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [selectLeadsSequenceState, dispatch] = useReducer(
    SelectLeadsSequenceProcessReducer,
    // Note : we should give i18n as a parameter to the function if we want to use translation
    // Documentation say : "So you should wait for init before using the t function"
    // https://www.i18next.com/overview/api
    getDefaultState(i18n)
  )

  const setSelectedView = (selectedViewId: string) => {
    dispatch({
      type: SelectLeadsSequenceStepActionType.SET_SELECTED_VIEW,
      payload: { selectedViewId },
    })
  }

  const setSelectedLeads = (selectedContactIdList: Array<string>) => {
    const hasAtLeastOneContactSelected = selectedContactIdList
      ? selectedContactIdList.length > 0
      : false

    dispatch({
      type: SelectLeadsSequenceStepActionType.SET_SELECT_SOME_LEADS,
      payload: { selectedContactIdList },
    })

    if (hasAtLeastOneContactSelected) {
      dispatch({
        type: SelectLeadsSequenceStepActionType.SET_ENABLE_UNSELECT_LEADS_STEP,
        payload: null,
      })
    } else {
      dispatch({
        type: SelectLeadsSequenceStepActionType.SET_DISABLE_UNSELECT_LEADS_STEP,
        payload: null,
      })
    }
  }

  const setSelectedAllLeadsInView = (isAddAllLeadsInView: boolean) => {
    dispatch({
      type: SelectLeadsSequenceStepActionType.SET_SELECT_ALL_LEADS_IN_VIEW,
      payload: { isAddAllLeadsInView },
    })
    if (isAddAllLeadsInView) {
      dispatch({
        type: SelectLeadsSequenceStepActionType.SET_ENABLE_UNSELECT_LEADS_STEP,
        payload: null,
      })
    } else {
      dispatch({
        type: SelectLeadsSequenceStepActionType.SET_DISABLE_UNSELECT_LEADS_STEP,
        payload: null,
      })
    }
  }

  const checkIfStepIsCompleted = (stepKey: string) => {
    switch (stepKey) {
      case SelectLeadsSequenceStepKeyEnum.SELECT_LEADS_IN_VIEW:
        return (
          selectLeadsSequenceState.isAddAllLeadsInView ||
          !isEmpty(selectLeadsSequenceState.selectedContactIdList)
        )
      case SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS: {
        const isFormStateValid =
          selectLeadsSequenceState.isAddAllLeadsInView ||
          !isEmpty(selectLeadsSequenceState.selectedContactIdList)

        const isUnselectLeadsStepActive =
          selectLeadsSequenceState.currentStepList.find(
            step => step.key === SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS
          )?.isActive || false

        const isUnselectLeadsStepNotDisabled =
          !selectLeadsSequenceState.currentStepList.find(
            step => step.key === SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS
          )?.isDisabled

        return (
          isFormStateValid &&
          isUnselectLeadsStepActive &&
          isUnselectLeadsStepNotDisabled
        )
      }
      default:
        return false
    }
  }

  const { goToStep, goToNextStep, goToPreviousStep, currentStepKey } =
    useStepper(
      selectLeadsSequenceState.currentStepList,
      (newStepList: Step<SelectLeadsSequenceStepKeyEnum>[]) => {
        dispatch({
          type: SelectLeadsSequenceStepActionType.SET_STEP_LIST,
          payload: { currentStepList: newStepList },
        })
      },
      checkIfStepIsCompleted
    )

  return (
    <SelectLeadsSequenceProcessContext.Provider
      value={{
        // generic stepper state
        currentStepKey,

        // specific state for this process
        selectLeadsSequenceState,

        // generic stepper actions
        goToStep,
        goToNextStep,
        goToPreviousStep,

        // specific actions for this process
        setSelectedView,
        setSelectedLeads,
        setSelectedAllLeadsInView,
      }}
    >
      {children}
    </SelectLeadsSequenceProcessContext.Provider>
  )
}
