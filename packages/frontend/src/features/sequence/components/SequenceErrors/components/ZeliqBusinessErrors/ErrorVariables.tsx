import { yupResolver } from '@hookform/resolvers/yup'
import type { ReactNode } from 'react'
import { useMemo } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Contact } from '@getheroes/shared'
import { EnrichmentMessage } from '@internals/components/business/ContactSequenceActivities/components/EnrichmentMessage/EnrichmentMessage'
import { Attribute } from '@internals/components/business/lead/common/Attribute/Attribute'
import { SelectEmailWithEnrichment } from '@internals/components/business/lead/contact/SelectEmailWithEnrichment/SelectEmailWithEnrichment'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useLeadEnrichmentProgress } from '@internals/features/lead/hooks/enrichment/useLeadEnrichmentProgress'
import { useGetContactFieldSchema } from '@internals/features/lead/hooks/leadFields/useGetContactFieldSchema'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import { idReferentials } from '@internals/utils/idReferentials'

const DEFAULT_RECENT_ENRICHMENT_REFERENCE_DATE = 20 * 1000 // 20 seconds in milliseconds

export type ErrorVariablesProps = {
  isLoading: boolean
  fieldList: LeadsField[]
  onSubmit: (e: Partial<Contact>) => void
  contact: Contact
  title: ReactNode
  onEnrich?: (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => void
}

export const ErrorVariables = ({
  isLoading,
  fieldList,
  onSubmit,
  contact,
  title,
  onEnrich,
}: ErrorVariablesProps) => {
  const { t } = useTranslation('sequence')

  // The email field is treated separately
  const emailField = useMemo(() => {
    return fieldList.find(
      field => field.id === ExclusiveContactLeadFieldEnum.EMAILS
    )
  }, [fieldList])
  const fieldListWithoutEmail = useMemo(() => {
    return fieldList.filter(
      field => field.id !== ExclusiveContactLeadFieldEnum.EMAILS
    )
  }, [fieldList])

  const schema = useGetContactFieldSchema({
    fields: fieldList,
  })
  const { isLoadingEnrichmentState } = useLeadEnrichmentProgress(contact)

  const defaultValues = useMemo(() => {
    const hasGenderField = fieldList.find(
      field => field.id === ExclusiveContactLeadFieldEnum.GENDER
    )

    if (hasGenderField) {
      return { [ExclusiveContactLeadFieldEnum.GENDER]: 0 }
    }
    return {}
  }, [fieldList])

  const checkHasFieldRecentlyBeenEnriched = ({
    lastEnrichmentDate,
    referenceDate = DEFAULT_RECENT_ENRICHMENT_REFERENCE_DATE,
  }: {
    lastEnrichmentDate: string | undefined
    referenceDate: number
  }) => {
    if (!lastEnrichmentDate) {
      return false
    }

    const lastEnrichmentDateTime = new Date(lastEnrichmentDate).getTime()
    const currentDate = new Date().getTime()
    const timeDifference = currentDate - lastEnrichmentDateTime

    return timeDifference < referenceDate
  }

  const formMethods = useForm<Partial<Contact>>({
    resolver: (data, context, options) => {
      return yupResolver(schema)(
        data,
        // Context data that will be passed to the validation schema
        // https://www.npmjs.com/package/yup#schemawhenkeys-string--string-builder-object--values-any-schema--schema-schema
        {
          ...context,
          isRequired: true,
          requiredFields: fieldList.map(field => field.id),
        },
        options
      )
    },
    defaultValues,
  })

  const enrichedEmailsCounter = contact?.enrichmentResult?.emails?.length || 0
  const isRecentMailEnrichment = checkHasFieldRecentlyBeenEnriched({
    lastEnrichmentDate: contact?.enrichmentEmailDate,
    referenceDate: DEFAULT_RECENT_ENRICHMENT_REFERENCE_DATE,
  })

  return (
    <div className="c-sequence-error-variables flex flex-col gap-2 w-full">
      {title}

      <FormProvider {...formMethods}>
        <form
          className="flex flex-col gap-2"
          onSubmit={formMethods.handleSubmit(onSubmit)}
        >
          <div className="flex flex-col gap-2">
            {emailField && (
              <SelectEmailWithEnrichment
                dataTestId={
                  idReferentials.sequence.components.ErrorVariables.selectEmail
                }
                contact={contact}
                onSelectOption={option => {
                  formMethods.setValue('emails', [
                    ...(contact.emails || []),
                    option.value as string,
                  ])
                }}
                error={formMethods.formState.errors.emails?.message || ''}
                onEnrich={() => {
                  if (onEnrich) {
                    onEnrich(ExclusiveContactLeadFieldEnum.EMAILS)
                  }
                }}
                isLoadingEnrichment={
                  isLoadingEnrichmentState[ExclusiveContactLeadFieldEnum.EMAILS]
                }
                enrichmentMessage={
                  isRecentMailEnrichment && (
                    <EnrichmentMessage nbEnrichments={enrichedEmailsCounter} />
                  )
                }
              />
            )}
            {fieldListWithoutEmail.map(field => (
              <Attribute
                key={field.id}
                dataTestIdPrefix={
                  idReferentials.sequence.components.ErrorVariables.attributes
                }
                lead={contact}
                attribute={field}
                forceEditable
              />
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              size={ButtonSize.MEDIUM}
              dataTestId={
                idReferentials.sequence.components.ErrorVariables.saveButton
              }
              variant={ButtonVariant.TERTIARY_OUTLINED}
              isLoading={isLoading}
            >
              {t('Save')}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  )
}
