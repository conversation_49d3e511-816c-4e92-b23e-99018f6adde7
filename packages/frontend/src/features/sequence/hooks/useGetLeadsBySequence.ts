import { useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { useGetLeadsBySequenceQuery } from '@internals/features/sequence/api/sequenceApi'
import { useTypedSelector } from '@internals/store/store'

export type UseGetLeadsBySequenceProps = {
  sequenceId: string
  filters?: {
    emails?: boolean
    phones?: boolean
  }
  pagination?: {
    page: number
    limitPerPage: number
  }
}

export const useGetLeadsBySequence = ({
  sequenceId,
  filters,
  pagination,
}: UseGetLeadsBySequenceProps) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { currentPage, pageSize } = useGridContext()

  const {
    data: leadsBySequenceData,
    isError,
    isLoading,
    isSuccess,
    isFetching,
    isUninitialized,
    refetch,
  } = useGetLeadsBySequenceQuery(
    {
      organizationId,
      sequenceId,
      pagination: {
        limitPerPage: pagination?.limitPerPage || pageSize || 50,
        page: pagination?.page || currentPage || 1,
      },
      filters,
    },
    {
      skip: !sequenceId,
    }
  )

  const leads = useMemo(() => {
    return Object.values(leadsBySequenceData?.items || {}).map(lead => ({
      ...lead,
      id: lead.contactId,
    }))
  }, [leadsBySequenceData?.items])

  return {
    leads,
    meta: leadsBySequenceData?.meta,
    isUninitialized,
    isError,
    isLoading,
    isSuccess,
    isFetching,
    refetch,
  }
}
