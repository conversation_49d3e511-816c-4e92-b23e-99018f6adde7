import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { getSequenceId } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ToastType, useToast } from '@getheroes/frontend/hooks'
import type { SequenceStepType } from '@getheroes/shared'
import { useAddSequenceStepMutation } from '@internals/features/sequence/api/sequenceStepApi'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'
import { useTypedSelector } from '@internals/store/store'

export const useAddSequenceStep = () => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const { createToast } = useToast()
  const sequenceId = useTypedSelector(getSequenceId)
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const [stepType, setStepType] = useState<SequenceStepType>()

  /* Queries */

  const [addSequenceStep, { isError, isSuccess, isLoading }] =
    useAddSequenceStepMutation()

  /* Effects */

  useEffect(() => {
    if (isSuccess) {
      createToast({
        type: ToastType.MAIN,
        message: t('Step has been added'),
      })
    }

    if (isError) {
      createToast({
        type: ToastType.ERROR,
        message: t('Error while add the step. Please try again'),
      })
    }
  }, [createToast, isError, isSuccess, t])

  /* Functions */

  const createSequenceStep = useCallback(
    async (
      step: Pick<
        SequenceStepApi,
        'name' | 'type' | 'waitingBetweenStep' | 'content'
      > & { order?: number }
    ) => {
      setStepType(step.type)

      if (!organization || !sequenceId) {
        return
      }

      const response = await addSequenceStep({
        organizationId: organization?.id,
        step,
        sequenceId,
      })

      return 'data' in response ? response.data : undefined
    },
    [addSequenceStep, organization, sequenceId]
  )

  return useMemo(
    () => ({
      createSequenceStep,
      isSuccess,
      isError,
      isLoading,
      stepType,
    }),
    [createSequenceStep, isError, isSuccess, isLoading, stepType]
  )
}
