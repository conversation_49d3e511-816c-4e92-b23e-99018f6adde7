import { useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useGetSequencesContactStepsQuery } from '@internals/features/sequence/api/sequenceApi'
import type { SequencesContactSteps } from '@internals/features/sequence/types/sequence'
import { useTypedSelector } from '@internals/store/store'

export type UseGetContactSequencesStepsReturnType = ReturnType<
  typeof useGetSequencesContactStepsQuery
>

export const useGetContactSequencesSteps = ({
  contactId,
  sequenceId,
  skip,
}: {
  contactId: string
  sequenceId?: string
  skip?: boolean
}): UseGetContactSequencesStepsReturnType & {
  steps: SequencesContactSteps[]
} => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { data, ...restResult } = useGetSequencesContactStepsQuery(
    {
      organizationId,
      contactId,
    },
    {
      skip,
    }
  )

  const steps = useMemo(() => {
    if (!sequenceId) {
      return data?.items || []
    }

    const items = data?.items || []
    return items.filter(({ sequence }) => {
      return sequence.id === sequenceId
    })
  }, [data?.items, sequenceId])

  return useMemo(() => {
    return {
      data,
      steps,
      ...restResult,
    }
  }, [data, restResult, steps])
}
