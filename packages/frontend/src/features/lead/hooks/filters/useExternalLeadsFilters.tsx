import { debounce } from 'lodash'
import { useCallback, useState } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { ExternalLeadsFilterIdEnum } from '@getheroes/shared'
import { useLazyGetAutocompleteFilterQuery } from '@internals/features/lead/api/filtersApi'
import type { ExternalLeadsFilterFormState } from '@internals/features/search/types/searchLeadsType'
import { useTypedSelector } from '@internals/store/store'

type AutocompleteItem = {
  id: string
  label: string
}

export type AutocompleteResultsType = {
  [key: string]: AutocompleteItem[]
}

type UseExternalLeadsFiltersReturnType = {
  handleAutocompleteChange: (value: string, filterId: string) => void
  autocompleteResults: AutocompleteResultsType | null
  getCurrentSearchOptionsNotSelected: (
    externalLeadsFilterId: ExternalLeadsFilterIdEnum,
    formState: ExternalLeadsFilterFormState,
    handleFilterValueClick: (
      key: string,
      filterId: ExternalLeadsFilterIdEnum
    ) => void
  ) => {
    key: string
    label: string
    onClick: (key: string) => void
    isActive: boolean
  }[]
  getSelectedFilterOptions: (
    externalLeadsFilterId: ExternalLeadsFilterIdEnum,
    formState: ExternalLeadsFilterFormState,
    handleFilterValueClick: (
      key: string,
      filterId: ExternalLeadsFilterIdEnum
    ) => void
  ) => {
    key: string
    label: string
    onClick: (key: string) => void
    isActive: boolean
  }[]
}

export const useExternalLeadsFilters: () => UseExternalLeadsFiltersReturnType =
  () => {
    const [autocompleteResults, setAutocompleteResults] =
      useState<AutocompleteResultsType | null>(null)

    const { id: organizationId } =
      useTypedSelector(selectCurrentUserOrganization) || {}

    const [getAutocompleteFilterById] = useLazyGetAutocompleteFilterQuery()

    // ***************************************************** //
    // ****************** Utils functions ****************** //
    // ***************************************************** //

    const handleAutocompleteChange = useCallback(
      debounce(async (value: string, filterId: string) => {
        if (!value) {
          return
        }
        const results = await getAutocompleteFilterById({
          organizationId: organizationId as string,
          filterId: filterId,
          searchText: value,
        }).unwrap()
        setAutocompleteResults(prev => {
          return {
            ...prev,
            [filterId]: results,
          }
        })
      }, 200),
      [getAutocompleteFilterById, organizationId]
    )

    // ***************************************************** //
    // **************** Exported functions ***************** //
    // ***************************************************** //

    const getCurrentSearchOptionsNotSelected = (
      externalLeadsFilterId: ExternalLeadsFilterIdEnum,
      formState: ExternalLeadsFilterFormState,
      handleFilterValueClick: (
        key: string,
        filterId: ExternalLeadsFilterIdEnum
      ) => void
    ) => {
      return (
        autocompleteResults?.[externalLeadsFilterId]
          ?.filter(
            option =>
              !(formState[externalLeadsFilterId] || []).includes(option.label)
          )
          ?.map(option => ({
            key: option.id,
            label: option.label,
            onClick: (key: string) =>
              handleFilterValueClick(key, externalLeadsFilterId),
            isActive: formState[externalLeadsFilterId]?.includes(option.id),
          })) || []
      )
    }

    const getSelectedFilterOptions = (
      externalLeadsFilterId: ExternalLeadsFilterIdEnum,
      formState: ExternalLeadsFilterFormState,
      handleFilterValueClick: (
        key: string,
        filterId: ExternalLeadsFilterIdEnum
      ) => void
    ) => {
      return (formState[externalLeadsFilterId] || []).map((value: string) => ({
        key: value,
        label: value,
        onClick: (key: string) =>
          handleFilterValueClick(key, externalLeadsFilterId),
        isActive: true,
      }))
    }

    return {
      handleAutocompleteChange,
      autocompleteResults,
      getCurrentSearchOptionsNotSelected,
      getSelectedFilterOptions,
    }
  }
