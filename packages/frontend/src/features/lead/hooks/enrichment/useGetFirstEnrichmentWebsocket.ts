import { useEffect } from 'react'

import { NameEvent } from '@getheroes/shared'
import { useWebSocketMessage } from '@internals/hooks/websockets/useWebSocketMessage'
import { useIntercom } from '@internals/providers/Intercom/useIntercom'
import { useAppDispatch } from '@internals/store/store'
import { IntercomEvents } from '@internals/utils/intercom/IntercomEvents.enum'

/**
 * Subscribes to WebSocket messages for the first enrichment launched by the user
 * @function useGetFirstEnrichmentWebsocket
 */
export const useGetFirstEnrichmentWebsocket = () => {
  const dispatch = useAppDispatch()
  const { lastMessage } = useWebSocketMessage(NameEvent.FIRST_ENRICHMENT)
  const intercom = useIntercom()

  useEffect(() => {
    if (!lastMessage) {
      return
    }

    intercom?.trackEvent(IntercomEvents.FIRST_LEAD_ENRICHED)
  }, [dispatch, intercom, lastMessage])
}
