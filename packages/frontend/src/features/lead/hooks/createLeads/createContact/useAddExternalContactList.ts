import { useCallback } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type {
  ExternalContact,
  LeadSource,
  Organization,
} from '@getheroes/shared'
import { useCreateLeadContactsBulkMutation } from '@internals/features/lead/api/contactApi'
import { createContactPayload } from '@internals/features/lead/utils/createContactPayload'
import { useTypedSelector } from '@internals/store/store'

interface HandleAddExternalContactListArgs {
  contactList: ExternalContact[]
  source: LeadSource
}

type UseAddExternalContactListReturn = [
  (
    args: HandleAddExternalContactListArgs
  ) => Promise<{ data?: any; error?: any }>,
  { isLoading: boolean },
]

/**
 * Handles the creation of external contacts
 * @returns [handleAddExternalContactList, { isLoading }]
 */
export const useAddExternalContactList =
  (): UseAddExternalContactListReturn => {
    const { id: organizationId } = useTypedSelector(
      selectCurrentUserOrganization
    ) as Organization

    const [createContactsBulkMutation, { isLoading }] =
      useCreateLeadContactsBulkMutation()

    const handleAddExternalContactList = useCallback(
      async ({ contactList, source }: HandleAddExternalContactListArgs) => {
        // 1 - Format the contacts to create
        const formattedContactListToCreate = contactList.map(
          ({ company, archived, sourceMetaInfoFilters, id, ...lead }) => ({
            phones: lead.phones?.filter(Boolean) ?? [],
            emails: lead.emails?.filter(Boolean) ?? [],
            companyName: company?.name ?? '',
            companyDomain: company?.domain ?? '',
            title: lead.title,
            linkedinUrl: lead.linkedinUrl,
            ...lead,
          })
        )

        // 2 - Create the contacts
        const contactPayload = await createContactPayload(
          formattedContactListToCreate
        )

        return createContactsBulkMutation({
          contacts: contactPayload,
          organizationId,
        })
      },
      [createContactsBulkMutation, organizationId]
    )

    return [handleAddExternalContactList, { isLoading }]
  }
