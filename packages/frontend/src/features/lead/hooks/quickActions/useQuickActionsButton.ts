import isEmpty from 'lodash/isEmpty'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ToastType, useToast } from '@getheroes/frontend/hooks'
import { TaskType } from '@getheroes/frontend/types'
import type { Contact, Organization } from '@getheroes/shared'
import { usePhoneContext } from '@internals/features/phone/providers/PhoneProvider/usePhoneContext'
import {
  useCreateTasksMutation,
  useLazyGetNextTaskByContactQuery,
} from '@internals/features/task/api/taskApi'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTypedSelector } from '@internals/store/store'

type UseLeadActionsButtonsProps = {
  navigate: (path: string) => void
}

type UseQuickActionsButtonReturnType = {
  handleTaskAction: (
    taskType: TaskType,
    contact: Contact,
    phoneNumberToCall?: string
  ) => void
  isActionLoading: boolean
}

/**
 *
 * @param navigate: is a function that allows to navigate to a new page
 * @returns {
 * handleTaskAction: function that creates a new task and navigates to it
 * isActionLoading: boolean that indicates if the action is loading
 * }
 */
export const useQuickActionsButton = ({
  // ⚠ DO NOT REMOVE navigate : is passed as a prop as the extension needs to open
  // a new tab instead of using internal navigation
  navigate,
}: UseLeadActionsButtonsProps): UseQuickActionsButtonReturnType => {
  const { createToast } = useToast()
  const { t } = useTranslation()

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const currentUser = useTypedSelector(selectCurrentUser)

  const [isActionLoading, setIsActionLoading] = useState(false)

  const { sendPhoneNumber } = usePhoneContext()
  const [createTask] = useCreateTasksMutation()
  const [getNextTaskByContact] = useLazyGetNextTaskByContactQuery()
  const { togglePhone } = usePhoneContext()

  const goToTask = useCallback(
    (taskType: TaskType, taskId: string, phoneNumber?: string) => {
      if (taskType === TaskType.CALL) {
        sendPhoneNumber(phoneNumber as string)
      }

      navigate(
        `${privateRoutes.tasks.path}/${taskId}/${taskType.toLowerCase()}`
      )

      if (taskType === TaskType.CALL) {
        togglePhone(true)
      }
    },
    [navigate, sendPhoneNumber, togglePhone]
  )

  const handleTaskAction = useCallback(
    async (taskType: TaskType, contact: Contact, phoneNumber?: string) => {
      setIsActionLoading(true)

      // DO NOT CREATE A NEW TASK
      // IF the contact already has one
      const { data: nextTaskContact } = await getNextTaskByContact({
        contactId: contact.id,
        organizationId,
      })
      if (!isEmpty(nextTaskContact)) {
        setIsActionLoading(false)
        goToTask(taskType, nextTaskContact.id, phoneNumber)
        return
      }

      // CREATE A NEW TASK
      try {
        const { successTaskIds } = await createTask({
          organizationId,
          tasks: [
            {
              type: taskType,
              contact: contact?.id,
              assign: currentUser?.id || '',
              dateOfExecution: new Date(),
              withTime: true,
            },
          ],
        }).unwrap()

        if (isEmpty(successTaskIds)) {
          throw new Error('Lead not assigned')
        }

        goToTask(taskType, successTaskIds[0], phoneNumber)
      } catch (e) {
        createToast({
          type: ToastType.ERROR,
          message: t(
            `An error occurred. It is not possible to ${
              taskType === TaskType.EMAIL ? 'email' : 'call'
            } this contact. Check if it is assigned.`
          ) as string,
        })
      }

      setIsActionLoading(false)
    },
    [
      createTask,
      createToast,
      currentUser?.id,
      getNextTaskByContact,
      goToTask,
      organizationId,
      t,
    ]
  )

  return useMemo(
    () => ({ handleTaskAction, isActionLoading }),
    [handleTaskAction, isActionLoading]
  )
}
