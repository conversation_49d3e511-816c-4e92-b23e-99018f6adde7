import * as yup from 'yup'

import { Gender, LeadSource } from '@getheroes/shared'

const createContactPayloadSchema = yup
  .object()
  .shape({
    externalId: yup.string().required(),
    firstName: yup.string().required(),
    lastName: yup.string().required(),
    emails: yup.array().of(yup.string().email()).required(),
    phones: yup.array().of(yup.string()).required(),
    title: yup.string().optional(),
    linkedinUrl: yup.string().url().optional(),
    source: yup.mixed().oneOf(Object.values(LeadSource)),
    sourceMeta: yup
      .object({
        id: yup.string().optional(),
        info: yup.string().optional(),
        createdAt: yup.date().optional(),
      })
      .optional(),
    city: yup.string().optional(),
    country: yup.string().optional(),
    gender: yup.mixed().oneOf(Object.values(Gender)),
    companyId: yup.string().optional(),
    companyName: yup.string().optional(),
  })
  .test(
    'company-required',
    'Either companyId or companyName is required',
    value => !!value.companyId || !!value.companyName
  )

const arrayCreateContactPayloadSchema = yup
  .array()
  .of(createContactPayloadSchema)

export type CreateVerifiedContactPayloadType = yup.InferType<
  typeof createContactPayloadSchema
>

type PayloadReturn<T> =
  T extends Array<unknown>
    ? Array<CreateVerifiedContactPayloadType>
    : CreateVerifiedContactPayloadType

/**
 * Processes a payload or array of payloads, stripping out the `sourceMeta` property
 * if it is either null, an empty object, or does not have meaningful content.
 * This operation is recursive when the payload is an array, applying the same logic to each element.
 *
 * @param {CreateVerifiedContactPayloadType | Array<CreateVerifiedContactPayloadType>} payload - The payload or array of payloads containing contact information.
 * @returns {CreateVerifiedContactPayloadType | Array<CreateVerifiedContactPayloadType>} The processed payload with empty `sourceMeta` properties removed.
 */
const stripEmptySourceMeta = (
  payload:
    | CreateVerifiedContactPayloadType
    | Array<CreateVerifiedContactPayloadType>
): typeof payload => {
  if (Array.isArray(payload)) {
    return payload.map(
      stripEmptySourceMeta
    ) as Array<CreateVerifiedContactPayloadType>
  }
  if (
    payload &&
    typeof payload === 'object' &&
    'sourceMeta' in payload &&
    (payload.sourceMeta == null ||
      (typeof payload.sourceMeta === 'object' &&
        Object.keys(payload.sourceMeta).length === 0))
  ) {
    const { sourceMeta, ...rest } = payload
    return rest
  }
  return payload
}

/**
 * Processes a raw payload and validates it using the appropriate schema,
 * ensuring it adheres to the create contact payload rules. It throws an error
 * if validation fails and returns the parsed payload with empty source metadata stripped.
 *
 * @param {unknown} rawPayload - The raw input payload to be validated and processed.
 * @return {Promise<CreateVerifiedContactPayloadType| Array<CreateVerifiedContactPayloadType>>} - The validated and processed payload, processed further to strip empty source metadata.
 */
export async function createContactPayload<T>(
  rawPayload: T
): Promise<PayloadReturn<T>> {
  const isArray = Array.isArray(rawPayload)
  const schema = isArray
    ? arrayCreateContactPayloadSchema
    : createContactPayloadSchema

  const parsedPayload = await schema.validate(rawPayload, {
    abortEarly: false,
  })

  if (parsedPayload === undefined) {
    throw new Error('Create contact payload validation failed')
  }

  return stripEmptySourceMeta(parsedPayload) as PayloadReturn<T>
}
