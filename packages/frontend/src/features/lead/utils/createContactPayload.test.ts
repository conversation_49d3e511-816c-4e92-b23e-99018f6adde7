import { describe, it, expect } from 'vitest'
import { createContactPayload } from './createContactPayload'
import { Gender, LeadSource } from '@getheroes/shared'

describe('createContactPayload', () => {
  const validPayload = {
    externalId: '123',
    firstName: '<PERSON>',
    lastName: 'Doe',
    emails: ['<EMAIL>'],
    phones: ['+*********'],
    source: LeadSource.CSV,
    sourceMeta: { id: 'src1', info: 'info', createdAt: new Date() },
    gender: Gender.MALE,
    companyId: 'company-1',
  }

  it('validates a correct payload', async () => {
    const result = await createContactPayload(validPayload)
    expect(result).toMatchObject(validPayload)
  })

  it('fails when required fields are missing', async () => {
    const invalidPayload = { ...validPayload }
    delete invalidPayload.externalId

    await expect(createContactPayload(invalidPayload)).rejects.toThrow()
  })

  it('fails when emails are invalid', async () => {
    const payload = { ...validPayload, emails: ['not-an-email'] }
    await expect(createContactPayload(payload)).rejects.toThrow()
  })

  it('validates using companyName if companyId is missing', async () => {
    const payload = { ...validPayload }
    delete payload.companyId
    payload.companyName = 'Test Co'

    const result = await createContactPayload(payload)
    expect(result.companyName).toBe('Test Co')
  })

  it('fails when both companyId and companyName are missing', async () => {
    const payload = { ...validPayload }
    delete payload.companyId
    delete payload.companyName

    await expect(createContactPayload(payload)).rejects.toThrow(
      /Either companyId or companyName is required/
    )
  })

  it('validates an array of payloads', async () => {
    const arrayPayload = [
      validPayload,
      { ...validPayload, externalId: '456', emails: ['<EMAIL>'] },
    ]
    const result = await createContactPayload(arrayPayload)
    expect(Array.isArray(result)).toBe(true)
    expect(result.length).toBe(2)
  })
})
