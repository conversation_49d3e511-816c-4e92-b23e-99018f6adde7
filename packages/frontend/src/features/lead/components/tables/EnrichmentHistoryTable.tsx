import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ClipLoader } from 'react-spinners'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { formatWithDistanceIfDateIsToday } from '@getheroes/frontend/utils'
import type { Enrichment, User } from '@getheroes/shared'
import { EnrichmentStatus, EnrichmentType } from '@getheroes/shared'
import { Icon, Tag, Tooltip } from '@getheroes/ui'
import { CreditConsumedTag } from '@internals/components/business/organization/credit/CreditConsumedTag/CreditConsumedTag'
import { Grid } from '@internals/components/common/dataDisplay/Grid/Grid'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { UserNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/UserNameCell'
import { WrapperCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/WrapperCell'
import type { GridColDef } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { AutoSizeStrategyEnum } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { useListEnrichmentsQuery } from '@internals/features/lead/api/enrichmentApi'
import { ArchiveUnenrichableContactsModal } from '@internals/features/lead/components/Enrichment/ArchiveUnenrichableContactsModal/ArchiveUnenrichableContactsModal'
import {
  ArchiveUnenrichableContactsModalContextEnum,
  ArchiveUnenrichableContactsModalStepEnum,
} from '@internals/features/lead/components/Enrichment/ArchiveUnenrichableContactsModal/ArchiveUnenrichableContactsModalsProvider-export'
import { useArchiveUnenrichableContactsModalsContext } from '@internals/features/lead/components/Enrichment/ArchiveUnenrichableContactsModal/useArchiveUnenrichableContactsModalsContext'
import type { ListEnrichmentsQueryType } from '@internals/features/lead/types/enrichType'
import { getStateColumnToLocalStorage } from '@internals/features/lead/utils/columns/contact/getContactColumn'
import { useTypedSelector } from '@internals/store/store'

export const EnrichmentHistoryTable = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { setModalStep, setModalContext } =
    useArchiveUnenrichableContactsModalsContext()
  const [enrichmentClicked, setEnrichmentClicked] = useState<Enrichment | null>(
    null
  )
  const { currentPage, pageSize, name } = useGridContext()
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const { data, isLoading } = useListEnrichmentsQuery<ListEnrichmentsQueryType>(
    {
      organizationId: organization?.id as string,
      limitPerPage: pageSize,
      page: currentPage,
    }
  )
  const enrichmentHistory = useMemo(() => data?.items || [], [data?.items])
  const pagination = data?.meta || {}

  const { t, i18n } = useTranslation(['lead', 'template'])
  const columns = useMemo(() => {
    return [
      {
        field: 'updatedAt',
        headerName: t('Date'),
        suppressMovable: true,
        resizable: false,
        ...getStateColumnToLocalStorage(name, 'updatedAt'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          return (
            <WrapperCell data={data.updatedAt}>
              {formatWithDistanceIfDateIsToday(new Date(data.updatedAt), i18n)}
            </WrapperCell>
          )
        },
      },
      {
        field: 'createdBy',
        headerName: t('Initiated'),
        suppressMovable: true,
        resizable: false,
        ...getStateColumnToLocalStorage(name, 'createdBy'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          return (
            <WrapperCell data={data}>
              <Tooltip content={t('Email enrichment only')}>
                <UserNameCell user={data.createdBy as unknown as User} />
              </Tooltip>
            </WrapperCell>
          )
        },
      },
      {
        field: 'type',
        headerName: t('Type', { ns: 'template' }),
        suppressMovable: true,
        resizable: false,
        ...getStateColumnToLocalStorage(name, 'type'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          switch (data.type) {
            case EnrichmentType.EMAIL:
              return (
                <WrapperCell data={data}>
                  <Tooltip content={t('Email enrichment only')}>
                    <Icon name={'Mail'} size={'small'} />
                  </Tooltip>
                </WrapperCell>
              )

            case EnrichmentType.PHONE:
              return (
                <WrapperCell data={data}>
                  <Tooltip content={t('Phone enrichment only')}>
                    <Icon name={'Phone'} size={'small'} />
                  </Tooltip>
                </WrapperCell>
              )

            case EnrichmentType.ADVANCED:
              return (
                <WrapperCell data={data}>
                  <Tooltip content={t('Email and phone enrichment')}>
                    <div className="flex items-center gap-2">
                      <Icon name={'Mail'} size={'small'} />
                      <Icon name={'Phone'} size={'small'} />
                    </div>
                  </Tooltip>
                </WrapperCell>
              )

            default:
              return null
          }
        },
      },
      {
        field: 'status',
        headerName: t('Status'),
        resizable: false,
        suppressMovable: true,
        ...getStateColumnToLocalStorage(name, 'status'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          switch (data.status) {
            case EnrichmentStatus.COMPLETED:
              return (
                <WrapperCell data={data}>
                  <Tag label={t('Completed')} color={'green'} size={'small'} />
                </WrapperCell>
              )
            case EnrichmentStatus.FAILED:
              return (
                <WrapperCell data={data}>
                  <Tag label={t('Failed')} color={'red'} size={'small'} />
                </WrapperCell>
              )
            case EnrichmentStatus.IN_PROGRESS:
              return (
                <WrapperCell data={data}>
                  <Tag
                    label={t('In progress...')}
                    color={'orange'}
                    size={'small'}
                  />
                </WrapperCell>
              )
            default:
              return <></>
          }
        },
      },
      {
        field: 'creditUsed',
        resizable: false,
        suppressMovable: true,
        headerName: t('Credit consumed'),
        ...getStateColumnToLocalStorage(name, 'creditUsed'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          const isError = data.status === EnrichmentStatus.FAILED
          if (isError) {
            return (
              <WrapperCell data={data}>
                <>-</>
              </WrapperCell>
            )
          } else {
            return (
              <WrapperCell data={data}>
                <CreditConsumedTag creditCount={data.creditUsed} />
              </WrapperCell>
            )
          }
        },
      },
      {
        field: 'nbEnrichments',
        suppressMovable: true,
        resizable: false,
        headerName: t('Total enriched'),
        ...getStateColumnToLocalStorage(name, 'nbEnrichments'),
        cellRenderer: ({ data }: { data: Enrichment }) => {
          const status = data.status
          const totalEnriched = data.nbEnrichments + data.nbEnrichmentsPartial
          const label = t('lead count', { count: totalEnriched })
          switch (status) {
            case EnrichmentStatus.IN_PROGRESS:
              return (
                <WrapperCell data={data}>
                  <ClipLoader size={25} color={'var(--borderAccent)'} />
                </WrapperCell>
              )
            case EnrichmentStatus.FAILED:
              return <WrapperCell data={data}>-</WrapperCell>
            default: {
              return <WrapperCell data={data}>{label}</WrapperCell>
            }
          }
        },
      },
    ]
  }, [i18n, name, t])

  return (
    <>
      <Grid
        name={'enrichments-history-table'}
        data={enrichmentHistory}
        columns={columns as GridColDef[]}
        paginationMeta={pagination}
        isLoading={isLoading}
        onRowClicked={(data: Enrichment) => {
          const enrichment = data as Enrichment
          setIsModalOpen(true)
          setModalStep(
            ArchiveUnenrichableContactsModalStepEnum.SUMMARY_BEFORE_ARCHIVE
          )
          setModalContext(
            ArchiveUnenrichableContactsModalContextEnum.TABLE_ROW_CLICKED
          )
          setEnrichmentClicked(enrichment)
        }}
        suppressDragLeaveHidesColumns
        hasWindowResize
        saveToLocalStorage
        autoSizeColumnStrategy={{
          type: AutoSizeStrategyEnum.FILL_GRID_WIDTH,
        }}
      />

      <ArchiveUnenrichableContactsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        enrichment={enrichmentClicked}
      />
    </>
  )
}
