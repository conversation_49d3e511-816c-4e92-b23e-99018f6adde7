import { clsx } from 'clsx'
import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import type { LeadCategory } from '@getheroes/shared'
import { Button } from '@getheroes/ui'
import { CommandIcon } from '@internals/assets/svg/icons/CommandIcon'
import { FloatingSearch } from '@internals/components/common/navigation/FloatingSearch/FloatingSearch'
import { SegmentedTabulation } from '@internals/components/common/navigation/SegmentedTabulation/SegmentedTabulation'
import { useManageFloatingSearchUI } from '@internals/features/lead/components/FloatingSearchLeads/useManageFloatingSearchUI'
import { useFloatingSearchLeads } from '@internals/features/lead/hooks/searchLeads/useFloatingSearchLeads'

type FloatingSearchLeadsProps = {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  onClickCreateCallback: (floatingSearchLeadCategory: LeadCategory) => void
  currentLeadCategoryTable: LeadCategory
  hotKey?: string
}

export const FloatingSearchLeads: (
  props: FloatingSearchLeadsProps
) => JSX.Element = ({
  isOpen,
  setIsOpen,
  hotKey,
  onClickCreateCallback,
  currentLeadCategoryTable,
}) => {
  const [currentSearch, setCurrentSearch] = useState<string>('')
  const [floatingSearchLeadCategory, setFloatingSearchLeadCategory] =
    useState<LeadCategory>(currentLeadCategoryTable)

  const { t } = useTranslation('lead')

  const {
    leadsWithJSXCollection,
    isLoading,
    isEmptyCollection,
    handleSearchleads,
  } = useFloatingSearchLeads({ floatingSearchLeadCategory })

  useEffect(() => {
    if (isOpen) {
      handleSearchleads(currentSearch)
    }
  }, [currentSearch, handleSearchleads, isOpen])

  const { leadCategoryTabOptions } = useManageFloatingSearchUI({
    floatingSearchLeadCategory,
  })

  return (
    <FloatingSearch
      name={'lead-search'}
      results={leadsWithJSXCollection}
      isLoading={isLoading}
      className={clsx('max-w-xl')}
      onChange={event => {
        const value = event.target.value
        setCurrentSearch(value)
        handleSearchleads(value)
      }}
      setIsOpen={setIsOpen}
      isOpen={isOpen}
      endIcon={
        <div
          className={clsx(
            'flex items-center justify-center text-textDefault text-white bg-backgroundInverse p-2 rounded-m'
          )}
        >
          <CommandIcon />
          <span className={clsx('ml-1')}>{hotKey}</span>
        </div>
      }
      placeholder={
        t(
          "Before creating a lead, please make sure it doesn't exist..."
        ) as string
      }
      header={
        <SegmentedTabulation
          className={clsx('px-3 w-min mb-6')}
          items={leadCategoryTabOptions}
          onClickTab={key => {
            if (floatingSearchLeadCategory !== key) {
              setFloatingSearchLeadCategory(key as LeadCategory)
            }
          }}
        />
      }
      footer={
        <Button
          variant={'tertiary-outlined'}
          onClick={() => {
            setIsOpen(false)
            onClickCreateCallback(floatingSearchLeadCategory)
          }}
          iconLeft={'Plus'}
        >
          {t('Create {{leadCategory}}', {
            leadCategory: floatingSearchLeadCategory.toLowerCase(),
          })}
        </Button>
      }
      hotKey={hotKey}
      isEmptyCollection={isEmptyCollection}
      emptyCollectionMessage={
        t("There's no {{leadCategory}} named like this in your org. database", {
          leadCategory: t(floatingSearchLeadCategory.toLowerCase()),
        }) as string
      }
    />
  )
}
