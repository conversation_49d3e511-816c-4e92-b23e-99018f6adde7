import * as yup from 'yup'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { LeadStatus } from '@getheroes/shared'

export enum ModalLeadBulkEditAction {
  STATUS = ExclusiveContactLeadFieldEnum.STATUS,
  LABELS = ExclusiveContactLeadFieldEnum.LABELS,
}

export const schema = yup.object({
  contactIds: yup.array().of(yup.string().required()).required(),
  status: yup.mixed<LeadStatus>(),
  labels: yup.object().shape({
    add: yup.array().of(yup.string().required()).required(),
    remove: yup.array().of(yup.string().required()).required(),
  }),
})
