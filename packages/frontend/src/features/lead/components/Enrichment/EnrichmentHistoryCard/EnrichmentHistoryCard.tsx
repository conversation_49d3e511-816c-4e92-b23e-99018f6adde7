import { useTranslation } from 'react-i18next'

import { Card, Typography } from '@getheroes/ui'
import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { EnrichmentHistoryTable } from '@internals/features/lead/components/tables/EnrichmentHistoryTable'

export const EnrichmentHistoryCard = () => {
  const { t } = useTranslation('lead')

  return (
    <GridProvider name={'enrichments-history-table'}>
      <div className="w-full">
        <Card
          isCondense
          fullHeight
          header={
            <Typography variant="heading" size="s">
              {t('Enrichment history', { ns: 'lead' })}
            </Typography>
          }
        >
          <EnrichmentHistoryTable />
        </Card>
      </div>
    </GridProvider>
  )
}
