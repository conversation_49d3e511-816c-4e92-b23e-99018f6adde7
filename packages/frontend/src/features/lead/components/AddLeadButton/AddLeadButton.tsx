import { useEffect, useState } from 'react'

import type { LeadCategory } from '@getheroes/shared'
import { IconButton } from '@getheroes/ui'
import { DropdownMenu } from '@internals/components/common/navigation/DropdownMenu/DropdownMenu'
import { CreateLeadsModals } from '@internals/features/lead/components/LeadsModals/CreateLeadsModals/CreateLeadsModals'
import { useLeadDataImport } from '@internals/features/leadImport/Modules/CsvModule/hooks/useLeadDataImport'
import { useLeadsModalsImport } from '@internals/features/leadImport/Modules/CsvModule/hooks/useLeadsModalsImport'
import { idReferentials } from '@internals/utils/idReferentials'

export const AddLeadButton = (): JSX.Element | null => {
  const [isFileImportOpen, setIsFileImportOpen] = useState(false)
  const [leadCategory, setLeadCategory] = useState<LeadCategory | null>(null)
  const { state, openModal, closeModal } = useLeadsModalsImport()

  const addLeadDropdownMenuItems = useLeadDataImport(
    openModal,
    setLeadCategory,
    setIsFileImportOpen
  )

  useEffect(() => {
    if (!isFileImportOpen) {
      setLeadCategory(null)
    }
  }, [isFileImportOpen])

  return (
    <>
      <DropdownMenu
        dataTestId={idReferentials.leads.components.AddLeadButton.dropdown}
        items={addLeadDropdownMenuItems}
        openedButton={<IconButton icon={'Plus'} variant={'primary'} />}
        closedButton={<IconButton icon={'Xmark'} variant={'primary'} />}
      />
      <CreateLeadsModals
        isFileImportOpen={isFileImportOpen}
        state={state}
        closeModal={closeModal}
        leadCategory={leadCategory}
        setIsFileImportOpen={setIsFileImportOpen}
      />
    </>
  )
}
