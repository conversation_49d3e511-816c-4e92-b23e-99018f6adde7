import type {
  CompanyLeadFieldEnum,
  ContactLeadFieldEnum,
} from '@getheroes/frontend/types'
import type { LeadFieldFormat } from '@internals/features/lead/types/leadFieldFormat'

export enum KindLeadEnum {
  STANDARD = 'standard',
  CUSTOM = 'custom',
}

export enum TypeLeadEnum {
  STRING = 'string',
  STRING_ARRAY = 'string[]',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
}

export interface LeadsFieldResponseType {
  id: ContactLeadFieldEnum | CompanyLeadFieldEnum
  name: ContactLeadFieldEnum | CompanyLeadFieldEnum
  sortable: boolean
  filterable: boolean
  editable: boolean
  kind: KindLeadEnum
  format: LeadFieldFormat | undefined
  type: TypeLeadEnum
  jsonPath: string
}

export interface LeadsField extends LeadsFieldResponseType {
  isActive: boolean
  isDisabled: boolean
  isHidden: boolean
  excludeFromSidePanel: boolean
  excludeFromFilters: boolean
  excludeFromColumnsToggle: boolean
  notTransferableFilters: boolean
  isEnrichment: boolean
}
