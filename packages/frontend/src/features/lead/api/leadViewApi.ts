import { api } from '@getheroes/frontend/config/api'
import i18n from '@internals/config/i18n'
import type {
  CreateLeadViewByIdRequestType,
  DeleteLeadViewByIdRequestType,
  GetLeadViewByIdRequestType,
  GetLeadViewByIdResponseType,
  GetLeadViewsRequestType,
  GetViewsResponseType,
  UpdateLeadViewByIdRequestType,
} from '@internals/features/lead/types/leadViewType'

export const leadViewApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      getLeadViews: builder.query<
        GetViewsResponseType,
        GetLeadViewsRequestType
      >({
        query: ({
          organizationId,
          order,
          orderBy,
          page,
          limitPerPage = 100,
        }) => {
          const params = new URLSearchParams()
          if (order) {
            params.append('order', order)
          }
          if (orderBy) {
            params.append('orderBy', orderBy)
          }
          if (page) {
            params.append('page', page.toString())
          }
          if (limitPerPage) {
            params.append('limitPerPage', limitPerPage.toString())
          }
          return {
            url: `${organizationId}/leads/views${
              params.toString() ? `?${params.toString()}` : ''
            }`,
            method: 'GET',
          }
        },
        providesTags: result => {
          if (!Array.isArray(result)) {
            return [{ type: 'LeadView' as const, id: 'LIST' }]
          }
          return [
            ...result.map(({ id }) => ({ type: 'Template', id }) as const),
            { type: 'LeadView' as const, id: 'LIST' },
          ]
        },
        transformResponse: (data: GetViewsResponseType) => {
          const dataMapped = data.items.map(view => ({
            ...view,
            filters: view.filters ?? [],
          }))
          return {
            items: dataMapped,
            meta: data.meta,
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the views',
            { ns: 'lead' }
          )
          return { message: errorMsg }
        },
      }),
      getLeadViewById: builder.query<
        GetLeadViewByIdResponseType,
        GetLeadViewByIdRequestType
      >({
        query: ({ organizationId, viewId }) => {
          return {
            url: `${organizationId}/leads/views/${viewId}`,
            method: 'GET',
          }
        },
        providesTags: () => [{ type: 'LeadView' as const, id: 'BY_ID' }],
        transformResponse: (view: GetLeadViewByIdResponseType) => {
          return {
            ...view,
            filters: view.filters ?? [],
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the view',
            { ns: 'lead' }
          )
          return { message: errorMsg }
        },
      }),
      updateLeadViewById: builder.mutation<
        GetLeadViewByIdResponseType,
        UpdateLeadViewByIdRequestType
      >({
        query: ({ organizationId, viewId, ...payload }) => {
          return {
            url: `${organizationId}/leads/views/${viewId}`,
            method: 'PATCH',
            body: payload,
          }
        },
        invalidatesTags: [
          { type: 'LeadView' as const, id: 'BY_ID' },
          { type: 'LeadView' as const, id: 'LIST' },
        ],
        transformErrorResponse: error => {
          switch (error.status) {
            case 401:
            case 403:
              return {
                message: i18n.t('You are not authorized to update this view', {
                  ns: 'lead',
                }),
              }
            case 409:
              return {
                message: i18n.t('The name of the view is already used', {
                  ns: 'lead',
                }),
              }
            default:
              return {
                message: i18n.t(
                  'An error occurred. Please check the values of the view and try again',
                  { ns: 'lead' }
                ),
              }
          }
        },
      }),
      createLeadView: builder.mutation<
        GetLeadViewByIdResponseType,
        CreateLeadViewByIdRequestType
      >({
        query: ({ organizationId, ...payload }) => {
          return {
            url: `${organizationId}/leads/views`,
            method: 'POST',
            body: payload,
          }
        },
        invalidatesTags: [
          { type: 'LeadView' as const, id: 'BY_ID' },
          { type: 'LeadView' as const, id: 'LIST' },
        ],
        transformErrorResponse: error => {
          switch (error.status) {
            case 401:
            case 403:
              return {
                message: i18n.t('You are not authorized to create this view', {
                  ns: 'lead',
                }),
              }
            case 409:
              return {
                message: i18n.t('The name of the view is already used', {
                  ns: 'lead',
                }),
              }
            default:
              return {
                message: i18n.t(
                  'An error occurred. Please check the values of the view and try again',
                  { ns: 'lead' }
                ),
              }
          }
        },
      }),
      deleteViewById: builder.mutation<null, DeleteLeadViewByIdRequestType>({
        query: ({ organizationId, viewId }) => {
          return {
            url: `${organizationId}/leads/views/${viewId}`,
            method: 'DELETE',
          }
        },
        invalidatesTags: [
          { type: 'LeadView' as const, id: 'BY_ID' },
          { type: 'LeadView' as const, id: 'LIST' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while creating this view',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
    }
  },
})

export const {
  useGetLeadViewsQuery,
  useLazyGetLeadViewsQuery,
  useGetLeadViewByIdQuery,
  useLazyGetLeadViewByIdQuery,
  useUpdateLeadViewByIdMutation,
  useCreateLeadViewMutation,
} = leadViewApi
