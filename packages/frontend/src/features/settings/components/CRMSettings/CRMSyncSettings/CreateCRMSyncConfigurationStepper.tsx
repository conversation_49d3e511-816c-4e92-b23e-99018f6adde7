import React, { useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { ToastType, useToast } from '@getheroes/frontend/hooks'
import { getZeliqBlogAddress } from '@getheroes/frontend/utils'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Helper, Stepper } from '@getheroes/ui'
import { ContactsLists } from '@internals/features/settings/components/CRMSettings/CRMSyncSettings/ContactsLists/ContactsLists'
import { ExclusionRulesOptions } from '@internals/features/settings/components/CRMSettings/CRMSyncSettings/ExclusionRules/ExclusionRulesOptions/ExclusionRulesOptions'
import { MappingsTab } from '@internals/features/settings/components/CRMSettings/CRMSyncSettings/Mappings/MappingsTab'
import { PushSettings } from '@internals/features/settings/components/CRMSettings/CRMSyncSettings/PushSettings/PushSettings'
import { useGlobalSyncSettingsContext } from '@internals/features/settings/hooks/CRM/GlobalSyncSettingsProvider/useGlobalSyncSettingsContext'
import { useSubmitGlobalSyncConfiguration } from '@internals/features/settings/hooks/CRM/submitConfiguration/useSubmitGlobalSyncConfiguration'
import { GlobalSyncSettingsStep } from '@internals/features/settings/types/CRMSettingsType'
import { idReferentials } from '@internals/utils/idReferentials'

type CreateCRMSyncConfigurationStepperPropsType = {
  setIsOpen: (isOpen: boolean) => void
}

export const CreateCRMSyncConfigurationStepper: (
  props: CreateCRMSyncConfigurationStepperPropsType
) => JSX.Element = ({ setIsOpen }) => {
  /* Vars */

  const { t, i18n } = useTranslation('settings')
  const { createToast } = useToast()
  const {
    steps,
    currentStep,
    setCurrentStep,
    isCurrentStepValid,
    isErrorsDisplay,
    setIsErrorsDisplay,
  } = useGlobalSyncSettingsContext()

  /* Queries */

  const { handleGlobalSyncConfigSubmitted, isError, isLoading } =
    useSubmitGlobalSyncConfiguration()

  /* Effects */

  useEffect(() => {
    if (isError) {
      createToast({
        type: ToastType.ERROR,
        message: t('An error occurred, please try again later'),
      })
    }
  }, [createToast, isError, t])

  /* Memos */

  const zeliqBlogAddress = useMemo(() => {
    return getZeliqBlogAddress(i18n.language)
  }, [i18n.language])

  return (
    <div className={'flex gap-4 h-full overflow-hidden'}>
      <div className={'flex flex-col justify-between w-1/5'}>
        <Stepper
          steps={steps}
          value={currentStep}
          onChange={step => {
            setCurrentStep(step)
          }}
          orientation={'vertical'}
        />
        <Helper
          icon="QuestionMark"
          orientation={'vertical'}
          title={t('Feeling lost?')}
          description={t(
            'Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.'
          )}
          endComponent={
            <a href={zeliqBlogAddress} target="_blank" rel="noreferrer">
              <Button
                dataTestId={idReferentials.common.dataDisplay.helper.button}
                size={'small'}
              >
                {t('Visit help center')}
              </Button>
            </a>
          }
        />
      </div>
      <Divider orientation={'vertical'} />
      <div className={'flex-1 flex flex-col gap-4 overflow-y-auto'}>
        {currentStep === GlobalSyncSettingsStep.SYNC_TYPE && <ContactsLists />}
        {currentStep === GlobalSyncSettingsStep.PUSH_DATA && <PushSettings />}
        {currentStep === GlobalSyncSettingsStep.MAPPINGS && <MappingsTab />}
        {currentStep === GlobalSyncSettingsStep.ADD_EXCLUSION_RULES && (
          <ExclusionRulesOptions />
        )}
        <div className={'flex items-center gap-3 justify-end mr-16'}>
          <Button
            iconLeft={'NavArrowLeft'}
            disabled={currentStep === GlobalSyncSettingsStep.SYNC_TYPE}
            onClick={() => {
              const currentStepIndex = steps.findIndex(
                step => step.value === currentStep
              )
              const newStepIndex = Math.max(currentStepIndex - 1, 0)
              setCurrentStep(steps[newStepIndex].value)
            }}
          >
            {t('Previous')}
          </Button>
          <Button
            iconRight={'NavArrowRight'}
            variant={'primary'}
            onClick={() => {
              if (!isCurrentStepValid) {
                setIsErrorsDisplay(true)
                return
              }

              if (currentStep === GlobalSyncSettingsStep.ADD_EXCLUSION_RULES) {
                handleGlobalSyncConfigSubmitted(() => setIsOpen(false))
                return
              }

              const currentStepIndex = steps.findIndex(
                step => step.value === currentStep
              )
              const newStepIndex = Math.min(
                currentStepIndex + 1,
                steps.length - 1
              )
              setCurrentStep(steps[newStepIndex].value)
            }}
            disabled={isErrorsDisplay || !isCurrentStepValid}
            loading={isLoading}
          >
            {currentStep === GlobalSyncSettingsStep.ADD_EXCLUSION_RULES
              ? t('Launch sync')
              : t('Next')}
          </Button>
        </div>
      </div>
    </div>
  )
}
