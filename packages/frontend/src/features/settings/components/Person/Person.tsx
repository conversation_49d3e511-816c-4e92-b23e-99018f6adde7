import { Avatar } from '@getheroes/ui'
import {
  getInitialsFromPerson,
  getPersonFullName,
} from '@internals/utils/string'

export type PersonProps = {
  firstName: string
  lastName: string
}

export const Person = ({ firstName, lastName }: PersonProps) => {
  return (
    <div className="mx-2 gap-1 flex items-center">
      <Avatar
        color="light"
        initials={getInitialsFromPerson({ firstName, lastName })}
      />
      <span className="body-s-medium">
        {getPersonFullName({ firstName, lastName })}
      </span>
    </div>
  )
}
