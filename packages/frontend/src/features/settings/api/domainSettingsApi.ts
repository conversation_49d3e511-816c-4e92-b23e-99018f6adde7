import { api } from '@getheroes/frontend/config/api'
import type { DnsSettingsType } from '@internals/features/settings/types/DnsSettingsType'
import type { DomainSettingsType } from '@internals/features/settings/types/DomainSettingsType'
import type { Domain } from '@internals/models/domain'

export const domainSettingsApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      getAllowedDomain: builder.query<Domain[], { organizationId: string }>({
        query: ({ organizationId }) => {
          return `${organizationId}/allowed/domains`
        },
        providesTags: () => [{ type: 'Domain', id: 'LIST' }],
      }),
      getDnsSettings: builder.query<
        DnsSettingsType,
        { organizationId: string; domainName: string }
      >({
        query: ({ organizationId, domainName }) => {
          return `${organizationId}/mailer/settings/dns?domain=${domainName}`
        },
      }),
      getSettings: builder.query<
        DomainSettingsType,
        { organizationId: string; domainId: string }
      >({
        query: ({ organizationId, domainId }) => {
          return `${organizationId}/mailer/settings/${domainId}`
        },
      }),
      patchSettings: builder.mutation<
        DomainSettingsType,
        {
          organizationId: string
          domainId: string
          settings: Partial<DomainSettingsType>
        }
      >({
        query: ({ organizationId, domainId, settings }) => {
          const rampupMode = settings.rampupMode
            ? {
                enabled: settings.rampupMode.enabled,
                rampupValue: settings.rampupMode.rampupValue,
                initialValue: settings.rampupMode.initialValue,
              }
            : undefined

          const newSettings = {
            ...settings,
            rampupMode,
          }
          return {
            url: `${organizationId}/mailer/settings/${domainId}`,
            method: 'PATCH',
            body: newSettings,
          }
        },
      }),
      deleteDomain: builder.mutation<
        DomainSettingsType,
        {
          organizationId: string
          domainId: string
        }
      >({
        query: ({ organizationId, domainId }) => {
          return {
            url: `${organizationId}/allowed/domains/${domainId}`,
            method: 'DELETE',
          }
        },
        invalidatesTags: [
          { type: 'Domain', id: 'LIST' },
          { type: 'Integration', id: 'AUTHORIZATION' },
        ],
      }),
    }
  },
})

export const {
  useGetAllowedDomainQuery,
  useGetDnsSettingsQuery,
  useGetSettingsQuery,
  usePatchSettingsMutation,
  useDeleteDomainMutation,
} = domainSettingsApi

export type UsePatchSettingsMutationType = ReturnType<
  typeof usePatchSettingsMutation
>
