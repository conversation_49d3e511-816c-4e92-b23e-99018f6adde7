import { api } from '@getheroes/frontend/config/api'
import {
  setUser,
  setUserSettings,
} from '@getheroes/frontend/config/store/slices/authSlice'
import type { User, UserSettings } from '@getheroes/shared'
import {
  updateUserOptimistic,
  updateUserSettingsOptimistic,
} from '@internals/features/settings/api/optimistic/userOptimistic'

export const userApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      getUser: builder.query<User, { organizationId: string }>({
        query: ({ organizationId }) => {
          return {
            url: `${organizationId}/users`,
            method: 'GET',
          }
        },
        providesTags: () => [{ type: 'User', id: 'GET_CURRENT_USER' }],
      }),
      updateUser: builder.mutation<
        User,
        {
          user: Partial<User>
        }
      >({
        query: ({ user }) => {
          return {
            url: '/users',
            method: 'PATCH',
            body: user,
          }
        },
        async onQueryStarted(
          { ...attributeAtUpdate },
          { dispatch, queryFulfilled, getState }
        ) {
          updateUserOptimistic({
            attributeAtUpdate: attributeAtUpdate.user,
            dispatch,
            queryFulfilled,
            getState,
          })
          dispatch(setUser(attributeAtUpdate.user))
        },
      }),
      updateUserSettings: builder.mutation<
        UserSettings,
        {
          userSettings: Partial<UserSettings>
          organizationId: string
        }
      >({
        query: ({ userSettings, organizationId }) => {
          return {
            url: `${organizationId}/user-settings`,
            method: 'PATCH',
            body: userSettings,
          }
        },
        async onQueryStarted(
          { organizationId, ...attributeAtUpdate },
          { dispatch, queryFulfilled, getState }
        ) {
          updateUserSettingsOptimistic({
            attributeAtUpdate: attributeAtUpdate.userSettings,
            dispatch,
            queryFulfilled,
            getState,
          })
          dispatch(setUserSettings(attributeAtUpdate.userSettings))
        },
      }),
      checkDomain: builder.mutation<User, void>({
        query: () => {
          return {
            url: '/users/me/check-domain',
            method: 'POST',
            body: {},
          }
        },
      }),
      sendVerificationCode: builder.mutation<
        User,
        {
          phoneNumber: string
        }
      >({
        query: ({ phoneNumber }) => {
          return {
            url: '/users/me/send-verification-code',
            method: 'POST',
            body: { phoneNumber },
          }
        },
      }),
      verifyCode: builder.mutation<
        User,
        {
          phoneNumber: string
          code: string
        }
      >({
        query: ({ phoneNumber, code }) => {
          return {
            url: '/users/me/verify-code',
            method: 'POST',
            body: { phoneNumber, code },
          }
        },
      }),
    }
  },
})

export const {
  useGetUserQuery,
  useLazyGetUserQuery,
  useUpdateUserMutation,
  useUpdateUserSettingsMutation,
  useCheckDomainMutation,
  useSendVerificationCodeMutation,
  useVerifyCodeMutation,
} = userApi
