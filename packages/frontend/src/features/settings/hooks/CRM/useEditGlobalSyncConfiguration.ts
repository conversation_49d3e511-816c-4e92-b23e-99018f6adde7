import { useCallback, useEffect } from 'react'

import { useGlobalSyncSettingsContext } from '@internals/features/settings/hooks/CRM/GlobalSyncSettingsProvider/useGlobalSyncSettingsContext'
import type {
  CurrentSyncConfigInProcess,
  GlobalSyncSettingsStep,
} from '@internals/features/settings/types/CRMSettingsType'
import type { SaveGlobalSyncConfigurationDefaultType } from '@internals/features/settings/types/IntegrationType'

import { useEditExclusionRules } from './useEditExclusionRules'
import { useEditMappings } from './useEditMapping'

export const useEditGlobalSyncConfiguration = (
  savedGlobalSyncConfiguration: SaveGlobalSyncConfigurationDefaultType,
  currentStep: GlobalSyncSettingsStep
) => {
  const { setCurrentGlobalSyncConfigInProcess, setCurrentStep } =
    useGlobalSyncSettingsContext()
  const { mapSavedExclusionRulesToExclusionRulesInProcess } =
    useEditExclusionRules()
  const { mapSavedMappingsToMappingsInProcess } = useEditMappings(
    savedGlobalSyncConfiguration.mapping
  )

  // **************************************************************
  // *********   Main function : mapper    ************************
  // **************************************************************
  const mapSavedGlobalSyncConfigToConfigInProcess: () => CurrentSyncConfigInProcess =
    useCallback(() => {
      return {
        ...savedGlobalSyncConfiguration,
        exclusionRules: mapSavedExclusionRulesToExclusionRulesInProcess(
          savedGlobalSyncConfiguration.exclusionRules
        ),
        mapping: mapSavedMappingsToMappingsInProcess(
          savedGlobalSyncConfiguration.mapping
        ),
      }
    }, [
      mapSavedExclusionRulesToExclusionRulesInProcess,
      mapSavedMappingsToMappingsInProcess,
      savedGlobalSyncConfiguration,
    ])

  useEffect(() => {
    setCurrentStep(currentStep)
    setCurrentGlobalSyncConfigInProcess(prev => {
      if (!prev) {
        return mapSavedGlobalSyncConfigToConfigInProcess()
      }
      return prev
    })
  }, [
    currentStep,
    mapSavedGlobalSyncConfigToConfigInProcess,
    savedGlobalSyncConfiguration,
    setCurrentGlobalSyncConfigInProcess,
    setCurrentStep,
  ])
}
