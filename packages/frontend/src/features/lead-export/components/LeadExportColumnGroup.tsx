import type { ChangeEvent } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { LeadExportableFieldDTO } from '@getheroes/shared'
import { Divider } from '@getheroes/ui'
import { InputCheckbox } from '@internals/components/common/dataEntry/Checkbox/Checkbox'
import { capitalizeFirstLetter } from '@internals/utils/string'

interface ColumnGroupProps {
  name: string
  fields?: LeadExportableFieldDTO[]
}

export const LeadExportColumnGroup = ({ name, fields }: ColumnGroupProps) => {
  const { t } = useTranslation('export')
  const [selected, setSelected] = useState<string[]>([])

  if (!fields) {
    fields = []
  }

  const handleSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked, name } = e.target

    if (checked) {
      setSelected([...selected, name])
    } else {
      setSelected(selected.filter(item => item !== name))
    }
  }

  const selectAll = () => {
    setSelected(fields.map(f => f.id))
  }

  const deselectAll = () => {
    setSelected([])
  }

  const isAllSelected = selected.length === fields.length

  return (
    <div className="flex flex-col gap-2">
      <InputCheckbox
        isStackingFormfield={false}
        labelClassName="!text-body-m !font-semibold"
        label={name}
        name={`all-${name}`}
        isChecked={isAllSelected}
        onChange={isAllSelected ? deselectAll : selectAll}
        indeterminate={!isAllSelected && selected.length > 0}
      />

      <Divider />

      <div className="columns-2">
        <div className="flex flex-col gap-[0.62rem]">
          {fields.map(({ id }) => (
            <InputCheckbox
              isStackingFormfield={false}
              key={id}
              label={capitalizeFirstLetter(t(id))}
              name={id}
              isChecked={selected.includes(id)}
              onChange={handleSelect}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
