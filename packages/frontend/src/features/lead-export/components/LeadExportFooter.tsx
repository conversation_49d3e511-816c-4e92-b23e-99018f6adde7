import { useTranslation } from 'react-i18next'

import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'

interface LeadExportFooterProps {
  isDisabled: boolean
  isLoading: boolean
}

export const LeadExportFooter = ({
  isDisabled = false,
  isLoading = false,
}: LeadExportFooterProps) => {
  const { t } = useTranslation(['export', 'common'])
  const { toggleExport } = useLeadExport()

  const shouldBeDisabled = isDisabled || isLoading

  return (
    <div className="bg-white shadow-top sticky bottom-0 pr-6 pl-6 pb-6 pt-4 mt-8">
      <div className="flex gap-2 justify-end mx-auto">
        <Button
          onClick={toggleExport}
          variant={ButtonVariant.TERTIARY_OUTLINED}
          size={ButtonSize.SMALL}
        >
          {t('Cancel', { ns: 'common' })}
        </Button>
        <Button
          type="submit"
          size={ButtonSize.SMALL}
          disabled={shouldBeDisabled}
          isLoading={isLoading}
        >
          {t('Export')}
        </Button>
      </div>
    </div>
  )
}
