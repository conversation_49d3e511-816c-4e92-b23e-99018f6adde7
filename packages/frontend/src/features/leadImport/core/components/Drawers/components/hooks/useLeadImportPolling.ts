import { useCallback, useEffect, useRef } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ToastType, useToast } from '@getheroes/frontend/hooks'
import type { LeadImport } from '@getheroes/shared'
import { LeadImportContextEnum, LeadImportStatusEnum } from '@getheroes/shared'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import {
  useLazyGetOneImportQuery,
  useUpdateLeadImportMutation,
} from '@internals/features/leadImport/core/api/leadImportApi'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useImportModuleStepperContext } from '@internals/features/leadImport/core/providers/ImportModuleStepperProvider/useImportModuleStepperContext'
import { useTypedSelector } from '@internals/store/store'

import type { StepsValueType } from '../../../initialStep'

export const useLeadImportPolling = () => {
  const { id: organizationId } =
    useTypedSelector(selectCurrentUserOrganization) || {}

  const { createToast } = useToast()

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const [getLeadImport] = useLazyGetOneImportQuery()
  const [updateLeadImport] = useUpdateLeadImportMutation()

  const {
    currentModule,
    setIsLoading,
    leadImportId,
    resetState,
    setStateLeadImport,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
  } = useImportModuleContext()
  const stepperContext = useImportModuleStepperContext()

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const goToStepReviewSelection = useCallback(
    (leadImport: LeadImport) => {
      let timeout: NodeJS.Timeout
      const nextStepIndex = stepperContext.steps.findIndex(
        step => step.value === stepperContext.currentStep.value
      )
      // leadImportWebsocketRef.current = stepperContext.currentStep.value
      setStateLeadImport({
        leadImportId: leadImport.id,
        status: leadImport.status,
        contextSpecificProps: leadImport.contextSpecificProps,
        specificProps: {
          context: leadImport.contextSpecificProps || {},
          source: leadImport.sourceSpecificProps ?? {},
        },
      })
      if (nextStepIndex !== -1) {
        timeout = setTimeout(() => {
          // Secure all data is ready
          if (stepperContext.steps[nextStepIndex + 1]?.value) {
            stepperContext.goToStep(
              stepperContext.steps[nextStepIndex + 1]?.value
            )
          }
          const filter = [
            {
              field: 'leadImportIds',
              operator: OperatorFilterType.EQUALS,
              value: leadImport.id,
            },
          ]
          setCurrentFiltersApi(filter)
          setCurrentFiltersInProcess(filter)
          setIsLoading(false)
          clearTimeout(timeout)
        }, 1000)
      } else {
        // eslint-disable-next-line no-console
        console.error(
          `[Step not found]: Are you sure that this step is included for the ${currentModule} module that is in useListModule?`,
          {
            previousStep: stepperContext.currentStep,
            availableSteps: stepperContext.steps,
          }
        )
      }
    },
    [
      currentModule,
      setCurrentFiltersApi,
      setCurrentFiltersInProcess,
      setIsLoading,
      setStateLeadImport,
      stepperContext,
    ]
  )

  const goToStepExecuteActionOrFinish = useCallback(
    (leadImport: LeadImport) => {
      // TODO lead-import: Le websocket ne retourne pas le bon status, a cette condition, LEADS_EXCLUDED est égale à SUCCESS
      if (
        ![LeadImportContextEnum.SEQUENCES].includes(leadImport.context) &&
        leadImport.status === LeadImportStatusEnum.LEADS_EXCLUDED
      ) {
        // TODO lead-import: Le websocket retourne de mauvais status, LEADS_EXCLUDED au lieu de SUCCESS, cela sert à corriger en attendant un fix de l'api
        if (leadImportId) {
          updateLeadImport({
            organizationId: organizationId as string,
            leadImportId,
            status: LeadImportStatusEnum.SUCCESS,
          })
        }
        return
      }

      const nextStepIndex = stepperContext.steps.findIndex(
        step => step.value === stepperContext.currentStep.value
      )
      // leadImportWebsocketRef.current = stepperContext.currentStep.value
      if (nextStepIndex !== -1) {
        setStateLeadImport({
          leadImportId: leadImport.id,
          status: leadImport.status,
          contextSpecificProps: leadImport.contextSpecificProps,
          specificProps: {
            context: leadImport.contextSpecificProps || {},
            source: leadImport.sourceSpecificProps || {},
          },
        })
        if (stepperContext.steps[nextStepIndex + 1]?.value) {
          stepperContext.goToStep(
            stepperContext.steps[nextStepIndex + 1]?.value
          )
        }
      } else {
        // eslint-disable-next-line no-console
        console.error(
          `[Step not found]: Are you sure that this step is included for the ${currentModule} module that is in useListModule?`,
          {
            previousStep: stepperContext.currentStep,
            availableSteps: stepperContext.steps,
          }
        )
      }
      setIsLoading(false)
    },
    [
      currentModule,
      leadImportId,
      organizationId,
      setIsLoading,
      setStateLeadImport,
      stepperContext,
      updateLeadImport,
    ]
  )

  const startPolling = useCallback(
    (id: string, step: StepsValueType) => {
      if (!id || !organizationId) {
        return
      }
      intervalRef.current = setInterval(async () => {
        const { data: leadImport } = await getLeadImport({
          organizationId,
          leadImportId: id,
        })
        if (leadImport?.status === LeadImportStatusEnum.IMPORTING) {
          return
        }

        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }

        if (leadImport?.status === LeadImportStatusEnum.ERROR) {
          stepperContext.resetStepperState()
          resetState()
          createToast({
            type: ToastType.ERROR,
            message: 'An error has occurred',
          })
          return
        }

        if (!leadImport) {
          return
        }

        if (step === 'select-leads') {
          goToStepReviewSelection(leadImport)
        }
        if (step === 'review-selections') {
          goToStepExecuteActionOrFinish(leadImport)
        }
      }, 4000)
    },
    [
      organizationId,
      getLeadImport,
      stepperContext,
      resetState,
      createToast,
      goToStepReviewSelection,
      goToStepExecuteActionOrFinish,
    ]
  )

  return {
    startPolling,
  }
}
