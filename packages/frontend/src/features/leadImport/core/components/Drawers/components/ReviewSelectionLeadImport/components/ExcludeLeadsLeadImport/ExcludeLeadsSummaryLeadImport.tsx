import { useTranslation } from 'react-i18next'

import { Card, Divider, Typography } from '@getheroes/ui'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { useLeadImportAllLeadsTable } from '@internals/features/leadImport/core/hooks/useLeadImportAllLeadsTable'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'

const minWidth = 452
const maxWidth = 500

export const ExcludeLeadsSummaryLeadImport = () => {
  const { t } = useTranslation()

  const { currentFiltersApi } = useImportModuleContext()
  const { selections } = useGridContext()

  const { data } = useLeadImportAllLeadsTable({
    skip: currentFiltersApi.length === 0,
  })

  const countSelected = selections.length

  return (
    <div className="flex gap-2 w-full">
      <div style={{ minWidth, maxWidth }}>
        <Card isCondense>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center justify-between w-full mr-3">
              <Typography size={'m'} variant={'body'}>
                <>{t('Leads to import')}</>
              </Typography>
              <div className={'flex items-center'}>
                <Typography size={'m'} variant={'heading'}>
                  <>{data?.meta?.totalItems - countSelected}</>
                </Typography>
                <Typography
                  size={'xs'}
                  variant={'heading'}
                  color={'base-subtle'}
                >
                  <span className={'relative top-1'}>
                    /{data?.meta?.totalItems}
                  </span>
                </Typography>
              </div>
            </div>
            <Divider orientation={'vertical'} />
            <div className="flex items-center  justify-between w-full ml-3">
              <Typography size={'m'} variant={'body'} color={'base-label'}>
                <>{t('Leads to exclude')}</>
              </Typography>

              <Typography size={'m'} variant={'heading'}>
                <>{countSelected}</>
              </Typography>
            </div>
          </div>
        </Card>
      </div>
      {/*<div className="max-w-xs h-full">*/}
      {/*  <Card isCondense>*/}
      {/*    <div className={'flex items-center  h-full gap-2'}>*/}
      {/*      <Typography*/}
      {/*        size={'m'}*/}
      {/*        variant={'heading'}*/}
      {/*        color={'decorative-light-pink'}*/}
      {/*      >*/}
      {/*        {t('{{count}}', {*/}
      {/*          count: 740,*/}
      {/*        })}*/}
      {/*      </Typography>*/}
      {/*      <Typography*/}
      {/*        size={'m'}*/}
      {/*        variant={'body'}*/}
      {/*        color={'decorative-light-pink'}*/}
      {/*      >*/}
      {/*        {t('credits')}*/}
      {/*      </Typography>*/}
      {/*    </div>*/}
      {/*  </Card>*/}
      {/*</div>*/}
    </div>
  )
}
