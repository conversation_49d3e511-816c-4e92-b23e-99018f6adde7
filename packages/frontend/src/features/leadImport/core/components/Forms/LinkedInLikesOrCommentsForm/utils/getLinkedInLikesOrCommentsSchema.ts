import type { TFunction } from 'i18next'
import * as Yup from 'yup'

export const LIKES_MAX_COUNT = 3_000
export const COMMENTS_MAX_COUNT = 1_000

export const getLinkedInLikesOrCommentsSchema = (t: TFunction) =>
  Yup.object({
    linkedInUrl: Yup.string()
      .matches(
        /(?:http(?:s)?:\/\/)(?:(?:www|\w{2})\.)?linkedin\.com\/(posts|feed)\/[\w%-]+/,
        t('Please enter a valid LinkedIn post URL')
      )
      .required(),
    wantLikes: Yup.boolean(),
    likes: Yup.number().when('wantLikes', {
      is: true,
      then: schema =>
        schema
          .required(t('Please specify a number'))
          .min(0, t('Number must be positive'))
          .max(
            LIKES_MAX_COUNT,
            t('Number should be lower', { count: LIKES_MAX_COUNT })
          ),
      otherwise: schema => schema.optional(),
    }),
    wantComments: Yup.boolean(),
    comments: Yup.number().when('wantComments', {
      is: true,
      then: schema =>
        schema
          .required(t('Please specify a number'))
          .min(0, t('Number must be positive'))
          .max(
            COMMENTS_MAX_COUNT,
            t('Number should be lower', {
              count: COMMENTS_MAX_COUNT,
            })
          ),
      otherwise: schema => schema.optional(),
    }),
  }).test(
    'wantLikesOrWantComments',
    t('You must select at least one: Likes or Comments'),
    value =>
      !!(
        (value.wantLikes && value.likes && value.likes > 0) ||
        (value.wantComments && value.comments && value.comments > 0)
      )
  )

export type LinkedInLikesOrCommentsSchemaType = Yup.InferType<
  ReturnType<typeof getLinkedInLikesOrCommentsSchema>
>
