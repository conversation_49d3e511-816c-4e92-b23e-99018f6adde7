import { FlatfileProvider } from '@flatfile/react'
import type { ReactNode } from 'react'

export const FileImporterProvider = ({
  displayAsModal = false,
  children,
}: {
  displayAsModal?: boolean
  children: ReactNode
}) => {
  return (
    <FlatfileProvider
      publishableKey={import.meta.env.VITE_FLATFILE_PUBLIC_KEY}
      config={{
        displayAsModal,
        iframeStyles: {
          height: '100%',
          width: '100%',
        },
        preload: false,
      }}
    >
      {children}
    </FlatfileProvider>
  )
}
