import { clsx } from 'clsx'
import {
  type MouseEvent,
  useCallback,
  useMemo,
  useState,
  useEffect,
} from 'react'
import { useTranslation } from 'react-i18next'

import { SEARCH_FREE_TEXT_PREFIX } from '@getheroes/shared'
import { But<PERSON> } from '@getheroes/ui'
import { FilterItemSelectionTag } from '@internals/features/search/components/FilterItem/components/FilterItemSelectionTag/FilterItemSelectionTag'
import type { ExternalLeadsFilterFormState } from '@internals/features/search/types/searchLeadsType'
import { getFilterNameSubtitle } from '@internals/features/search/utils/filters/getFilterNameSubtitle'
import {
  getFilterTagList,
  getTagIsDisabledExclusion,
} from '@internals/features/search/utils/filters/getFilterTagList'
import { idReferentials } from '@internals/utils/idReferentials'

export type FilterItemTagListItemProps = {
  id: string
  formState: Omit<ExternalLeadsFilterFormState, 'searchText'>
  onRemoveSelection: (props: { value: string; id: string }) => void
  onExcludeSelection: (props: {
    value: string
    isExcluded: boolean
    id: string
  }) => void
  hasSubtitle: boolean
}

export const FilterTagListItem = ({
  id,
  formState,
  onRemoveSelection,
  onExcludeSelection,
  hasSubtitle,
}: FilterItemTagListItemProps) => {
  const { t } = useTranslation('search')
  const [divRef, setDivRef] = useState<HTMLDivElement | null>(null)
  const [sentinelRef, setSentinelRef] = useState<HTMLDivElement | null>(null)
  const [isShowTagList, setIsShowTagList] = useState(false)
  const [itemsOverflow, setItemsOverflow] = useState(false)
  const [hiddenItemsCount, setHiddenItemsCount] = useState(0)

  const isDisabledExclusion = useMemo(() => getTagIsDisabledExclusion(id), [id])
  const subtitle = useMemo(() => getFilterNameSubtitle(id), [id])

  const tagList = getFilterTagList({
    id,
    formState,
    t,
  })

  useEffect(() => {
    setIsShowTagList(false)
  }, [tagList.length])

  useEffect(() => {
    if (!divRef || !sentinelRef || tagList.length === 0) {
      return
    }

    const callback: IntersectionObserverCallback = entries => {
      const [entry] = entries
      const isSentinelVisible = entry.isIntersecting

      setItemsOverflow(!isSentinelVisible)

      if (!isSentinelVisible) {
        const visibleItems = Array.from(divRef.children).filter(
          child =>
            child !== sentinelRef &&
            child.getBoundingClientRect().top <
              divRef.getBoundingClientRect().bottom
        )

        setHiddenItemsCount(tagList.length - visibleItems.length)
      }
    }

    const observerOptions: IntersectionObserverInit = {
      root: divRef,
      threshold: 0.1,
    }

    const observer = new IntersectionObserver(callback, observerOptions)
    observer.observe(sentinelRef)

    return () => {
      observer.disconnect()
    }
  }, [divRef, sentinelRef, tagList])

  const handleClick = useCallback((e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setIsShowTagList(prev => !prev)
  }, [])

  return (
    <>
      {tagList.length > 0 && (
        <div className={'flex flex-col gap-2'}>
          {hasSubtitle && subtitle && (
            <p className={'body-xxs-regular'}>{t(subtitle)}</p>
          )}
          <div className={clsx('flex gap-2 flex-col items-start')}>
            <div
              className={clsx(
                'flex gap-2 flex-wrap overflow-hidden max-w-full',
                {
                  'max-h-28': !isShowTagList,
                }
              )}
              ref={setDivRef}
            >
              {tagList.map(({ value, isExcluded }) => (
                <FilterItemSelectionTag
                  dataTestId={`${idReferentials.search.components.FilterItemContainer.selections}-${id}`}
                  key={value}
                  isExcluded={isExcluded || false}
                  title={value.replace(SEARCH_FREE_TEXT_PREFIX, '')}
                  onClose={() => {
                    onRemoveSelection({ value, id })
                  }}
                  onClick={isExcluded => {
                    onExcludeSelection({ value, isExcluded, id })
                  }}
                  isDisabledExclusion={isDisabledExclusion}
                />
              ))}
              <div ref={setSentinelRef} style={{ height: '1px' }} />
            </div>
            {((itemsOverflow && hiddenItemsCount > 0) || isShowTagList) && (
              <Button
                iconLeft={isShowTagList ? 'FastArrowUp' : 'FastArrowDown'}
                onClick={handleClick}
                size={'extra-small'}
              >
                {isShowTagList
                  ? t('View less')
                  : `${t('View more')} (${hiddenItemsCount})`}
              </Button>
            )}
          </div>
        </div>
      )}
    </>
  )
}
