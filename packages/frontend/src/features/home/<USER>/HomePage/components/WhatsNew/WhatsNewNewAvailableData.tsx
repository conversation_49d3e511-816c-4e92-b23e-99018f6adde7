import { clsx } from 'clsx'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useWindowSize } from 'usehooks-ts'

import { HomeEvents, useTracking } from '@getheroes/frontend/hooks'
import type { Contact } from '@getheroes/shared'
import { Divider, Tag } from '@getheroes/ui'
import { EmptyStateActivity } from '@internals/assets/svg/icons/EmptyStateActivity'
import { ContactTitle } from '@internals/components/business/lead/contact/ContactTitle/ContactTitle'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { Skeleton } from '@internals/components/common/feedback/Skeletons/Skeleton/Skeleton'
import { SkeletonImage } from '@internals/components/common/feedback/Skeletons/SkeletonImage/SkeletonImage'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import {
  MAX_ITEM,
  MIN_ITEM,
  MIN_WINDOW_SIZE,
} from '@internals/features/home/<USER>/HomePage/components/WhatsNew/WhatsNew-export'
import { useWhatsNewNewAvailableData } from '@internals/features/home/<USER>/HomePage/hooks/useWhatsNewNewAvailableData'
import { DrawerNewEnrichmentAvailable } from '@internals/features/lead/components/Enrichment/NewEnrichmentAvailable/DrawerNewEnrichmentAvailable/DrawerNewEnrichmentAvailable'
import { useNewEnrichmentContext } from '@internals/features/lead/components/Enrichment/NewEnrichmentAvailable/Provider/useNewEnrichmentContext'
import { EnrichLeadsModal } from '@internals/features/lead/components/LeadsModals/EnrichLeadsModal/EnrichLeadsModal'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { idReferentials } from '@internals/utils/idReferentials'

export const WhatsNewNewAvailableData = () => {
  const { t } = useTranslation('home')
  const { handleOpenDrawer, isModalOpen, handleCloseModal, afterSubmit } =
    useNewEnrichmentContext()
  const { height = 0 } = useWindowSize()
  const limitPerPage = useMemo(
    () => (height > MIN_WINDOW_SIZE ? MAX_ITEM : MIN_ITEM),
    [height]
  )
  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()
  const { sendEvent } = useTracking()

  const { data, isLoading } = useWhatsNewNewAvailableData({
    limitPerPage,
  })
  const { selections, setSelections, selectedRowsData } = useGridContext()

  const handleSeeNewLeads = () => {
    sendEvent(HomeEvents.HOME_BUTTON_CLICK_NEW_DATA_SEE_LEADS)

    handleOpenDrawer()
  }

  return (
    <div
      className={
        'flex flex-col bg-backgroundDefault rounded-xl p-3 border border-borderSubtle gap-3 w-full'
      }
    >
      <div className={'flex justify-between items-center h-6'}>
        <div className={'label-l'}>{t('New data available')}</div>
        {data?.meta?.totalItems > 0 && (
          <Tag
            dataTestId={idReferentials.home.whatsNew.newAvailableData}
            size={'extra-small'}
            label={data?.meta?.totalItems}
            color={'grey'}
            variant={'outlined'}
          />
        )}
      </div>
      <div
        className={clsx('flex flex-col p-3 rounded-xl min-h-44 h-full', {
          'justify-center items-center border-2 border-dashed':
            data?.meta?.totalItems === 0,
          'bg-backgroundPrimary': data?.meta?.totalItems !== 0,
        })}
      >
        {isLoading ? (
          <div className={'flex flex-col gap-5'}>
            {Array.from({ length: limitPerPage }).map((_, index) => (
              <div key={index}>
                <div className={'flex h-full w-full gap-3'}>
                  <SkeletonImage size={2.3} />
                  <div className={'flex flex-col gap-2 h-full w-full'}>
                    <Skeleton height={0.8} width={26} hasAnimation />
                    <Skeleton height={0.8} width={26} hasAnimation />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : data?.meta?.totalItems === 0 ? (
          <div className={'flex flex-col items-center justify-center'}>
            <EmptyStateActivity style={{ width: 96, height: 64 }} />
            <span className={'heading-xs text-textSubtle text-center'}>
              {t('No New Data Available')}
            </span>
            <span className={'body-xxs-regular text-textSubtle text-center'}>
              {t('Please check back later for updates.')}
            </span>
          </div>
        ) : (
          <div className={'flex flex-col gap-3 h-full'}>
            <div className={'flex flex-col h-full gap-3'}>
              {Object.values(data?.items || {}).map((lead, index) => (
                <div
                  key={(lead as Contact).id}
                  className="flex flex-col truncate flex-shrink gap-3"
                >
                  <ContactTitle
                    contact={lead as Contact}
                    className={'max-w-72'}
                  />
                  {(data?.meta?.totalItems < limitPerPage
                    ? data?.meta?.totalItems
                    : limitPerPage) >
                    index + 1 && <Divider />}
                </div>
              ))}
            </div>

            {data?.meta?.totalItems && (
              <Button
                className={'w-full'}
                dataTestId={idReferentials.home.whatsNew.goToLeadsButton}
                size={ButtonSize.MEDIUM}
                variant={ButtonVariant.TERTIARY_OUTLINED}
                onClick={handleSeeNewLeads}
              >
                {t('See {{count}} leads', {
                  count: data?.meta?.totalItems || 0,
                })}
              </Button>
            )}
          </div>
        )}
      </div>

      <DrawerNewEnrichmentAvailable />
      {isModalOpen && (
        <EnrichLeadsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onClickEnrich={async enrichmentType => {
            // noinspection JSIgnoredPromiseFromCall
            await enrichPhoneOrEmail(
              Object.values(selectedRowsData),
              enrichmentType
            )
            setSelections([])
            afterSubmit()
          }}
          nbContacts={selections.length}
        />
      )}
    </div>
  )
}
