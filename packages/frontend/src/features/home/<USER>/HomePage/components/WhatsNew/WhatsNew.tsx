import { useTranslation } from 'react-i18next'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { WhatsNewHotLeads } from '@internals/features/home/<USER>/HomePage/components/WhatsNew/WhatsNewHotLeads'
import { WhatsNewLatestLeads } from '@internals/features/home/<USER>/HomePage/components/WhatsNew/WhatsNewLatestLeads'
import { WhatsNewNewAvailableData } from '@internals/features/home/<USER>/HomePage/components/WhatsNew/WhatsNewNewAvailableData'
import { NewEnrichmentProvider } from '@internals/features/lead/components/Enrichment/NewEnrichmentAvailable/Provider/NewEnrichmentProvider'
import { useTypedSelector } from '@internals/store/store'

export const WhatsNew = () => {
  const { t } = useTranslation('home')
  const currentUser = useTypedSelector(selectCurrentUser)
  return (
    <GridProvider>
      <NewEnrichmentProvider>
        <div className={'flex flex-col gap-4'}>
          <div className={'heading-s'}>
            {`${t("What's new")}, ${currentUser?.firstName}?`}
          </div>
          <div className={'flex w-full gap-3 justify-between'}>
            <WhatsNewLatestLeads />
            <WhatsNewHotLeads />
            <WhatsNewNewAvailableData />
          </div>
        </div>
      </NewEnrichmentProvider>
    </GridProvider>
  )
}
