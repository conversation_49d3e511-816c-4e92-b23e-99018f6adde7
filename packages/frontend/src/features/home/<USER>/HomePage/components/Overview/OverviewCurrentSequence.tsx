import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useTracking, HomeEvents } from '@getheroes/frontend/hooks'
import type { Organization } from '@getheroes/shared'
import { Button } from '@getheroes/ui'
import { OverviewMetric } from '@internals/features/home/<USER>/HomePage/components/Overview/OverviewMetric'
import { useCountSequencesQuery } from '@internals/features/sequence/api/sequenceApi'
import { SequenceStatus } from '@internals/features/sequence/types/sequence'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

export const OverviewCurrentSequence = () => {
  const { t } = useTranslation('home')
  const navigate = useNavigate()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { sendEvent } = useTracking()

  const { data, isLoading } = useCountSequencesQuery(
    {
      organizationId,
      statuses: [SequenceStatus.PAUSE, SequenceStatus.IN_PROGRESS],
    },
    {
      refetchOnMountOrArgChange: true,
    }
  )

  const sequenceData = [
    { status: SequenceStatus.IN_PROGRESS, label: t('Sequence Active') },
    { status: SequenceStatus.PAUSE, label: t('Sequence Pause') },
  ]

  const handleGoToSequences = () => {
    sendEvent(HomeEvents.HOME_BUTTON_CLICK_GO_TO_SEQUENCES)

    navigate(privateRoutes.sequences.path)
  }

  return (
    <div
      className={
        'flex flex-col bg-backgroundDefault rounded-xl p-3 border border-borderSubtle gap-3 w-full'
      }
    >
      <div className={'flex justify-between items-center'}>
        <div className={'label-l'}>{t('Current sequences')}</div>
        <Button
          dataTestId={idReferentials.home.overview.currentSequenceButton}
          size={'small'}
          variant={'tertiary-outlined'}
          iconRight={'NavArrowRight'}
          onClick={handleGoToSequences}
        >
          {t('Go to sequences')}
        </Button>
      </div>
      <div className={'flex gap-2'}>
        {sequenceData.map(({ status, label }) => (
          <div
            key={status}
            className={
              'flex flex-col justify-between gap-3 w-full first:border-r'
            }
          >
            <div className={'body-xxs-medium text-textSubtle'}>{label}</div>
            <OverviewMetric isLoading={isLoading}>
              {(data as Record<SequenceStatus, number>)?.[status] || 0}
            </OverviewMetric>
          </div>
        ))}
      </div>
    </div>
  )
}
