import { useTranslation } from 'react-i18next'

import { OverviewCurrentSequence } from '@internals/features/home/<USER>/HomePage/components/Overview/OverviewCurrentSequence'
import { OverviewDailyTasks } from '@internals/features/home/<USER>/HomePage/components/Overview/OverviewDailyTasks'
import { OverviewWeeklySuccess } from '@internals/features/home/<USER>/HomePage/components/Overview/OverviewWeeklySuccess'

export const Overview = () => {
  const { t } = useTranslation('home')
  return (
    <div className={'flex flex-col gap-4'}>
      <div className={'heading-s'}>{t('Overview')}</div>
      <div className={'flex w-full gap-3'}>
        <OverviewDailyTasks />
        <OverviewCurrentSequence />
        <OverviewWeeklySuccess />
      </div>
    </div>
  )
}
