import { clsx } from 'clsx'
import { InfoCircle } from 'iconoir-react'
import { useTranslation } from 'react-i18next'

import { Button, Typography } from '@getheroes/ui'
import { useCommonPageTracking } from '@internals/hooks/useCommonPageTracking'
import type { GenericResponseError } from '@internals/models/errors'
import { idReferentials } from '@internals/utils/idReferentials'

type ErrorPageProps = {
  error?: GenericResponseError
  showReloadButton?: boolean
}

export const ErrorPage: (props: ErrorPageProps) => JSX.Element = ({
  error,
  showReloadButton = true,
}) => {
  const { trackErrorPageReloaded } = useCommonPageTracking()
  const { t } = useTranslation('common')

  const handleClickReload = async () => {
    if (error) {
      await trackErrorPageReloaded({ error })
    }
    window.location.reload()
  }

  return (
    <div
      className={clsx(
        'page-error w-full h-full p-4 flex flex-col items-center justify-center gap-4'
      )}
    >
      <InfoCircle height={54} width={54} color={'var(--textBrand)'} />
      <Typography variant={'heading'} size={'m'}>
        {t('Something went wrong')}
      </Typography>
      <Typography>{t('Try reloading the page')}</Typography>
      {showReloadButton && (
        <Button
          variant={'primary'}
          size={'large'}
          onClick={handleClickReload}
          dataTestId={idReferentials.components.errorPage.reloadButton}
        >
          {t('Reload page')}
        </Button>
      )}
    </div>
  )
}
