export type CreateOrganizationPayload = {
  name: string
  automaticDomainJoin: boolean
  countryCode: string
  zipcode?: string
  website?: string
  referrerId?: string
}
export type EditOrganizationPayload = {
  organizationId: string
  name: string
  website: string
  slug: string
  automaticDomainJoin: boolean
}

export type GetUsersAssignationsPayload = {
  organizationId: string
  meta?: boolean
}

export type GetUsersAssignationsResponse = Array<Assignation>

export type DeleteOrganizationMemberPayload = {
  organizationId: string
  memberId: string
}
export type DeleteOrganizationInvitationPayload = {
  organizationId: string
  invitationId: string
}
export type EditOrganizationMemberPayload = {
  organizationId: string
  role: string
  memberId: string
}
export type InviteOrganizationMemberPayload = {
  body: [
    {
      role: string
      email: string
    },
  ]
  params: {
    organizationId: string
  }
}

export type RemindInvitationMemberPayload = {
  organizationId: string
  invitationId: string
}
