// #######################################################################################
// #######################################################################################
// Usage: node contacts-update-external_ids.js
// Warning: just a quick script to update external_id in contacts table
// and elastic initially for a specific hubspot migration
// #######################################################################################
// #######################################################################################

const { Client } = require('pg')
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0
process.removeAllListeners('warning')

const data = require('./contact_to_update.json')
//example
/*
[
  {
    id: '67b41d2c-ca9b-4b31-92e1-8d83b12335c3',
    external_id: '1234'
  }
]
*/

const client = new Client({
  host: 'localhost',
  port: 5433,
  database: 'zeliq-XXX',
  user: 'zeliq_XXX',
  password: 'XXX',
})

const elasticUrl = 'https://localhost:9202'
const elasticUsername = 'opensearch-user-XXX'
const elasticPassword = 'XXX'
const organizationId = 'c59e45a9-2e0f-4b08-97fa-0a7f4bcb365c'

const elasticContactIndex = `contacts_${organizationId}`
async function run() {
  await client.connect()

  let totalUpdatedPostgres = 0
  let totalUpdatedElastic = 0
  for (let i = 0; i < data.length; i++) {
    const datum = data[i]
    const id = datum.id
    let external_id = datum.external_id || null
    if (external_id) {
      external_id = `'${external_id}'`
    }
    try {
      let res
      if (!external_id) {
        res = await client.query(
          `update contacts set external_id=${external_id} where id = '${id}' and organization_id = '${organizationId}'`
        )
      } else {
        res = await client.query(
          `update contacts set external_id=${external_id}, source='CRM' where id = '${id}' and organization_id = '${organizationId}'`
        )
      }

      totalUpdatedPostgres += res?.rowCount || 0
    } catch (e) {}

    try {
      const body = {
        doc: {
          externalId: datum.external_id + '' || null,
        },
      }

      if (external_id) {
        body.doc.source = 'CRM'
      }

      const elasticRes = await fetch(
        `${elasticUrl}/${elasticContactIndex}/_update/${id}`,
        {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Basic ${Buffer.from(
              `${elasticUsername}:${elasticPassword}`
            ).toString('base64')}`,
          },
          body: JSON.stringify(body),
        }
      )
      const elasticResJson = await elasticRes.json()
      totalUpdatedElastic += elasticResJson?.result === 'updated' ? 1 : 0
    } catch (e) {
      console.log(e)
    }
    console.log(`${i}/${data.length}`)
  }

  console.log(`total updated: ${totalUpdatedPostgres}`)
  console.log(`total updated elastic: ${totalUpdatedElastic}`)
  await client.end()
}

run().catch(console.error)
