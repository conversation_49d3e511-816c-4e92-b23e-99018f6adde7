import { config } from 'dotenv'
import { LinearClient } from '@linear/sdk'
import _ from 'lodash'

config({ path: '../.env' })

const linearApiKey = process.env.LINEAR_API_KEY
const linearLabelReleaseInProgressId = process.env.LINEAR_RELEASE_IN_PROGRESS_ID
const linearLabelReleaseInDeployId = process.env.LINEAR_RELEASE_IN_DEPLOY_ID
const linearLabelReleaseInDeployLabel =
  process.env.LINEAR_RELEASE_IN_DEPLOY_LABEL
const linearLabelReleaseInProgressLabel =
  process.env.LINEAR_RELEASE_IN_PROGRESS_LABEL

const linearLabelCurrentReleaseId = process.env.LINEAR_CURRENT_RELEASE_ID
const linearLabelStagingId = process.env.LINEAR_STAGING_ID
const linearLabelProdId = process.env.LINEAR_PROD_ID
const linearLabelProd = process.env.LINEAR_PROD_LABEL

const linearLabelNewReleaseLabel = process.env.LINEAR_NEW_RELEASE_LABEL
const linearLabelNewReleaseId = process.env.LINEAR_NEW_RELEASE_ID
const linearLabelNewMepLabel = process.env.LINEAR_NEW_MEP_LABEL
const linearLabelNewMepId = process.env.LINEAR_NEW_MEP_ID
const linearLabelPreviousMepLabel = process.env.LINEAR_PREVIOUS_MEP_LABEL
const linearLabelPreviousMepId = process.env.LINEAR_PREVIOUS_MEP_ID

const linearClient = new LinearClient({ apiKey: linearApiKey })

async function getIssues(after) {
  let afterQuery = ''
  if (after) {
    afterQuery = `after: "${after}"`
  }

  const issues = await linearClient.client.request(
    `
        query ExampleQuery {
          issues(orderBy: updatedAt, filter: {
             labels: {
                and: [
                  { id: { eq: "${linearLabelNewReleaseId}" } }
                ]
            },
          }
            first: 50
            ${afterQuery}
          ) {
          pageInfo {
                    endCursor
                    hasNextPage
                  }
            edges {

              node {
                id
                title
                estimate
                identifier
                labelIds
                state {
                  name
                }
                team {
                    name
                    }
                updatedAt
                history(orderBy: updatedAt) {
                  pageInfo {
                    hasNextPage
                  }
                  nodes {
                    toState {
                      name
                    }
                    updatedAt
                  }
                }
              }
            }
          }
        }
    `,
    {
      orderBy: 'updatedAt',
      historyOrderBy2: 'updatedAt',
    }
  )

  return issues
}

// Fetch linear issues
export const addLabelReleaseInDeploy = async () => {
  let edges = []
  let hasNextPage = true
  let endCursor = null
  let page = 0
  while (hasNextPage) {
    const pageIssues = await getIssues(endCursor)
    edges = edges.concat(pageIssues?.issues?.edges || [])
    hasNextPage = pageIssues?.issues?.pageInfo?.hasNextPage
    endCursor = pageIssues?.issues?.pageInfo?.endCursor
    console.log(`page ${page} - ${hasNextPage}`)
    page++
    if (page > 10) {
      break
    }
  }

  for (let edge of edges) {
    const issue = edge.node
    try {
      const linearIssue = await linearClient.issue(issue.id)

      const releaseIndex = issue.labelIds.indexOf(linearLabelNewReleaseId)
      if (releaseIndex !== -1) {
        issue.labelIds.splice(releaseIndex, 1)
      }

      await linearIssue.update({
        labelIds: _.uniq([...issue.labelIds, linearLabelNewMepId]),
      })
      console.log(
        `Issue ${linearIssue.title} updated with new label ${linearLabelNewMepLabel}.`
      )
    } catch (error) {
      console.error(`Failed to update issue ${issue.title}: ${error}`)
    }
  }
}
